<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .debug-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .captcha-demo {
            border: 2px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
    <!-- 加载验证码脚本 -->
    <script src="https://recaptcha.net/recaptcha/api.js?render=explicit" async defer></script>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js?compat=recaptcha" async defer></script>
</head>
<body>
    <h1>🔍 验证码问题调试</h1>
    
    <div class="debug-section">
        <h3>❓ 问题描述</h3>
        <div class="error">
            <strong>问题:</strong> 修改密码时 type=cloudflare，但显示的是谷歌验证码的图片<br>
            <strong>预期:</strong> type=cloudflare 应该显示 CloudFlare Turnstile 验证码<br>
            <strong>实际:</strong> 显示了 Google reCAPTCHA 的界面
        </div>
    </div>

    <div class="debug-section">
        <h3>🔧 Site Key 配置检查</h3>
        <div class="code">开发环境 Site Key 配置:
Google reCAPTCHA: 6Le1WVAqAAAAAOXxgAbk-GpkDrSzeta_at5f_fDh
CloudFlare Turnstile: 0x4AAAAAABgkoiuUbu_9cxnv

生产环境 Site Key 配置:
Google reCAPTCHA: 6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_
CloudFlare Turnstile: 0x4AAAAAABggGCrS6o4FSYCK</div>
        
        <button onclick="validateSiteKeys()">验证 Site Key 格式</button>
        <div id="sitekey-result"></div>
    </div>

    <div class="debug-section">
        <h3>🧪 验证码渲染测试</h3>
        <div class="info">
            测试不同 Site Key 的渲染效果
        </div>
        
        <h4>Google reCAPTCHA 测试</h4>
        <button onclick="renderGoogleCaptcha()">渲染 Google reCAPTCHA</button>
        <div id="google-captcha-demo" class="captcha-demo"></div>
        
        <h4>CloudFlare Turnstile 测试</h4>
        <button onclick="renderCloudflareCaptcha()">渲染 CloudFlare Turnstile</button>
        <div id="cloudflare-captcha-demo" class="captcha-demo"></div>
        
        <div id="render-result"></div>
    </div>

    <div class="debug-section">
        <h3>🔍 脚本加载检查</h3>
        <button onclick="checkScriptLoading()">检查脚本加载状态</button>
        <div id="script-status"></div>
    </div>

    <div class="debug-section">
        <h3>💡 可能的解决方案</h3>
        <div class="warning">
            <h4>可能的原因和解决方案:</h4>
            <ol>
                <li><strong>Site Key 格式问题:</strong> CloudFlare Site Key 格式可能不正确</li>
                <li><strong>脚本加载顺序:</strong> Google reCAPTCHA 脚本可能覆盖了 CloudFlare 脚本</li>
                <li><strong>API 兼容性:</strong> CloudFlare 兼容模式可能有问题</li>
                <li><strong>缓存问题:</strong> 浏览器缓存了旧的验证码</li>
            </ol>
        </div>
        
        <button onclick="showSolutions()">显示解决方案</button>
        <div id="solutions"></div>
    </div>

    <div class="debug-section">
        <h3>🛠️ 修复建议</h3>
        <div id="fix-suggestions">
            <div class="info">
                点击下面的按钮获取具体的修复建议
            </div>
        </div>
        <button onclick="generateFixSuggestions()">生成修复建议</button>
    </div>

    <script>
        // Site Key 配置
        const SITE_KEYS = {
            google_dev: '6Le1WVAqAAAAAOXxgAbk-GpkDrSzeta_at5f_fDh',
            google_prod: '6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_',
            cloudflare_dev: '0x4AAAAAABgkoiuUbu_9cxnv',
            cloudflare_prod: '0x4AAAAAABggGCrS6o4FSYCK'
        };

        function validateSiteKeys() {
            const result = document.getElementById('sitekey-result');
            let html = '<div class="status info"><h4>Site Key 验证结果:</h4>';
            
            // Google Site Key 格式验证 (应该以 6L 开头)
            Object.keys(SITE_KEYS).forEach(key => {
                const siteKey = SITE_KEYS[key];
                const isGoogle = key.includes('google');
                const isCloudflare = key.includes('cloudflare');
                
                if (isGoogle && siteKey.startsWith('6L')) {
                    html += `<p>✅ ${key}: ${siteKey} (格式正确)</p>`;
                } else if (isCloudflare && siteKey.startsWith('0x4')) {
                    html += `<p>✅ ${key}: ${siteKey} (格式正确)</p>`;
                } else {
                    html += `<p>❌ ${key}: ${siteKey} (格式可能有问题)</p>`;
                }
            });
            
            html += '</div>';
            result.innerHTML = html;
        }

        function renderGoogleCaptcha() {
            const container = document.getElementById('google-captcha-demo');
            container.innerHTML = '<p>正在渲染 Google reCAPTCHA...</p>';
            
            setTimeout(() => {
                if (window.grecaptcha && window.grecaptcha.render) {
                    try {
                        window.grecaptcha.render(container, {
                            sitekey: SITE_KEYS.google_dev,
                            callback: (token) => {
                                console.log('Google reCAPTCHA token:', token);
                            }
                        });
                        updateRenderResult('Google reCAPTCHA 渲染成功', 'success');
                    } catch (error) {
                        container.innerHTML = '<p class="error">Google reCAPTCHA 渲染失败: ' + error.message + '</p>';
                        updateRenderResult('Google reCAPTCHA 渲染失败: ' + error.message, 'error');
                    }
                } else {
                    container.innerHTML = '<p class="error">grecaptcha 未加载</p>';
                    updateRenderResult('grecaptcha 脚本未加载', 'error');
                }
            }, 1000);
        }

        function renderCloudflareCaptcha() {
            const container = document.getElementById('cloudflare-captcha-demo');
            container.innerHTML = '<p>正在渲染 CloudFlare Turnstile...</p>';
            
            setTimeout(() => {
                if (window.grecaptcha && window.grecaptcha.render) {
                    try {
                        window.grecaptcha.render(container, {
                            sitekey: SITE_KEYS.cloudflare_dev,
                            callback: (token) => {
                                console.log('CloudFlare Turnstile token:', token);
                            }
                        });
                        updateRenderResult('CloudFlare Turnstile 渲染成功', 'success');
                    } catch (error) {
                        container.innerHTML = '<p class="error">CloudFlare Turnstile 渲染失败: ' + error.message + '</p>';
                        updateRenderResult('CloudFlare Turnstile 渲染失败: ' + error.message, 'error');
                    }
                } else {
                    container.innerHTML = '<p class="error">grecaptcha 未加载</p>';
                    updateRenderResult('grecaptcha 脚本未加载', 'error');
                }
            }, 1000);
        }

        function updateRenderResult(message, type) {
            const result = document.getElementById('render-result');
            result.innerHTML = `<div class="status ${type}"><p>${message}</p></div>`;
        }

        function checkScriptLoading() {
            const status = document.getElementById('script-status');
            let html = '<div class="status info"><h4>脚本加载状态:</h4>';
            
            // 检查 grecaptcha 对象
            if (typeof window.grecaptcha !== 'undefined') {
                html += '<p>✅ window.grecaptcha 已加载</p>';
                
                if (typeof window.grecaptcha.render === 'function') {
                    html += '<p>✅ grecaptcha.render 方法可用</p>';
                } else {
                    html += '<p>❌ grecaptcha.render 方法不可用</p>';
                }
            } else {
                html += '<p>❌ window.grecaptcha 未加载</p>';
            }
            
            // 检查脚本标签
            const scripts = document.querySelectorAll('script[src*="recaptcha"], script[src*="turnstile"]');
            html += `<p>📊 找到 ${scripts.length} 个验证码脚本标签</p>`;
            
            scripts.forEach((script, index) => {
                html += `<p>📄 脚本 ${index + 1}: ${script.src}</p>`;
            });
            
            html += '</div>';
            status.innerHTML = html;
        }

        function showSolutions() {
            const solutions = document.getElementById('solutions');
            solutions.innerHTML = `
                <div class="status warning">
                    <h4>🔧 解决方案:</h4>
                    <ol>
                        <li><strong>检查 Site Key:</strong> 确认 CloudFlare Site Key 格式正确 (0x4开头)</li>
                        <li><strong>清除缓存:</strong> 清除浏览器缓存和 localStorage</li>
                        <li><strong>检查脚本顺序:</strong> 确保 CloudFlare 脚本在 Google 脚本之后加载</li>
                        <li><strong>使用不同的渲染方法:</strong> 为不同验证码类型使用不同的渲染逻辑</li>
                        <li><strong>添加调试日志:</strong> 在验证码渲染时添加 console.log 查看实际使用的 Site Key</li>
                    </ol>
                </div>
            `;
        }

        function generateFixSuggestions() {
            const suggestions = document.getElementById('fix-suggestions');
            suggestions.innerHTML = `
                <div class="status success">
                    <h4>🛠️ 具体修复建议:</h4>
                    
                    <h5>1. 修改 GoogleRecaptcha.vue 组件:</h5>
                    <div class="code">// 添加调试日志
mounted() {
  console.log('验证码类型:', this.captchaType);
  console.log('使用的 Site Key:', this.sitekey);
  this.loaded();
}</div>

                    <h5>2. 检查环境变量:</h5>
                    <div class="code">// 确认开发环境的 CloudFlare Site Key
VUE_APP_CLOUD_FLARE_SITE_KEY = '0x4AAAAAABgkoiuUbu_9cxnv'</div>

                    <h5>3. 添加验证码类型检查:</h5>
                    <div class="code">// 在渲染前检查 Site Key 格式
if (this.captchaType === 'cloudflare' && !this.sitekey.startsWith('0x4')) {
  console.error('CloudFlare Site Key 格式错误:', this.sitekey);
}</div>

                    <h5>4. 清除缓存:</h5>
                    <p>• 按 Ctrl+Shift+R 强制刷新页面</p>
                    <p>• 清除浏览器缓存和 localStorage</p>
                    <p>• 在开发者工具中禁用缓存</p>
                </div>
            `;
        }

        // 页面加载时自动检查
        window.onload = function() {
            console.log('🔍 验证码调试页面已加载');
            setTimeout(() => {
                checkScriptLoading();
                validateSiteKeys();
            }, 2000);
        };
    </script>
</body>
</html>
