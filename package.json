{"name": "myfx-co", "version": "1.0.1", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development ", "test": "vue-cli-service serve --mode test", "prod": "vue-cli-service serve --mode production", "build:test": "vue-cli-service build --report --mode development ", "build:prod": "vue-cli-service build --report --mode production ", "lint": "vue-cli-service lint", "i18n:report": "vue-cli-service i18n:report --src './src/**/*.?(js|vue)' --locales './src/locales/**/*.json'"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.14.0", "algoliasearch": "^3.35.1", "apexcharts": "^3.26.1", "axios": "^0.21.1", "babel-plugin-component": "^1.1.1", "bootstrap-vue": "^2.1.0", "carddragger": "^0.3.6", "core-js": "^3.6.5", "counterup2": "^1.0.4", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "el-phone-number-input": "^1.1.12", "element-ui": "^2.15.14", "html2pdf.js": "^0.10.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "moment": "^2.27.0", "node-snackbar": "^0.1.16", "raphael": "latest", "register-service-worker": "^1.7.1", "smooth-scrollbar": "^8.5.2", "url-loader": "^3.0.0", "vee-validate": "^3.3.9", "vue": "^2.6.11", "vue-chartist": "^2.3.1", "vue-i18n": "^8.21.0", "vue-instantsearch": "^2.7.1", "vue-phone-number-input": "^1.12.13", "vue-router": "^3.2.0", "vue-slider-component": "3.0.32", "vuex": "^3.4.0", "waypoints": "^4.0.1", "webpack": "^4.44.1", "wot-design": "^2.4.1"}, "devDependencies": {"@babel/polyfill": "^7.10.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "bootstrap": "5.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.12.2", "less-loader": "^5.0.0", "lint-staged": "^9.5.0", "mutationobserver-shim": "^0.3.7", "node-sass": "^4.14.1", "popper.js": "^1.16.0", "portal-vue": "^2.1.6", "sass": "1.32.11", "sass-loader": "8.0.2", "stylus": "^0.54.8", "stylus-loader": "^3.0.2", "vue-cli-plugin-bootstrap-vue": "~0.6.0", "vue-cli-plugin-i18n": "~0.6.1", "vue-loader": "^15.9.3", "vue-template-compiler": "^2.6.11"}}