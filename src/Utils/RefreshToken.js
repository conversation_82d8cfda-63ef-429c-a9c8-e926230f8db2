import axios from 'axios'
import constant from '@/config/constant'
import { getLocal, removeLocalItem, setLocal } from '@/Utils/authLocalStorage'

const request = axios.create(
  {
    baseURL: window.location.origin.includes('myfx.group') ? constant.indonesiaBaseURL : constant.webBaseURL,
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
    },
    timeout: 50000
  }
)

export async function resetToken () {
  let myguid = JSON.parse(getLocal('user')).myguid
  await request({
    url: '/resetToken',
    method: 'get',
    headers: {
      'refreshToken': getLocal('refresh_token'),
    },
    params: {
      myguid: myguid
    }
  }).then(res => {
    setLocal('access_token', res.data.data.accessToken, 0.5)
    removeLocalItem('refresh_token')
    setLocal('refresh_token', res.data.data.refreshToken, 1)
    let user = getLocal('user')
    setLocal('user', user, 1)
  })
}
