// 获取该co可用的入金通道
import { getLocal } from '@/Utils/authLocalStorage'
import i18n from '../i18n.js'
import xcoinsArray from '@/Utils/XcoinsAvailableCountry.json'

export function getCoEnabledDepositWayList (dataList) {
  const depositWay = [
    {
      depositMethods: i18n.t('transaction.deposit.jpbank.depositMethods'),
      dataBaseName: 'jpbank',
      status: '',
      timeToFund: i18n.t('transaction.deposit.jpbank.timeToFund'),
      fee: i18n.t('transaction.deposit.jpbank.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.bitwallet.depositMethods'),
      dataBaseName: 'mybitwallet',
      status: '',
      timeToFund: i18n.t('transaction.deposit.bitwallet.timeToFund'),
      fee: i18n.t('transaction.deposit.bitwallet.fee'),
      maintenance: false,
      enabled: true
    },

    {
      depositMethods: i18n.t('transaction.deposit.bank_wire.depositMethods'),
      dataBaseName: 'none',
      status: '',
      timeToFund: i18n.t('transaction.deposit.bank_wire.timeToFund'),
      fee: i18n.t('transaction.deposit.bank_wire.fee'),
      maintenance: false,
      enabled: true
    },
    {
      depositMethods: i18n.t('transaction.deposit.crpto.depositMethods'),
      status: '',
      dataBaseName: 'usdt',
      timeToFund: i18n.t('transaction.deposit.crpto.timeToFund'),
      fee: i18n.t('transaction.deposit.crpto.fee'),
      maintenance: false,
      enabled: true
    },
    {
      depositMethods: i18n.t('transaction.deposit.credit_card.depositMethods'),
      status: '',
      dataBaseName: 'pcins',
      timeToFund: i18n.t('transaction.deposit.credit_card.timeToFund'),
      fee: i18n.t('transaction.deposit.credit_card.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.awe_pay.myr'),
      dataBaseName: 'awepay_myr_p2p',
      status: '',
      timeToFund: i18n.t('transaction.deposit.awe_pay.timeToFund'),
      fee: i18n.t('transaction.deposit.awe_pay.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.awe_pay.thb'),
      dataBaseName: 'awepay_thb_p2p',
      status: '',
      timeToFund: i18n.t('transaction.deposit.awe_pay.timeToFund'),
      fee: i18n.t('transaction.deposit.awe_pay.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.awe_pay.vnd'),
      dataBaseName: 'awepay_vnd_p2p',
      status: '',
      timeToFund: i18n.t('transaction.deposit.awe_pay.timeToFund'),
      fee: i18n.t('transaction.deposit.awe_pay.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.rmb_funding.depositMethods'),
      dataBaseName: 'crypto356',
      status: '',
      timeToFund: i18n.t('transaction.deposit.awe_pay.timeToFund'),
      fee: i18n.t('transaction.deposit.awe_pay.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.rmb_funding2.depositMethods1'),
      dataBaseName: 'myPay-CNY',
      status: '',
      timeToFund: i18n.t('transaction.deposit.rmb_funding2.timeToFund'),
      fee: i18n.t('transaction.deposit.rmb_funding2.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.rmb_funding2.depositMethods2'),
      dataBaseName: 'myPay-THB',
      status: '',
      timeToFund: i18n.t('transaction.deposit.rmb_funding2.timeToFund'),
      fee: i18n.t('transaction.deposit.rmb_funding2.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.rmb_funding2.depositMethods3'),
      dataBaseName: 'myPay-VND',
      status: '',
      timeToFund: i18n.t('transaction.deposit.rmb_funding2.timeToFund'),
      fee: i18n.t('transaction.deposit.rmb_funding2.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.rmb_funding2.depositMethods5'),
      dataBaseName: 'myPay-IDR',
      status: '',
      timeToFund: i18n.t('transaction.deposit.rmb_funding2.timeToFund'),
      fee: i18n.t('transaction.deposit.rmb_funding2.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.rmb_funding2.depositMethods4'),
      dataBaseName: 'myPay-INR',
      status: '',
      timeToFund: i18n.t('transaction.deposit.rmb_funding2.timeToFund'),
      fee: i18n.t('transaction.deposit.rmb_funding2.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.xCoins.depositMethods1'),
      dataBaseName: 'xCoins',
      status: '',
      timeToFund: i18n.t('transaction.deposit.xCoins.timeToFund'),
      fee: i18n.t('transaction.deposit.xCoins.fee'),
      maintenance: false,
      enabled: false,
      type: 1
    },
    {
      depositMethods: i18n.t('transaction.deposit.xCoins.depositMethods2'),
      dataBaseName: 'xCoins',
      status: '',
      timeToFund: i18n.t('transaction.deposit.xCoins.timeToFund'),
      fee: i18n.t('transaction.deposit.xCoins.fee'),
      maintenance: false,
      enabled: false,
      type: 2
    },
    {
      depositMethods: i18n.t('transaction.deposit.xCoins.depositMethods3'),
      dataBaseName: 'xCoins',
      status: '',
      timeToFund: i18n.t('transaction.deposit.xCoins.timeToFund'),
      fee: i18n.t('transaction.deposit.xCoins.fee'),
      maintenance: false,
      enabled: false,
      type: 3
    },
    {
      depositMethods: i18n.t('transaction.deposit.MybanQ.depositMethods'),
      dataBaseName: 'mybanq_deposit',
      status: '',
      timeToFund: i18n.t('transaction.deposit.MybanQ.timeToFund'),
      fee: i18n.t('transaction.deposit.MybanQ.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t('transaction.deposit.grandPay.depositMethods'),
      dataBaseName: 'grandPay',
      status: '',
      timeToFund: i18n.t('transaction.deposit.grandPay.timeToFund'),
      fee: i18n.t('transaction.deposit.grandPay.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t(`transaction.deposit.monetix.${JSON.parse(getLocal('user'))?.country || JSON.parse(getLocal('user'))?.nationality}`),
      dataBaseName: 'monetix',
      status: '',
      timeToFund: i18n.t('transaction.deposit.monetix.timeToFund'),
      fee: i18n.t('transaction.deposit.monetix.fee'),
      maintenance: false,
      enabled: false
    },
    {
      depositMethods: i18n.t(`transaction.deposit.monetix.MYR`),
      dataBaseName: 'monetix-MYR',
      status: '',
      timeToFund: i18n.t('transaction.deposit.monetix.timeToFund'),
      fee: i18n.t('transaction.deposit.monetix.fee'),
      maintenance: false,
      enabled: false
    }
  ]

  const usefulDepositData = [{
    depositMethods: i18n.t('transaction.deposit.bank_wire.depositMethods'),
    dataBaseName: 'none',
    status: i18n.t('transaction.deposit.bank_wire.status'),
    timeToFund: i18n.t('transaction.deposit.bank_wire.timeToFund'),
    fee: i18n.t('transaction.deposit.bank_wire.fee'),
    enabled: true
  }]
  const userInfo = JSON.parse(getLocal('user'))
  const country = userInfo.country
  const nationality = userInfo.nationality
  const isMauritius = userInfo.license === 'Mauritius'

  const result = {
    depositMethods: '',
    status: '',
    timeToFund: '',
    fee: '',
    enabled: false
  }
  let hasAwepay = false

  // xCoins可用邮箱
  // let availableEmail = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
  const availableEmail = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
  const currentEmail = JSON.parse(getLocal('user')).email
  const myPayVisibleCountry = ['Indonesia', 'India', 'Malaysia', 'Thailand', 'Vietnam']

  dataList.forEach(item => {
    const temp = depositWay.filter(res => {
      if(item.ppsName === 'xCoins') {
        return item.ppsName.includes(res.dataBaseName)
      } else {
        return item.ppsName === res.dataBaseName
      }
    })
    const way = temp[0]

    if (temp.length === 1) {
      if (item.ppsName === 'jpbank' && country.includes('Japan')) {
        way.enabled = true
      }

      if (item.ppsName === 'pcins' && nationality.includes('Japan')) {
        way.enabled = true
      }

      if (item.ppsName.includes('awepay_myr_p2p') && myPayVisibleCountry.includes(country)) {
        way.enabled = true
      }

      if (item.ppsName.includes('awepay_thb_p2p') && country.includes('Thai')) {
        way.enabled = true
      }

      if (item.ppsName.includes('awepay_vnd_p2p') && country.includes('Vietnam')) {
        way.enabled = true
      }

      if (item.ppsName === 'crypto356' && country.includes('China')) {
        way.enabled = true
      }

      if (item.ppsName === 'xCoins' && availableEmail.includes(currentEmail)) {
        way.enabled = true
      }

      if (item.ppsName === 'mybanq_deposit' && country.includes('Japan')) {
        way.enabled = true
      }

      if (item.ppsName === 'grandPay' && (country.includes('Japan') || nationality.includes('Japan'))) {
        way.enabled = true
      }

      if (item.ppsName === 'monetix' && (country.includes('Thailand') || nationality.includes('Thailand') || country.includes('Philippines'))) {
        way.enabled = true
      }

      if (item.ppsName === 'monetix-MYR' && myPayVisibleCountry.includes(country)) {
        way.enabled = true
      }

      if (isMauritius) {
        way.enabled = false
      }

      if (item.ppsName === 'none') {
        way.enabled = true
      }

      if (item.ppsName.startsWith('myPay-') && myPayVisibleCountry.includes((country))) {
        way.enabled = true
      } else {
        if (item.ppsName === 'myPay-CNY' && country.includes('China')) {
          way.enabled = true
        }

        if (item.ppsName === 'myPay-THB' && country.includes('Thailand')) {
          way.enabled = true
        }

        if (item.ppsName === 'myPay-VND' && country.includes('Vietnam')) {
          way.enabled = true
        }

        if (item.ppsName === 'myPay-IDR' && country.includes('Indonesia')) {
          way.enabled = true
        }

        if (item.ppsName === 'myPay-INR' && country.includes('India')) {
          way.enabled = true
        }
      }

      if (way.enabled) {
        way.status = item.status

        if (way.status === 'available') {
          if (way.dataBaseName === 'pcins' || way.dataBaseName === 'grandPay') {
            way.status = i18n.t('transaction.deposit.credit_card.status')
          } else {
            way.status = i18n.t('transaction.deposit.status.available')
          }
        } else if (way.status === 'under maintenance') {
          way.status = i18n.t('transaction.deposit.status.maintenance')
          way.maintenance = true
        }

        if (way.dataBaseName.includes('awepay')) {
          if (!hasAwepay) {
            hasAwepay = true
            usefulDepositData.push(way)
          }
        } else {
          usefulDepositData.push(way)
        }
      }
    } else if (temp.length > 1) {
      console.log(temp)
      // XCoins 一分为三
      temp.forEach(v => {
        if (item.ppsName === 'xCoins' && (!xcoinsArray.includes(country) || !xcoinsArray.includes(nationality))) {
          if (v.depositMethods === 'Credit Card' && (country.includes('Japan') || nationality.includes('Japan'))) {
            v.enabled = false
          } else if (v.depositMethods === 'Credit Card' &&
            dataList.find(item => item.ppsName === 'grandPay' && country.includes('Japan') && nationality.includes('Japan'))) {
            v.enabled = false
          } else {
            v.enabled = true
            v.status = item.status
            if (v.status === 'available') {
              if (v.dataBaseName === 'pcins') {
                v.status = i18n.t('transaction.deposit.credit_card.status')
              } else {
                v.status = i18n.t('transaction.deposit.status.available')
              }
            } else if (v.status === 'under maintenance') {
              v.status = i18n.t('transaction.deposit.status.maintenance')
              v.maintenance = true
            }
            if (isMauritius) {
              v.enabled = false
            }
            if (v.enabled) {
              usefulDepositData.push(v)
            }
          }
        }
      })
    }
  })

  return queryAllDepositWay(usefulDepositData)
}

function queryAllDepositWay (depositWayList) {
  const query = [
    'jpbank', 'none', 'xCoins', 'mybanq_deposit', 'grandPay', 'mybitwallet', 'usdt', 'pcins', 'awepay_myr_p2p', 'awepay_thb_p2p',
    'awepay_vnd_p2p', 'crypto356', 'myPay-CNY', 'myPay-THB', 'myPay-VND', 'myPay-IDR', 'myPay-INR', 'myPay-KRW', 'monetix', 'monetix-MYR'
  ]
  depositWayList.sort((a, b) => {
    return query.indexOf(a.dataBaseName) - query.indexOf(b.dataBaseName)
  })
  // console.log(depositWayList)
  return depositWayList
}
