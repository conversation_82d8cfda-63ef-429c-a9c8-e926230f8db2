'use struct'

import { Request } from './request'

/**
 * @description 埋点
 */
export const Viewpoint = new Request({
  // baseURL: '',
  // baseURL: 'http://localhost:8306/viewpoint/api',
  // baseURL: 'http://************:8306/api',
  // baseURL: 'https://common-service-1.myfxmarkets.com:8306',
  baseURL: window.location.origin.includes('myfx.group') ? process.env.VUE_APP_INDONESIA_VP_API : process.env.VUE_APP_VP_API,
  headers: {
    'content-type': 'application/json'
  }
})
