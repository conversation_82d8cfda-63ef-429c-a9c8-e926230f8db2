/**
 * @param {string} key 存储的键名
 * @param {*} value 存储的值
 * @param {number} exHours 存储小时，未传入视为永久存储
 * @description 设置本地存储方法
 */
export function setLocal (key, value, exHours) {
  const expire = exHours ? exHours * 60 * 60 * 1000 : 0 // 小时时间
  const storageData = {
    key,
    value,
    expire: exHours ? Date.now() + expire : null, // 当前时间 + 存储时限 = 到期时间戳
  }
  window.localStorage.setItem(key, JSON.stringify(storageData))
}

/**
 * @param {string} key 存储的键名
 * @description 获取本地存储方法
 */
export function getLocal (key) {
  try {

    const localStorageItem = JSON.parse(window.localStorage.getItem(key))
    if (!localStorageItem || !localStorageItem.key) return null // 只对有key值类型的缓存操作
    if (!localStorageItem.expire || localStorageItem.expire >= Date.now()) {
      // 无过期时间或者在有效期内返回数据
      return localStorageItem.value
    }
    // 验证失败或者已过期返回null并删除相应的缓存
    removeLocalItem(key)
    return null
  } catch (error) {
    console.log('无法解析的 JSON 字符串: ' + error.message)
  }
}

/**
 * 删除单个本地存储方法
 * @param {string} key 存储的键名
 */
export function removeLocalItem (key) {
  window.localStorage.removeItem(key)
}

/**
 * @description 检查删除本地存储所有已过期数据
 */
export function checkLocal () {
  const storageKeys = Object.keys(window.localStorage)
  // 获取所有的localStorage循环
  storageKeys.forEach((item) => {
    getLocal(item) // 获取数据方法，过期后会调用删除方法
  })
}

