<template>
  <div style="box-sizing: border-box; overflow-x: hidden">
    <router-view/>
    <LPOAUpdateDialog/>
  </div>

</template>
<script>
import LPOAUpdateDialog from '@/components/LPOAUpdateDialog/index.vue'
import { core } from './config/pluginInit'
import i18n from '@/i18n'
import { mapActions } from 'vuex'
import { LPOACheck } from '@/services/LPOAUpdate'
import { getLocal } from '@/Utils/authLocalStorage'

export default {
  name: 'App',
  components: {
    LPOAUpdateDialog
  },
  computed: {
    language: () => i18n.locale
  },
  mounted () {
    this.$versionPrompt()
    // LPOA
    this.LPOACheck()

    window.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // Version
        this.$versionPrompt()

        // LPOA
        this.LPOACheck()
      }
    })

    core.mainIndex()
  },
  watch: {
    language (val) {
      for (const option of this.$store.getters['Setting/langOptionState']) {
        if (option.value.startsWith(val)) {
          localStorage.setItem('i18n', JSON.stringify(option))
          this.$store.state['Setting/lang'] = option
        }
      }
    }
  },
  beforeMount () {
    const data = localStorage.getItem('i18n')
    if (data) {
      i18n.locale = JSON.parse(data).value
      this.$store.state['Setting/lang'] = JSON.parse(data)
      this.langChangeState(JSON.parse(data))
    } else {
      for (const option of this.$store.getters['Setting/langOptionState']) {
        if (option.value.startsWith(i18n.locale)) {
          localStorage.setItem('i18n', JSON.stringify(option))
          this.$store.state['Setting/lang'] = option
          this.langChangeState(option)
        }
      }
    }
  },

  methods: {
    ...mapActions({
      langChangeState: 'Setting/setLangAction'
    }),

    // LPOA检测
    async LPOACheck () {
      try {
        if (!localStorage.user) return
        const { lpoaChecked } = await LPOACheck({ coguid: JSON.parse(getLocal('user')).myguid })
        if (lpoaChecked === 1) {
          // 暂不显示
          // this.$store.commit('Setting/setLPOAUpdateShow', true)
        }
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>
<style lang="scss">
@import "assets/scss/style.scss";
</style>
