<template>
  <b-container fluid>
    <b-row>
      <b-col>
        <iq-card class="card-body">
          <template v-slot:headerTitle>
            <h4 class="card-title">{{ $t('oata.title') }}</h4>
          </template>
          <template v-slot:body>

            <div v-if="!success">
              <ValidationObserver ref="form" v-slot="{ handleSubmit }">
                <b-form @submit.stop.prevent="handleSubmit(openAccount)">
                  <!--                  测试-->
                  <!--                <b-form @submit.stop.prevent="handleSubmit(submitOpenAccountRequest)">-->
                  <b-form-group class="col-sm-7" :label="$t('oata.label2')" label-cols-sm="2">

                    <b-form-select
                      :disabled="submitLoading"
                      v-model="openAdditionalAccountForm.accountType"
                      :options="AccountTypeOptions"
                      @change="accountTypeChange"
                    ></b-form-select>
                    <!-- PC端 -->
                    <!--                    <div v-loading="reidoActive" class="hidden-sm-and-down">-->
                    <!--                      <RadioButton v-if="!reidoActive" ref="accountTypeNoCommission" @change="changeAccountType"-->
                    <!--                                   value="No Commission" :val="accountType" element="accountType"-->
                    <!--                                   :label="$t('oata.redioLabel1')"-->
                    <!--                                   style="margin-top: 5px;"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->

                    <!--                      <RadioButton v-if="!reidoActive" :val="accountType" ref="accountTypeRawSpreads"-->
                    <!--                                   @change="changeAccountType"-->
                    <!--                                   value="Raw Spreads" element="accountType" :label="$t('oata.redioLabel2')"-->
                    <!--                                   style="margin-top: 5px;"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->

                    <!--                      &lt;!&ndash; 美分账户 &ndash;&gt;-->
                    <!--                      <RadioButton v-if="!reidoActive" :val="accountType" ref="accountTypeMicro"-->
                    <!--                                   @change="changeAccountType"-->
                    <!--                                   :disabled="serverOption === 'MT5'"-->
                    <!--                                   value="Micro" element="accountType" :label="`Micro`"-->
                    <!--                                   style="margin-top: 5px;"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->

                    <!--                      <RadioButton v-if="!reidoActive && isShowManPamm" :val="accountType" ref="accountTypeMAM"-->
                    <!--                                   @change="changeAccountType" value="MAM"-->
                    <!--                                   :disabled="serverOption === 'MT5'" element="accountType" label="MAM"-->
                    <!--                                   style="margin-top: 5px;"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->

                    <!--                      <RadioButton v-if="!reidoActive && isShowManPamm" :val="accountType" ref="accountTypePAMM"-->
                    <!--                                   @change="changeAccountType" value="PAMM"-->
                    <!--                                   :disabled="serverOption === 'MT5'" element="accountType" label="PAMM"-->
                    <!--                                   style="margin-top: 5px;"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->
                    <!--                    </div>-->

                    <!--                    &lt;!&ndash; 移动端 &ndash;&gt;-->
                    <!--                    <div v-loading="reidoActive" class="hidden-md-and-up">-->
                    <!--                      <RadioButton v-if="!reidoActive" ref="accountTypeNoCommission" @change="changeAccountType"-->
                    <!--                                   value="No Commission" :val="accountType" element="accountType"-->
                    <!--                                   :label="$t('oata.redioLabel1')"-->
                    <!--                                   style="margin-top: 5px;">-->
                    <!--                      </RadioButton>-->
                    <!--                      <RadioButton v-if="!reidoActive" ref="accountTypeRawSpreads" @change="changeAccountType"-->
                    <!--                                   value="Raw Spreads" :val="accountType" element="accountType"-->
                    <!--                                   :label="$t('oata.redioLabel2')"-->
                    <!--                                   style="margin-top: 5px;">-->
                    <!--                      </RadioButton>-->

                    <!--                      &lt;!&ndash; 美分账户 &ndash;&gt;-->
                    <!--                      <RadioButton v-if="!reidoActive && isShowManPamm" :val="accountType" ref="accountTypeMicro"-->
                    <!--                                   @change="changeAccountType"-->
                    <!--                                   :disabled="serverOption === 'MT5'"-->
                    <!--                                   value="Micro" element="accountType" :label="`Micro`"-->
                    <!--                                   style="margin-top: 5px;"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->

                    <!--                      <RadioButton v-if="!reidoActive && isShowManPamm" ref="accountTypePAMM" @change="changeAccountType" value="PAMM"-->
                    <!--                                   :disabled="serverOption === 'MT5'" :val="accountType" element="accountType"-->
                    <!--                                   label="PAMM"-->
                    <!--                                   style="margin-top: 5px;">-->
                    <!--                      </RadioButton>-->
                    <!--                      <RadioButton v-if="!reidoActive" ref="accountTypeMAM" @change="changeAccountType" value="MAM"-->
                    <!--                                   :disabled="serverOption === 'MT5'" :val="accountType" element="accountType"-->
                    <!--                                   label="MAM"-->
                    <!--                                   style="margin-top: 5px;">-->
                    <!--                      </RadioButton>-->

                    <!--                    </div>-->
                    <!-- <wd-radio-group v-model="serverOption" checked-color="#6546D2" class="hidden-md-and-up">
                      <wd-radio value="No Commission"  shape="button" >No Commission</wd-radio>
                      <wd-radio value="Raw Spreads"  shape="button" >Raw Spreads</wd-radio>
                      <wd-radio value="MAM"  shape="button" >MAM</wd-radio>
                      <wd-radio value="PAMM"  shape="button" >PAMM</wd-radio>
                    </wd-radio-group> -->
                    <div v-if="!accountTypeCheck" class="validation">
                      <span>{{ $t('validation.selectionValidate', { field: $t('oata.label2') }) }}</span>
                    </div>
                  </b-form-group>

                  <!-- 服务器选择 -->
                  <!--                  <b-form-group class="col-sm-7" :label="$t('oata.label1')" label-cols-sm="2">-->
                  <!--                    <b-form-select-->
                  <!--                      v-model="openAdditionalAccountForm.server"-->
                  <!--                      :options="ServerOptions"-->
                  <!--                    ></b-form-select>-->
                  <!--                    &lt;!&ndash;                    <RadioButton element="server" :val="serverOption" @change="changeServer" :id="server + '-btn'"&ndash;&gt;-->
                  <!--                    &lt;!&ndash;                                 v-for="server in accountTypeOptions.serverOptions" :value.sync="server"&ndash;&gt;-->
                  <!--                    &lt;!&ndash;                                 :label="server">&ndash;&gt;-->
                  <!--                    &lt;!&ndash;                    </RadioButton>&ndash;&gt;-->
                  <!--                    <div v-if="!serverCheck" class="validation">-->
                  <!--                      <span>{{ $t('validation.selectionValidate', { field: $t('oata.label1') }) }}</span>-->
                  <!--                    </div>-->
                  <!--                  </b-form-group>-->

                  <!-- 货币种类选择 -->
                  <b-form-group
                    v-if="!(['MAM', 'PAMM'].includes(openAdditionalAccountForm.accountType))"
                    class="col-sm-7"
                    :label="$t('oata.label3')"
                    label-cols-sm="2"
                  >
                    <b-form-select
                      :disabled="submitLoading"
                      v-model="openAdditionalAccountForm.currency"
                      :options="CurrencyOptions"
                    ></b-form-select>
                    <!--                      <RadioButton-->
                    <!--                        ref="currencyRadio"-->
                    <!--                        style="margin-top: 5px;"-->
                    <!--                        v-for="(currency) in (isMicro ? currencySelectionMicro : currencySelection)"-->
                    <!--                        :key="currency"-->
                    <!--                        :label="currency"-->
                    <!--                        :value.sync="currency"-->
                    <!--                        image-->
                    <!--                        :val="openAdditionalAccountForm.currency"-->
                    <!--                        element="currency"-->
                    <!--                        @change="changeCurrency"-->
                    <!--                      >-->
                    <!--                      </RadioButton>-->
                    <div v-if="!currencyCheck" class="validation">
                      <span>{{ $t('validation.selectionValidate', { field: $t('oata.label3') }) }}</span>
                    </div>
                  </b-form-group>

                  <!-- 杠杆选择 -->
                  <b-form-group
                    class="col-sm-7"
                    label-cols-sm="2"
                    :label="$t('oata.label6')"
                    v-if="!(['MAM', 'PAMM'].includes(openAdditionalAccountForm.accountType))"
                  >
                    <b-form-select
                      :disabled="submitLoading"
                      v-model="openAdditionalAccountForm.leverage"
                      :options="leverageList"
                    ></b-form-select>
                    <div v-if="!leverageCheck" class="validation">
                      <span>{{ $t('validation.selectionValidate', { field: $t('oata.label6') }) }}</span>
                    </div>
                  </b-form-group>

                  <!-- 杠杆协议 -->
                  <b-form-group
                    class="col-sm-7"
                    v-if="!(['MAM', 'PAMM'].includes(openAdditionalAccountForm.accountType))"
                  >
                    <b-form-checkbox
                      v-if="openAdditionalAccountForm.leverage == '500'"
                      v-model="checkedLeverage"
                      @change="leverageTipCheck = checkedLeverage"
                      v-viewpoint.change="{ message: `[Open Additional Trading Account] Click Conditions checkbox` }">
                      {{ $t('accountRegister.radioText') }}
                    </b-form-checkbox>
                    <b-form-checkbox
                      v-if="openAdditionalAccountForm.leverage == '1000'"
                      v-model="checkedLeverage"
                      @change="leverageTipCheck = checkedLeverage"
                      v-viewpoint.change="{ message: `[Open Additional Trading Account] Click Conditions checkbox` }">
                      {{ $t('accountRegister.radioText1000') }}
                    </b-form-checkbox>
                    <div class="validation"
                         v-if="!leverageTipCheck && (openAdditionalAccountForm.leverage == '500' || openAdditionalAccountForm.leverage == '1000')">
                      <span>{{ $t('accountRegister.StepThreeForm.label3_error2') }}</span>
                    </div>
                  </b-form-group>

                  <!-- IB账户 -->
                  <b-form-group class="col-sm-7" label="">
                    <b-form-input
                      :disabled="submitLoading"
                      :placeholder="$t('oata.label4')"
                      v-model="openAdditionalAccountForm.ibCode"
                      @input="changeIbCode"
                    ></b-form-input>
                    <div class="validation" v-if="!ibCodeCheck">
                      <span>{{ $t('oata.ibCodeCheck.noData') }}</span>
                    </div>
                    <div class="validation" v-if="!ibCodeCheck1">
                      <span>{{ $t('oata.ibCodeCheck.isNumber') }}</span>
                    </div>
                  </b-form-group>

                  <!-- 备注 -->
                  <b-form-group class="col-sm-7" label="">
                    <b-form-textarea :disabled="submitLoading" v-model="openAdditionalAccountForm.comments" rows="5"
                                     type="textarea" :placeholder="$t('oata.label5')"
                                     style="font-size: 14px;"></b-form-textarea>
                  </b-form-group>

                  <!--                  <b-form-group class="col-sm-7">
                                      <div style="color: red">
                                        <span>{{ $t('oata.instruction') }}</span>
                                      </div>
                                    </b-form-group>-->

                  <b-form-group class="col-sm-7">
                    <b-form-checkbox v-model="checked"
                                     @change="boxCheck = checked"
                                     v-viewpoint.change="{ message: `[Open Additional Trading Account] Click Conditions checkbox` }">
                      {{ $t('oata.test1') }}
                    </b-form-checkbox>
                    <div class="validation" v-if="!boxCheck">
                      <span>{{ $t('validation.checkbox') }}</span>
                    </div>
                  </b-form-group>

                  <ValidationProvider rules="checkbox">
                    <b-form-group class="col-sm-7">
                      <el-button
                        @click.native="openFile"
                        type="text"
                        style="color: #495EEB;font-size: 14px; padding: 0;"
                        v-viewpoint.click="{ message: `[Open Additional Trading Account] Click MYFX Markets Terms and Conditions` }"
                      >
                        <i style="color: #495EEB" class="fas fa-file-pdf"></i>
                        {{ $t('oata.test2') }}
                      </el-button>
                      <!--                      <b-form-checkbox v-model="checkedFile1" @change="boxCheck1 = checkedFile1">
                                              <el-button @click.native="openFile" type="text" style="color: #9055f9;font-size: 14px; padding: 0;">
                                                <i style="color: #9055f9" class="fas fa-file-pdf"></i>
                                                {{ $t('oata.test2') }}
                                              </el-button>
                                            </b-form-checkbox>
                                            <div class="validation" v-if="!boxCheck1">
                                              <span>{{ $t('validation.checkbox') }}</span>
                                            </div>-->

                      <!--                      <br>-->
                      <!--                      <div style="margin-top: 10px">
                                              <b-form-checkbox v-model="checkedFile2" @change="boxCheck2 = checkedFile2" >
                                                <el-button @click.native="openFile" type="text" style="color: #9055f9;font-size: 14px; padding: 0">
                                                  <i style="color: #9055f9" class="fas fa-file-pdf"></i>
                                                  {{ $t('oata.test_2_2') }}
                                                </el-button>
                                              </b-form-checkbox>
                                            </div>

                                            <div class="validation" v-if="!boxCheck2" >
                                              <span>{{ $t('validation.checkbox') }}</span>
                                            </div>-->

                    </b-form-group>
                  </ValidationProvider>

                  <b-form-group class="col-sm-7" style="margin-top: 40px;">
                    <b-button
                      class="submit-button btn-common-shadow"
                      :class="{ 'submit-ok': submitOK }"
                      :disabled="submitLoading"
                      type="submit"
                      variant="primary"
                      v-viewpoint.click="{ message: `[Open Additional Trading Account] Click Submit` }"
                    >
                      {{ $t('sidebar.submit') }}
                      <i v-if="submitLoading"
                         style="padding: 0;margin-left: 5px" class="el-icon-loading"></i>
                      <span></span>
                    </b-button>
                  </b-form-group>
                </b-form>
              </ValidationObserver>
            </div>

            <!--            <el-result v-if="success" icon="success" :title="$t('oata.openSuccessTitle')">-->
            <!--              <template slot="extra">-->
            <!--                -->
            <!--              </template>-->
            <!--            </el-result>-->
            <div
              v-if="success"
              style="display: flex;flex-direction: column; align-items: center;padding: 20px 0; min-height: calc(100vh - 350px)"
            >
              <div>
                <img width="360" src="@/assets/images/open-account.png">
              </div>
              <b-button
                style="margin-top: 50px;"
                class="btn-common-shadow"
                variant="primary"
                size="medium"
                @click="success=false"
                v-viewpoint.click="{ message: `[Open Additional Trading Account] Click success back button` }">
                {{ $t('sidebar.back') }}
              </b-button>
            </div>
          </template>
        </iq-card>
      </b-col>
    </b-row>


    <el-dialog v-if="checkForDocumentDialogMobileVisible" custom-class="tip-dialog" :title="$t('oata.title3')"
               :visible.sync="checkForDocumentDialogMobileVisible" :show-close="true" :close-on-click-modal="false"
               :width="dialogWidth"
               :close-on-press-escape="false">
      <div style="text-align: center" class="div-wrap-text">
        {{ $t('oata.test3') }}
      </div>
      <div style="text-align: center">
        <b-button class="document-btn" @click="$router.push({name: 'production.myDocument'})" pill>
          {{ $t('oata.test4') }}
        </b-button>
      </div>
      <div style="text-align: center" class="div-wrap-text">
        {{ $t('oata.test5') }}
      </div>
      <!--      <div class="div-wrap-text" style="color: #9055f9;text-align: center">
              {{$t('oata.test6')}}
            </div>-->
    </el-dialog>


    <el-dialog v-if="LPOAVisible" :close-on-click-modal="false" @close="submitLoading = false" :destroy-on-close="true"
               :close-on-press-escape="false" :width="dialogWidth"
               :visible.sync="LPOAVisible">
      <LPOA :checkAccount="checkOpenAccount" :LPOAVisible.sync="LPOAVisible" :submitLoading.sync="submitLoading"
            :contract-id="lpoaContractId"
            :lpoa-format="lpoaFormat"
            :submit-open-account="submitOpenAccountRequest" :account-form="openAdditionalAccountForm"></LPOA>
    </el-dialog>
  </b-container>
</template>

<script>
import { getLocal } from '@/Utils/authLocalStorage'
import { checkOpenAccount, ibCheck, submitOpenMt4AccountRequest, iBCodeMapping } from '@/services/account'
import RadioButton from '@/components/MyfxComponent/RadioButton'
import LPOA from '@/components/template/LPOA'
import sha256 from 'crypto-js/sha256'
import md5 from 'crypto-js/md5'
// import { LPOA_PDF } from '@/Utils/LPOA_PDF'
//
// LPOA_PDF('English', {
//   lpoaFormat: 'this.lpoaFormat',
//   contractId: 'this.contractId',
//   dateTime: 'this.dateTime',
//   fullName: 'this.fullName',
//   isSpecialIb: 'this.isSpecialIb',
//   dateformat: 'this.dateformat',
//   actionparams: 'this.lpoaFormat.actionparams',
//   coguid: 'this.userInfo.myguid',
//   ibcode: 'this.lpoaFormat.ibcode',
//   ibname: 'this.lpoaFormat.ibname',
//   firstName: 'this.firstName',
//   lastName: 'this.lastName'
// })

export default {
  name: 'OpenAdditionalAccount',
  components: {
    LPOA,
    RadioButton
  },

  computed: {

    /**
     * @description 2024-11-22 选择交互修改为下拉菜单
     * <AUTHOR>
     */

    // Account Type
    AccountTypeOptions () {

      // MAM、PAMM 泰国客户不可见
      if (JSON.parse(getLocal('user')).country === 'Thailand') {
        return [
          { text: this.$t('accountRegister.mt4AccountTypeList.select1.item'), value: 'Standard' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select2.item'), value: 'Pro' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select7.item'), value: 'mt4_micro_standard' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select3.item'), value: 'st_mt5' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select4.item'), value: 'pro_mt5' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select8.item'), value: 'Copy Trade' }
        ]
      }

      // Copy Trade, Thailand / Malaysia / Indonesia可见
      if (JSON.parse(getLocal('user')).country === 'Malaysia' ||
        JSON.parse(getLocal('user')).country === 'Indonesia') {
        return [
          { text: this.$t('accountRegister.mt4AccountTypeList.select1.item'), value: 'Standard' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select2.item'), value: 'Pro' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select7.item'), value: 'mt4_micro_standard' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select3.item'), value: 'st_mt5' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select4.item'), value: 'pro_mt5' },
          { text: 'MAM', value: 'MAM' },
          { text: 'PAMM', value: 'PAMM' },
          { text: this.$t('accountRegister.mt4AccountTypeList.select8.item'), value: 'Copy Trade' }
        ]
      }

      // 默认
      return [
        { text: this.$t('accountRegister.mt4AccountTypeList.select1.item'), value: 'Standard' },
        { text: this.$t('accountRegister.mt4AccountTypeList.select2.item'), value: 'Pro' },
        { text: this.$t('accountRegister.mt4AccountTypeList.select7.item'), value: 'mt4_micro_standard' },
        { text: this.$t('accountRegister.mt4AccountTypeList.select3.item'), value: 'st_mt5' },
        { text: this.$t('accountRegister.mt4AccountTypeList.select4.item'), value: 'pro_mt5' },
        { text: 'MAM', value: 'MAM' },
        { text: 'PAMM', value: 'PAMM' }
      ]
    },

    // Leverage
    leverageList () {
      // MT4 Standard Account => 1000、500、400、200、100
      // MAM、PAMM => 400、200、100
      // 其他 => 500、400、200、100
      this.openAdditionalAccountForm.leverage = ''

      if (['Standard', 'st_mt5'].includes(this.openAdditionalAccountForm.accountType)) {
        return ['1000', '500', '400', '200', '100']
      }
      if (['MAM', 'PAMM'].includes(this.openAdditionalAccountForm.accountType)) {
        return ['400', '200', '100']
      }
      // 默认
      return ['500', '400', '200', '100']
    },

    // Currency
    CurrencyOptions () {
      // Micro => USD
      // 其他 => USD、JPY、AUD、GBP、EUR
      if (this.openAdditionalAccountForm.accountType === 'mt4_micro_standard') {
        this.openAdditionalAccountForm.currency = 'USD'
        return ['USD']
      }

      if (this.openAdditionalAccountForm.accountType === 'Copy Trade') {
        return ['USD']
      }

      // 默认
      return ['USD', 'JPY', 'AUD', 'GBP', 'EUR']
    }

  },

  data () {
    return {
      submitOK: false,
      reidoActive: false,
      // accountTypeSelection: ['MT4 - No Commission', 'MT4 - Raw Spreads', 'MAM', 'PAMM', 'MT5 - No Commission', 'MT5 - Raw Spreads'],
      // currencySelection: ['USD', 'JPY', 'AUD', 'GBP', 'EUR'],
      // currencySelectionMicro: ['USD'],

      accountTypeOptions: {
        serverOptions: ['MT4', 'MT5'],
        mt4AccountTypeOptions: ['No Commission', 'Raw Spreads', 'MAM', 'PAMM'],
        mt5AccountTypeOptions: ['No Commission', 'Raw Spreads']
      },
      accountType: '',
      serverOption: '',
      mt4AccountTypeOption: '',
      mt5AccountTypeOption: '',

      openAdditionalAccountForm: {
        accountType: '',
        currency: '',
        ibCode: '',
        comments: '',
        accountMyguid: JSON.parse(getLocal('user')).myguid,
        leverage: ''
      },

      submitLoading: false,
      checked: false,
      checkedLeverage: false,
      checkedFile1: false,
      checkedFile2: false,
      serverCheck: true,
      accountTypeCheck: true,
      currencyCheck: true,
      leverageCheck: true,
      leverageTipCheck: true,
      ibCodeCheck: true,
      ibCodeCheck1: true,
      boxCheck: true,
      boxCheck1: true,
      boxCheck2: true,
      success: false,
      checkForDocumentDialogVisible: false,
      checkForDocumentDialogMobileVisible: false,
      LPOAVisible: false,
      lpoaFormat: null,
      lpoaContractId: '',
      dialogWidth: '',
      isShowLeverage: false,
      isShowManPamm: true
    }
  },

  watch: {
    'openAdditionalAccountForm.accountType' (newVal, oldVal) {
      if (newVal?.length !== 0) {
        // this.accountTypeButtonChecked = newVal
        this.accountTypeCheck = true
      }
    },
    // 'openAdditionalAccountForm.server' (newVal, oldVal) {
    //   if (oldVal !== null && oldVal !== '' && oldVal !== undefined) {
    //     this.openAdditionalAccountForm.accountType = ''
    //   }
    //   if (newVal.length !== 0) {
    //     this.serverCheck = true
    //   } else {
    //     this.accountTypeCheck = false
    //   }
    //   if (newVal === 'MT5' && (!this.accountTypeOptions.mt5AccountTypeOptions.includes(this.accountType))) { //当选择的accountType不在mt5里面
    //     this.reidoActive = true
    //     setTimeout(() => this.reidoActive = false, 500)
    //   }
    //   this.accountMapping(newVal, this.accountType)
    // },
    'openAdditionalAccountForm.currency' (newVal, oldVal) {
      if (newVal?.length !== 0) {
        this.currencyCheck = true
      }
    },
    'openAdditionalAccountForm.leverage' (newVal, oldVal) {
      if (newVal?.length !== 0) {
        this.leverageCheck = true
      }
    }
  },
  created () {
    this.setDialogWidth()
    this.checkForDocument()
    this.success = false
    // this.showManPamm()
  },
  mounted () {
    window.onresize = () => {
      return (() => {
        this.setDialogWidth()
      })()
    }
  },
  methods: {

    /**
     * @description 检测 IB CODE
     */
    async checkIbCode () {
      try {
        const myguid = JSON.parse(getLocal('user')).myguid
        const tokenSha256 = sha256(`${ this.openAdditionalAccountForm.ibCode }|${ myguid }asufh2397jdhg`)

        const { code, data } = await iBCodeMapping({
          ibCode: this.openAdditionalAccountForm.ibCode,
          coGuid: myguid,
          token: md5(`${ tokenSha256.toString() }vgyuw89asd9fsd923`).toString()
        })

        if (code == 200 && data) {
          if (!data.actioncode) return
          if (data.actioncode === 'enablespecialpamm') {
            this.openAdditionalAccountForm.accountType = 'PAMM'
          } else this.openAdditionalAccountForm.accountType = 'MAM'
          this.openAdditionalAccountForm.leverage = ''
          this.openAdditionalAccountForm.currency = ''

        } else {

        }

      } catch (err) {
        console.log(err)
      }
    },


    /**
     * @description 2024-11-22 选择交互修改为下拉菜单
     * <AUTHOR>
     */
    accountTypeChange (ev) {
      if (['MAM', 'PAMM'].includes(ev)) {
        this.openAdditionalAccountForm.leverage = ''
        this.openAdditionalAccountForm.currency = ''
      }
    },


    // ----------------------------------------------


    // country = Thailand MAN PAMM隐藏
    // showManPamm () {
    //   this.isShowManPamm = JSON.parse(getLocal('user')).country !== 'Thailand'
    // },

    setDialogWidth () {
      const val = document.body.clientWidth
      const def = 800 // 默认宽度
      if (val < def) {
        this.dialogWidth = '100%'
      } else {
        this.dialogWidth = def + 'px'
      }
    },
    validate () {
      this.accountTypeCheck = this.openAdditionalAccountForm?.accountType
      this.leverageCheck = this.openAdditionalAccountForm?.leverage
      this.boxCheck = this.checked
      this.leverageTipCheck = this.checkedLeverage
      this.boxCheck1 = this.checkedFile1
      // this.boxCheck2 = this.checkedFile2
      // return (this.serverCheck && this.accountTypeCheck && this.currencyCheck && this.boxCheck && this.boxCheck1 && this.boxCheck2)
      if (this.openAdditionalAccountForm?.accountType === 'MAM' || this.openAdditionalAccountForm?.accountType === 'PAMM') {
        this.currencyCheck = true
        this.ibCodeCheck = !!this.openAdditionalAccountForm?.ibCode
        this.ibCodeCheck1 = !this.ibCodeCheck || /^[a-zA-Z0-9]+$/.test(this.openAdditionalAccountForm?.ibCode)
        return (this.serverCheck && this.accountTypeCheck && this.ibCodeCheck && this.ibCodeCheck1 && this.boxCheck)
      } else {
        this.ibCodeCheck1 = !this.openAdditionalAccountForm?.ibCode || /^[a-zA-Z0-9]+$/.test(this.openAdditionalAccountForm?.ibCode)
        this.currencyCheck = this.openAdditionalAccountForm?.currency.length !== 0
        if (this.openAdditionalAccountForm?.leverage === '500' || this.openAdditionalAccountForm?.leverage === '1000') {
          return (this.leverageTipCheck && this.leverageCheck && this.serverCheck && this.accountTypeCheck && this.currencyCheck && this.boxCheck && this.ibCodeCheck1)
        } else {
          return (this.leverageCheck && this.serverCheck && this.accountTypeCheck && this.currencyCheck && this.boxCheck && this.ibCodeCheck1)
        }
      }
    },
    // 输入ibCode做校验
    changeIbCode () {
      this.validate()
    },
    async checkOpenAccount () {
      let result = true
      await checkOpenAccount(async res => {
        result = true
      }).catch(error => {
        result = false
      })

      return result
    },
    async openAccount () {
      this.checkForDocument()
      if (this.checkForDocumentDialogMobileVisible) return

      if (!this.validate()) {
        this.submitLoading = false
        return
      }

      // 检测ibcode
      if (this.openAdditionalAccountForm.ibCode) {
        await this.checkIbCode()
      }

      this.submitLoading = true

      if (this.openAdditionalAccountForm?.accountType === 'MAM' || this.openAdditionalAccountForm?.accountType === 'PAMM') {
        if (this.validate()) {
          ibCheck(this.openAdditionalAccountForm.ibCode).then(async res => {
            this.lpoaFormat = res.data
            this.lpoaContractId = res.contractMyGuid
            this.LPOAVisible = true
          }).catch(async error => {
            this.submitLoading = false
          })
        } else {
          this.submitLoading = false
        }
      } else {
        // 测试时关闭
        let booleanPromise = await this.checkOpenAccount()
        if (!booleanPromise) {
          this.submitLoading = false
          return
        }
        this.submitOpenAccountRequest()
      }
      // 测试时开启
      // this.submitOpenAccountRequest()
    },
    submitOpenAccountRequest () {
      if (!this.validate()) return this.submitLoading = false

      this.submitLoading = true

      let currency = this.openAdditionalAccountForm.currency
      // 美分账户需要改为 USC
      if (this.openAdditionalAccountForm.accountType === 'mt4_micro_standard') currency = 'USC'

      // console.log(JSON.stringify({
      //   ...this.openAdditionalAccountForm,
      //   currency
      // }, null, 4))


      submitOpenMt4AccountRequest({
        ...this.openAdditionalAccountForm,
        currency
      }).then(res => {
        this.submitOK = true

        setTimeout(() => {
          this.submitOK = false
          this.openAdditionalAccountForm.accountMyguid = ''
          this.openAdditionalAccountForm.ibCode = ''
          this.openAdditionalAccountForm.accountType = ''
          this.openAdditionalAccountForm.currency = ''
          this.openAdditionalAccountForm.comments = ''
          this.checked = false
          this.checkedLeverage = false
          this.success = true
          this.submitLoading = false
        }, 1300)
      }).catch(error => {
        this.openAdditionalAccountForm.accountMyguid = ''
        this.openAdditionalAccountForm.ibCode = ''
        this.openAdditionalAccountForm.accountType = ''
        this.openAdditionalAccountForm.currency = ''
        this.openAdditionalAccountForm.comments = ''
        this.checked = false
        this.checkedLeverage = false
        this.submitLoading = false
      })
    },
    // accountMapping (server, accountType) {
    //   if (server === 'MT4') {
    //     if (accountType === 'No Commission') {
    //       this.openAdditionalAccountForm.accountType = 'Standard'
    //     }
    //     if (accountType === 'Raw Spreads') {
    //       this.openAdditionalAccountForm.accountType = 'Pro'
    //     }
    //     if (accountType === 'Micro') {
    //       this.openAdditionalAccountForm.accountType = 'mt4_micro_standard'
    //     }
    //     if (accountType === 'MAM' || accountType === 'PAMM') {
    //       this.openAdditionalAccountForm.accountType = accountType
    //     }
    //   }
    //   if (server === 'MT5') {
    //     if (accountType === 'No Commission') {
    //       this.openAdditionalAccountForm.accountType = 'st_mt5'
    //     }
    //     if (accountType === 'Raw Spreads') {
    //       this.openAdditionalAccountForm.accountType = 'pro_mt5'
    //     }
    //   }
    //
    //   if (server === '' || server === null || server === undefined) {
    //     this.openAdditionalAccountForm.accountType = ''
    //   }
    // },
    // changeServer (val) {
    //   // 执行清除
    //   this.serverOption = val
    // },
    // changeCurrency (val) {
    //   this.openAdditionalAccountForm.currency = val
    // },
    // changeAccountType (val) {
    //   this.isShowLeverage = val === 'MAM' || val === 'PAMM'
    //   this.ibCodeCheck = true
    //   this.ibCodeCheck1 = true
    //   this.accountMapping(this.serverOption, val)
    //   this.accountType = val
    //   this.openAdditionalAccountForm.accountType = val
    //   this.leverageList = val === 'No Commission' ? [1000, 500, 400, 200, 100] : [500, 400, 200, 100]
    //   this.openAdditionalAccountForm.leverage = 400
    //   if (val === 'MAM' || val === 'PAMM') {
    //     this.leverageList = [400, 200, 100]
    //     if (!this.serverOption) {
    //       document.querySelector('#MT4-btn .box').click()
    //       this.changeServer('MT4')
    //     }
    //   }
    //
    //   // 美分账户，货币只有美分
    //   this.isMicro = val === 'Micro'
    //   this.openAdditionalAccountForm.currency = ''
    //   this.$refs.currencyRadio.forEach(item => {
    //     item.clear()
    //   })
    // },
    openFile () {
      window.open('/static/Myfx-Client-Agreement.pdf')
    },
    // clearAccountType(){
    //   if(this.openAdditionalAccountForm.accountType === 'Standard' || this.openAdditionalAccountForm.accountType === 'st_mt5'){
    //     this.$refs.accountTypeNoCommission.clear();
    //     this.openAdditionalAccountForm.accountType=""
    //   }else if(this.openAdditionalAccountForm.accountType === 'Pro' || this.openAdditionalAccountForm.accountType === 'pro_mt5'){
    //     this.$refs.accountTypeRawSpreads.clear();
    //     this.openAdditionalAccountForm.accountType=""
    //   }else if(this.openAdditionalAccountForm.accountType === 'MAM' || this.openAdditionalAccountForm.accountType  === 'PAMM'){
    //     this.$refs.accountTypeMAM.clear();
    //     this.$refs.accountTypePAMM.clear();
    //     this.openAdditionalAccountForm.accountType=""
    //   }
    // }
    checkForDocument () {
      const isok = JSON.parse(getLocal('user')).isok
      this.checkForDocumentDialogMobileVisible = isok != 1
    }
  }

}
</script>

<style lang="scss" scoped>
.validation {
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #dc3545;
  float: right;
}

.document-btn {
  margin-top: 30px;
  margin-bottom: 20px;
  width: 60%;
  background: linear-gradient(to right, #371bee, #7b1ef8);
  transform: scale(1);
}

.document-btn:active {
  transform: scale(0.97);
}

.div-wrap-text {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #7937f4;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #b285e5;
}

.el-radio {
  .is-bordered.is-checked {
    border-color: #871fee;
  }

  // 默认文本样式
  .el-radio__label {
    color: rgba(0, 0, 0, 0.71);
  }

  // 选中文本样式
  .el-radio__input.is-checked + .el-radio__label {
    color: #871fee;
  }

  // 选中 icon 样式
  .el-radio__inner::after {
    background-color: #871fee;
  }

  // 覆盖默认 样式
  .el-radio__input.is-checked .el-radio__inner {
    border: 1px solid #871fee;
    background-color: transparent;
  }
}

.tip-dialog {
  width: 90%;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
}

@media (min-width: 493px) {
  .tip-dialog {
    width: 70%;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
  }
}

@media (min-width: 1024px) {
  .tip-dialog {
    width: 50%;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
  }
}

@media (min-width: 1440px) {
  .tip-dialog {
    width: 30%;

  }
}

::v-deep .card-style4 :not(input:checked) + .option {
  //background-color: #eef0fd;
  background-color: #ffffff;
  border: 1px solid #485eeb;
}

::v-deep .card-style4 input:checked + .option {
  background-color: #485eeb;
}

.form-group:first-child ::v-deep .card-style4 :not(input:checked) + .option {
  background-color: #ffffff;
  border: 1px solid #485eeb;
}

::v-deep .card-style4 input:checked + .option .radio-title {
  color: #FFFFFF;
}

.form-group:first-child ::v-deep .card-style4 :not(input:checked) + .option .radio-title {
  color: #485eeb;
}

.submit-button {
  position: relative;
  //width: 100px;
  padding: 6px 30px;
  overflow: hidden;

  span {
    top: 0;
    left: 0;
    position: absolute;
    width: 100%;
    transition: all .3s;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: -20px;
      width: 0px;
      height: 0px;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transition: all .3s;
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  }

  //box-shadow: 0 6px 5px -3px rgba(0, 0, 0, .3) !important;

  &:active {
    //letter-spacing: 2px;
    //box-shadow: 0 0 10px rgba(127, 142, 246, 1) !important;
  }
}

.submit-ok {
  border-color: #53B502 !important;
  background: #53B502 !important;

  span {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #53B502;

    &::before {
      top: 44%;
      width: 20px;
      height: 10px;
    }
  }
}
</style>
