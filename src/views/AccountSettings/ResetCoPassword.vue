<template>
  <div class="app-container" style="margin: 0 15px">
    <h4 style="margin-bottom: 40px;padding-left: 10px;">{{ $t('reset_password.title') }}</h4>
    <!--    <div class="content">-->
    <el-form
      style="padding: 0 28px;"
      ref="form"
      :model="form"
      :rules="formRules"
      label-width="0px"
      label-position="left"
    >

      <el-form-item label="">
        <div style="display: flex;align-items: center;gap: 10px">
          <div class="ipt-row">
            <el-input
              style="width: 300px"
              type="password"
              auto-complete="new-password"
              :disabled="status.current === 2"
              v-model="form.currentPassword"
              :placeholder="$t('reset_password.label1')"
              @blur="verifyCurrentPasswordHandle"
            ></el-input>
            <div class="ipt-label">{{ $t('reset_password.label1') }}*</div>
          </div>
          <div>
            <i class="el-icon-loading" v-if="status.current === 1"></i>
            <i class="el-icon-success" style="color: green;" v-if="status.current === 2"></i>
            <i class="el-icon-error" style="color: red" v-if="status.current === 3"></i>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="question" style="display: flex;align-items: center;gap: 10px">
          <div style="display: flex;align-items: center;gap: 10px">
            <div class="ipt-row">
              <el-input
                style="width: 300px"
                v-model="form.answer"
                :disabled="status.answer === 2 || status.current !== 2"
                :placeholder="form.question || $t('reset_password.label2')"
                @blur="verifySecurityQuestionHandle"
              ></el-input>
              <div class="ipt-label">{{ $t('reset_password.label2') }}*</div>
            </div>
            <div>
              <i class="el-icon-loading" v-if="status.answer === 1"></i>
              <i class="el-icon-success" style="color: green;" v-if="status.answer === 2"></i>
              <i class="el-icon-error" style="color: red" v-if="status.answer === 3"></i>
            </div>
          </div>
          <DialogSecurity class="DialogSecurity" v-if="status.answer !== 2 && status.current === 2"></DialogSecurity>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div style="display: flex;align-items: center;gap: 10px" class="newPassword">
          <div class="ipt-row">
            <el-input
              style="width: 300px"
              type="password"
              auto-complete="new-password"
              v-model="form.newPassword"
              :placeholder="$t('reset_password.label4')"
              @input="newPasswordInput"
              @blur="verifyConfirmPassword"
            ></el-input>
            <div class="ipt-label">{{ $t('reset_password.label4') }}*</div>
          </div>
          <div style="position: relative">
            <div style="line-height: 0; display: grid; grid-template-columns: 1fr 1fr;">
              <el-checkbox style="line-height: 20px;margin-bottom: 0; pointer-events: none" v-model="check1">
                {{ $t('accountRegister.checkbox1') }}
              </el-checkbox>
              <el-checkbox style="line-height: 20px;margin-bottom: 0; pointer-events: none" v-model="check2">
                {{ $t('accountRegister.checkbox2') }}
              </el-checkbox>
              <el-checkbox style="line-height: 20px;margin-bottom: 0; pointer-events: none" v-model="check3">
                {{ $t('accountRegister.checkbox3') }}
              </el-checkbox>
              <el-checkbox style="line-height: 20px;margin-bottom: 0; pointer-events: none" v-model="check4">
                {{ $t('accountRegister.checkbox4') }}
              </el-checkbox>
              <!--            <el-checkbox style="line-height: 20px;margin-bottom: 0; pointer-events: none" v-model="check5">-->
              <!--              {{ $t('accountRegister.checkbox_5') }}-->
              <!--            </el-checkbox>-->
            </div>
            <div style="height: 4px"></div>
            <div v-if="!check5" style="position: absolute; white-space: nowrap; font-size: 12px; line-height: 1;" v-html="$t('accountRegister.checkbox_5')"></div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div style="display: flex;align-items: center;gap: 10px">
          <div class="ipt-row">
            <el-input
              style="width: 300px"
              type="password"
              v-model="form.confirmPassword"
              :placeholder="$t('reset_password.label5')"
              @blur="verifyConfirmPassword"
            ></el-input>
            <div class="ipt-label">{{ $t('reset_password.label5') }}*</div>
          </div>
          <div>
            <i class="el-icon-loading" v-if="status.confirmPassword === 1"></i>
            <i class="el-icon-success" style="color: green;" v-if="status.confirmPassword === 2"></i>
            <i class="el-icon-error" style="color: red" v-if="status.confirmPassword === 3"></i>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <div style="width: fit-content">
          <div v-if="googleRecaptchaVisible" id="google-captcha">
            <GoogleRecaptcha
              ref="recaptcha"
              @getValidateCode="clickGoogleCaptcha"
              :captchaType="captchaType"
              key="6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_"
            ></GoogleRecaptcha>
          </div>
          <div v-else style="display: flex;align-items: center;gap: 10px" class="code">
            <div class="ipt-row">
              <el-input
                style="width: 300px"
                v-model="form.code"
                :placeholder="$t('reset_password.label6')"
              ></el-input>
              <div class="ipt-label">{{ $t('reset_password.label6') }}*</div>
            </div>
            <el-image
              v-loading="captchaLoading"
              :fit="codePictureStyle"
              @click="clickCodeImage"
              :src="codePicture"
              class="captcha-picture"
              element-loading-spinner="el-icon-loading"
            ></el-image>
          </div>
        </div>
      </el-form-item>
      <div style="">
        <b-button
          style="margin-top: 12px;"
          class="step-one"
          :class="{ 'step-one-ok': stepOneButtonShow }"
          variant="primary"
          :disabled="submitLoading || !(status.current === 2 && status.answer === 2 && check1 && check2 && check3 && check4 && check5 && status.confirmPassword === 2)"
          @click="submitNewPassword"
        >
          {{ $t('sidebar.next') }}
          <i v-if="submitLoading"
             style="padding: 0;margin-left: 5px"
             class="el-icon-loading"/>
          <span></span>
        </b-button>
      </div>
    </el-form>
    <!--    </div>-->

  </div>
</template>

<!--<template>-->
<!--  <b-container fluid>-->
<!--    <b-row>-->
<!--      <b-col>-->
<!--        <iq-card class="card-body">-->
<!--          <template v-slot:headerTitle>-->
<!--            <h4 class="card-title">{{ $t('reset_password.title') }}</h4>-->
<!--          </template>-->
<!--          <template v-slot:body>-->
<!--            <el-steps :active="active" align-center finish-status="success">-->
<!--              <el-step :description="$t('reset_password.description1')"></el-step>-->
<!--              <el-step :description="$t('reset_password.description2')"></el-step>-->
<!--              <el-step :description="$t('reset_password.description3')"></el-step>-->
<!--            </el-steps>-->
<!--            <ValidationObserver v-if="active === 0" ref="form" v-slot="{ handleSubmit }">-->
<!--              <b-form @submit.stop.prevent="handleSubmit(verifyCurrentPassword)" class="card-body">-->
<!--                <ValidationProvider name="reset_password.label1" rules="required" v-slot="{ errors }">-->
<!--                  <b-form-group-->
<!--                    class="col-sm-7"-->
<!--                    :label="$t('reset_password.label1')"-->
<!--                    label-cols-sm="3"-->
<!--                    style="padding: 0"-->
<!--                  >-->
<!--                    <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
<!--                                  style="border-color: #DBDCFC;width: 240px"-->
<!--                                  type="password"-->
<!--                                  v-bind:disabled="submitLoading"-->
<!--                                  v-model="currentPassword"-->
<!--                    ></b-form-input>-->
<!--                    <div class="validation">-->
<!--                      <span>{{ errors[0] }}</span>-->
<!--                    </div>-->
<!--                  </b-form-group>-->
<!--                </ValidationProvider>-->
<!--                <b-button-->
<!--                  class="step-one"-->
<!--                  :class="{ 'step-one-ok': stepOneButtonShow }"-->
<!--                  variant="primary"-->
<!--                  type="submit"-->
<!--                  style="margin-top: 12px;"-->
<!--                >-->
<!--                  {{ $t('sidebar.next') }}-->
<!--                  <i v-if="submitLoading"-->
<!--                     style="padding: 0;margin-left: 5px"-->
<!--                     class="el-icon-loading"/>-->
<!--                  <span></span>-->
<!--                </b-button>-->
<!--              </b-form>-->
<!--            </ValidationObserver>-->

<!--            <ValidationObserver v-if="active === 1" ref="form" v-slot="{ handleSubmit }">-->
<!--              <b-form v-loading="questionLoading" style="padding: 0;margin-top: 20px;border: 1px solid #DBDCFC"-->
<!--                      @submit.stop.prevent="handleSubmit(verifySecurityQuestion)"-->
<!--                      class="card-body">-->
<!--                <div class="question-bar">-->
<!--                  {{ $t('reset_password.label2') }}-->
<!--                </div>-->
<!--                <div class="question-title">{{ securityQuestion.question }}</div>-->
<!--                <DialogSecurity></DialogSecurity>-->
<!--                <div class="question-form">-->
<!--                  &lt;!&ndash;                  <input v-model="answer.answer" @keyup.enter="next" class="question-form-ipt" type="text"/>&ndash;&gt;-->
<!--                  <ValidationProvider name="reset_password.label3" rules="required" v-slot="{ errors }">-->
<!--                    <b-form-group style="padding: 0;">-->
<!--                      <b-form-input style="border-color: #DBDCFC;"-->
<!--                                    :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
<!--                                    v-bind:disabled="submitLoading" v-model="answer.answer"></b-form-input>-->
<!--                      <div class="validation">-->
<!--                        <span>{{ errors[0] }}</span>-->
<!--                      </div>-->
<!--                      &lt;!&ndash;                      <DialogSecurity></DialogSecurity>&ndash;&gt;-->
<!--                    </b-form-group>-->
<!--                  </ValidationProvider>-->
<!--                  &lt;!&ndash;                  <div class="question-form-btn">&ndash;&gt;-->
<!--                  &lt;!&ndash;                    <div></div>&ndash;&gt;-->
<!--                  &lt;!&ndash;                    <div class="question-form-btn-next" @click="next">{{ $t('sidebar.next') }}</div>&ndash;&gt;-->
<!--                  &lt;!&ndash;                  </div>&ndash;&gt;-->
<!--                  <b-button class="btn-common-shadow" variant="primary" type="submit" style="margin-top: 12px;">-->
<!--                    {{ $t('sidebar.next') }}-->
<!--                    <i v-if="submitLoading"-->
<!--                       style="padding: 0;margin-left: 5px"-->
<!--                       class="el-icon-loading"/>-->
<!--                  </b-button>-->
<!--                </div>-->
<!--                <div style="height: 30px"></div>-->
<!--                &lt;!&ndash;                <b-form-group&ndash;&gt;-->
<!--                &lt;!&ndash;                  v-loading="questionLoading"&ndash;&gt;-->
<!--                &lt;!&ndash;                  class="col-sm-7"&ndash;&gt;-->
<!--                &lt;!&ndash;                  :label="$t('reset_password.label2')"&ndash;&gt;-->
<!--                &lt;!&ndash;                  label-cols-sm="3">&ndash;&gt;-->
<!--                &lt;!&ndash;                  <span>{{ securityQuestion.question }}</span>&ndash;&gt;-->
<!--                &lt;!&ndash;                </b-form-group>&ndash;&gt;-->
<!--                &lt;!&ndash;                <ValidationProvider name="reset_password.label3" rules="required" v-slot="{ errors }">&ndash;&gt;-->
<!--                &lt;!&ndash;                  <b-form-group&ndash;&gt;-->
<!--                &lt;!&ndash;                    class="col-sm-7"&ndash;&gt;-->
<!--                &lt;!&ndash;                    :label="$t('reset_password.label3')"&ndash;&gt;-->
<!--                &lt;!&ndash;                    label-cols-sm="3">&ndash;&gt;-->
<!--                &lt;!&ndash;                    <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"&ndash;&gt;-->
<!--                &lt;!&ndash;                                  v-bind:disabled="submitLoading" v-model="answer.answer"></b-form-input>&ndash;&gt;-->
<!--                &lt;!&ndash;                    <div class="validation">&ndash;&gt;-->
<!--                &lt;!&ndash;                      <span>{{ errors[0] }}</span>&ndash;&gt;-->
<!--                &lt;!&ndash;                    </div>&ndash;&gt;-->
<!--                &lt;!&ndash;                    <DialogSecurity></DialogSecurity>&ndash;&gt;-->
<!--                &lt;!&ndash;                  </b-form-group>&ndash;&gt;-->
<!--                &lt;!&ndash;                </ValidationProvider>&ndash;&gt;-->
<!--                &lt;!&ndash;                <b-button variant="primary" type="submit" style="margin-top: 12px;">{{ $t('sidebar.next') }}&ndash;&gt;-->
<!--                &lt;!&ndash;                  <i v-if="submitLoading"&ndash;&gt;-->
<!--                &lt;!&ndash;                     style="padding: 0;margin-left: 5px"&ndash;&gt;-->
<!--                &lt;!&ndash;                     class="el-icon-loading"/>&ndash;&gt;-->
<!--                &lt;!&ndash;                </b-button>&ndash;&gt;-->
<!--              </b-form>-->
<!--            </ValidationObserver>-->

<!--            <ValidationObserver v-if="active === 2" ref="form" v-slot="{ handleSubmit }">-->
<!--              <b-form @submit.stop.prevent="handleSubmit(submitNewPassword)" class="card-body">-->
<!--                <ValidationProvider name="reset_password.label4" rules="required||newPassword" v-slot="{ errors }">-->
<!--                  <b-form-group-->
<!--                    class="col-sm-7"-->
<!--                    :label="$t('reset_password.label4')"-->
<!--                    label-cols-sm="3"-->
<!--                  >-->
<!--                    <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')" type="password"-->
<!--                                  v-model="newPassword.newPassword" style="border-color: #DBDCFC;"></b-form-input>-->
<!--                    <div class="validation">-->
<!--                      <span>{{ errors[0] }}</span>-->
<!--                    </div>-->
<!--                  </b-form-group>-->
<!--                  <b-form-group-->
<!--                    class="col-sm-7"-->
<!--                    label=""-->
<!--                    label-cols-sm="3">-->
<!--                    <div>-->
<!--                      <b-checkbox :disabled="true" v-for="item in validateSelection" v-model="item.checked"-->
<!--                                  :key="item.index">-->
<!--                        {{ item.name }}-->
<!--                      </b-checkbox>-->
<!--                    </div>-->
<!--                  </b-form-group>-->
<!--                </ValidationProvider>-->
<!--                <ValidationProvider name="reset_password.label5" rules="required" v-slot="{ errors }">-->
<!--                  <b-form-group-->
<!--                    class="col-sm-7"-->
<!--                    :label="$t('reset_password.label5')"-->
<!--                    label-cols-sm="3">-->
<!--                    <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')" type="password"-->
<!--                                  v-model="newPassword.newPasswordConfirm"-->
<!--                                  style="border-color: #DBDCFC;"></b-form-input>-->
<!--                    <div class="validation">-->
<!--                      <span>{{ errors[0] }}</span>-->
<!--                    </div>-->
<!--                    <div v-if="inconsistency" class="validation">-->
<!--                      <span>{{ $t('validation.passwordInconsistency') }}</span>-->
<!--                    </div>-->
<!--                  </b-form-group>-->
<!--                </ValidationProvider>-->
<!--                <div v-if="googleRecaptchaVisible" id="google-captcha">-->
<!--                  <GoogleRecaptcha ref="recaptcha" @getValidateCode="clickGoogleCaptcha"-->
<!--                                   key="6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_"></GoogleRecaptcha>-->
<!--                </div>-->
<!--                <ValidationProvider v-else name="reset_password.label6" rules="required" v-slot="{ errors }">-->
<!--                  <b-form-group-->
<!--                    class="col-sm-7"-->
<!--                    :label="$t('reset_password.label6')"-->
<!--                    label-cols-sm="3">-->
<!--                    <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
<!--                                  v-model="newPassword.checkCode" style="border-color: #DBDCFC;"></b-form-input>-->
<!--                    <div class="validation">-->
<!--                      <span>{{ errors[0] }}</span>-->
<!--                    </div>-->
<!--                  </b-form-group>-->
<!--                  <b-form-group-->
<!--                    class="col-sm-7"-->
<!--                    label=""-->
<!--                    label-cols-sm="3">-->
<!--                    <el-image-->
<!--                      :fit="codePictureStyle"-->
<!--                      @click="clickCodeImage"-->
<!--                      :src="codePicture"-->
<!--                      class="captcha-picture"-->
<!--                      v-loading="codeLoading"-->
<!--                      element-loading-spinner="el-icon-loading"></el-image>-->
<!--                  </b-form-group>-->
<!--                </ValidationProvider>-->

<!--                <b-button class="btn-common-shadow" variant="primary" type="submit" style="margin-top: 12px;">-->
<!--                  {{ $t('sidebar.next') }}-->
<!--                  <i v-if="submitLoading"-->
<!--                     style="padding: 0;margin-left: 5px"-->
<!--                     class="el-icon-loading"/>-->
<!--                </b-button>-->
<!--              </b-form>-->
<!--            </ValidationObserver>-->
<!--          </template>-->
<!--        </iq-card>-->
<!--      </b-col>-->
<!--    </b-row>-->
<!--  </b-container>-->
<!--</template>-->

<script>
import { getLocal, removeLocalItem } from '@/Utils/authLocalStorage'
import GoogleRecaptcha from '@/components/MyfxComponent/GoogleRecaptcha'
import { submitPasswordChange, verifyCurrentPassword } from '@/services/account'
import { Message } from 'element-ui'
import AESCrypto from '@/Utils/AESCrypto'
import auth from '@/services/auth'
import { mapGetters } from 'vuex'
import { passwordValid } from '@/Utils/NewPasswordValidater'
import { getQueryAll, questionVerification } from '@/services/securityQuestionContent'
import vm from '@/main.js'
import DialogSecurity from '@/components/MyfxComponent/DialogSecurity'

export default {
  name: 'ResetCoPassword',
  components: {
    GoogleRecaptcha,
    DialogSecurity
  },
  data () {
    return {
      form: {
        currentPassword: '',
        answer: '',
        question: '',
        newPassword: '',
        confirmPassword: '',
        code: ''
      },
      check1: false,
      check2: false,
      check3: false,
      check4: false,
      check5: true,
      status: {
        current: 0,
        answer: 0,
        newPassword: 0,
        confirmPassword: 0
      },
      formRules: {},
      captchaLoading: false,

      // --------------------------

      active: 0,
      stepOneButtonShow: false,
      submitLoading: false,
      questionLoading: false,
      googleRecaptchaVisible: false,
      currentPassword: '',
      codeLoading: false,
      inconsistency: false,
      securityQuestion: {
        code: 0,
        lang: '',
        question: ''
      },
      answer: {
        answer: '',
        code: 0,
        myguid: ''
      },
      newPassword: {
        newPassword: '',
        newPasswordConfirm: '',
        checkCode: ''
      },
      codePicture: '',
      codePictureStyle: 'contains',
      validateNewPassword: [],
      captchaType: 'cloudflare' // 默认使用cloudflare
      // validateSelection: [
      //   {
      //     index: 0,
      //     name: this.$t('reset_password.eight_character'),
      //     checked: false
      //   },
      //   {
      //     index: 1,
      //     name: this.$t('reset_password.one_number'),
      //     checked: false
      //   },
      //   {
      //     index: 2,
      //     name: this.$t('reset_password.one_lowerCase'),
      //     checked: false
      //   },
      //   {
      //     index: 3,
      //     name: this.$t('reset_password.one_upperCase'),
      //     checked: false
      //   },
      // ]
    }
  },
  watch: {
    active (newVal, oldVal) {
      if (newVal === 2) {
        this.getCheckCode()
      }
    },
    'newPassword.newPassword': {
      handler (newVal, oldVal) {
        this.validateNewPassword = passwordValid(newVal)
        this.validateSelection.forEach(item => {
          item.checked = this.validateNewPassword.includes(item.name)
        })
      }
    },
    'newPassword.newPasswordConfirm': {
      handler (newVal, oldVal) {
        this.inconsistency = !(newVal === this.newPassword.newPassword && newVal !== null)
      }
    },
    '$i18n.locale' (newValue) {
      console.log(newValue)
      if (this.active === 1) {
        this.getCOQuestions()
      }
    }
  },
  computed: {

    ...mapGetters({
      stateCaptcha: 'Setting/captchaState'
    }),

    validateSelection () {
      return [
        { index: 0, name: this.$t('reset_password.eight_character'), checked: false },
        { index: 1, name: this.$t('reset_password.one_number'), checked: false },
        { index: 2, name: this.$t('reset_password.one_lowerCase'), checked: false },
        { index: 3, name: this.$t('reset_password.one_upperCase'), checked: false }
      ]
    }
  },

  mounted () {
    this.getCheckCode()
  },
  methods: {

    async verifyCurrentPasswordHandle () {
      try {
        if (!this.form.currentPassword.trim()) {
          this.status.current = 0
          this.form.question = ''
          return
        }
        this.status.current = 1
        const myguid = JSON.parse(getLocal('user')).myguid
        const currentPassword = AESCrypto.Encrypt(this.form.currentPassword)
        await verifyCurrentPassword({ myguid, currentPassword })
        this.status.current = 2
        this.getCOQuestions()
      } catch (err) {
        this.status.current = 3
        this.form.question = ''
        console.log(err)
      }
    },

    async verifySecurityQuestionHandle () {
      try {
        if (!this.form.answer.trim()) {
          this.status.answer = 0
          return
        }
        this.status.answer = 1
        const myguid = JSON.parse(getLocal('user')).myguid
        await questionVerification(myguid, this.form.answer)
        this.status.answer = 2
      } catch (err) {
        this.status.answer = 3
        console.log(err)
      }
    },

    newPasswordInput (val) {
      this.check1 = /[A-Z]/.test(val)
      this.check2 = /[a-z]/.test(val)
      this.check3 = /\d/.test(val)
      this.check4 = val.length >= 8
      this.check5 = val.trim() ? /^[\w!@#.]+$/.test(val) : true
    },

    verifyConfirmPassword () {
      console.log(this.form.confirmPassword, !this.form.confirmPassword.trim())
      if (!this.form.confirmPassword.trim()) return this.status.confirmPassword = 0
      if (!/^[\w!@#.]{8,}$/g.test(this.form.newPassword)) return console.log('密码不规范')
      if (this.form.newPassword !== this.form.confirmPassword) return this.status.confirmPassword = 3
      if (this.form.newPassword === this.form.confirmPassword) return this.status.confirmPassword = 2
    },

    next () {
      if (this.active === 0) {
        this.stepOneButtonShow = true
        setTimeout(() => {
          this.stepOneButtonShow = false
          this.active++
        }, 1000)
      } else if (this.active++ > 2) this.active = 0
    },
    _verifyCurrentPassword () {
      this.$vp.add({ message: '[ResetCoPassword] Verify current password.' })
      const myguid = JSON.parse(getLocal('user')).myguid
      const currentPassword = AESCrypto.Encrypt(this.currentPassword)
      // console.log(currentPassword)
      this.submitLoading = true
      verifyCurrentPassword({
        myguid,
        currentPassword
      }).then(res => {
        this.submitLoading = false
        this.next()
        this.getCOQuestions()
      }).catch(error => {
        this.submitLoading = false
      })
    },
    getCOQuestions () {
      this.questionLoading = true
      const myguid = JSON.parse(getLocal('user')).myguid
      this.status.answer = 1
      getQueryAll(myguid).then(res => {
        this.securityQuestion = res.data
        this.form.question = res.data.question
        this.questionLoading = false
        this.status.answer = 0
      }).catch(error => {
        this.status.answer = 0
        this.questionLoading = false
      })
    },
    _verifySecurityQuestion () {
      this.$vp.add({ message: '[ResetCoPassword] Verify Security question.' })

      this.submitLoading = true
      this.answer.myguid = JSON.parse(getLocal('user')).myguid
      this.answer.code = this.securityQuestion.code
      questionVerification(this.answer.myguid, this.answer.answer).then(res => {
        vm.$message.success(res.msg)
        this.submitLoading = false
        this.next()
      }).catch(error => {
        this.submitLoading = false
        this.securityQuestion.question = error.question
      })
    },
    getCheckCode () {
      this.captchaLoading = true
      this.googleRecaptchaVisible = false
      auth.getCaptcha().then(res => {
        // 开发环境强制使用cloudflare
        if (process.env.NODE_ENV === 'development') {
          this.captchaType = 'cloudflare'
          this.googleRecaptchaVisible = true
          return
        }

        // 生产环境根据后端返回的type选择
        if (res.type === 'googleCaptcha') {
          this.captchaType = 'googleCaptcha'
          this.googleRecaptchaVisible = true
          return
        } else {
          this.captchaType = 'cloudflare'
          this.googleRecaptchaVisible = true
          return
        }

        // 兼容旧的图片验证码逻辑（如果后端没有返回type或者返回其他值）
        if (this.stateCaptcha.token) {
          this.$store.dispatch('Setting/authCaptchaAction', {
            codeImage: '',
            token: ''
          })
        }
        const codeData = res.data
        if (!codeData) {
          Message.error('Error: Get check code error!')
          return
        }
        this.$store.dispatch('Setting/authCaptchaAction', {
          codeImage: codeData.codeImage,
          token: codeData.token
        })
        this.codePicture = this.stateCaptcha.codeImage
        this.captchaLoading = false
      })
    },
    clickGoogleCaptcha (val) {
      this.$store.dispatch('Setting/authCaptchaAction', {
        codeImage: '',
        token: val
      })
    },
    clickCodeImage () {
      this.getCheckCode()
    },

    submitNewPassword () {
      this.$vp.add({ message: '[ResetCoPassword] Confirm reset password.' })
      if (this.inconsistency) {
        // console.log(this.inconsistency)
        return
      }
      this.submitLoading = true
      const newPassword = AESCrypto.Encrypt(this.form.newPassword)
      const email = JSON.parse(getLocal('user')).email
      // const code = this.newPassword.checkCode
      const code = this.form.code
      const token = this.stateCaptcha.token
      submitPasswordChange({
        email,
        newPassword
      }, code, token).then(res => {
        this.stepOneButtonShow = true
        setTimeout(() => {
          this.submitLoading = false
          this.stepOneButtonShow = false
          removeLocalItem('user')
          this.$router.push({
            path: '/auth/sign-in1',
            query: { param: 'resetCOPassWdSuccess' }
          })
        }, 1000)
      }).catch(error => {
        // this.stepOneButtonShow = true
        // setTimeout(() => {
        //   this.submitLoading = false
        //   this.stepOneButtonShow = false
        // }, 1000)

        this.submitLoading = false
        this.getCheckCode()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  //align-items: center;
  //justify-content: center;
  min-height: 76vh;
  padding: 60px 20px 20px;
  border-radius: 10px;
  background-color: #fff;

  .content {
  }

  .el-form-item {
    margin-bottom: 28px !important;
  }

  .ipt-row {
    position: relative;

    .ipt-label {
      pointer-events: none;
      padding: 0 10px 0 6px;
      position: absolute;
      top: -10px;
      //left: 20px;
      left: 50%;
      transform: translateX(-50%);
      height: 20px;
      line-height: 20px;
      font-size: 10px;
      z-index: 10;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 2px;
        z-index: -1;
        background-color: #fff;
      }
    }
  }

  .DialogSecurity {
    ::v-deep .reset {
      margin: 0 !important;
      line-height: 20px;
    }
  }
}

@media screen and (max-width: 767px) {
  .app-container {
    padding: 20px !important;
  }
}

.step-one {
  position: relative;
  width: 90px;
  overflow: hidden;

  span {
    top: 0;
    left: 0;
    position: absolute;
    width: 100%;
    transition: all .3s;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: -20px;
      width: 0px;
      height: 0px;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transition: all .3s;
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  }

  &:focus {
    box-shadow: 0 16px 5px -13px rgba(0, 0, 0, 0.5) !important;
  }
}

.step-one-ok {
  border-color: #53B502 !important;
  background: #53B502 !important;

  span {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #53B502;

    &::before {
      top: 44%;
      width: 20px;
      height: 10px;
    }
  }
}

.el-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-input {
  ::v-deep {
    input {
      text-align: center;
      width: 300px !important;
    }
  }
}

.el-checkbox {
  margin-right: 10px;

  ::v-deep .el-checkbox__label {
    font-size: 10px !important;
  }
}


@media screen and (max-width: 767px) {
  .newPassword,
  .code,
  .question {
    flex-direction: column;
    align-items: start !important;
  }
}
</style>

<!--<style lang="scss">-->
<!--.validation {-->
<!--  width: 100%;-->
<!--  margin-top: .25rem;-->
<!--  font-size: 80%;-->
<!--  color: #dc3545-->
<!--}-->

<!--.captcha-picture {-->
<!--  padding-top: 2px;-->
<!--  padding-bottom: 5px;-->
<!--}-->

<!--.question {-->
<!--  overflow: hidden;-->

<!--  &-bar {-->
<!--    padding: 0 30px;-->
<!--    color: #495EEB;-->
<!--    line-height: 50px;-->
<!--    background-color: #EDF2FF;-->
<!--  }-->

<!--  &-title {-->
<!--    margin-top: 30px;-->
<!--    margin-left: 30px;-->
<!--  }-->

<!--  &-reset {-->
<!--    margin-top: 10px;-->
<!--    margin-left: 30px;-->
<!--    cursor: pointer;-->
<!--    color: #F88407;-->
<!--    text-decoration: underline;-->
<!--  }-->

<!--  &-form {-->
<!--    margin: 20px 30px 0;-->
<!--    //width: 400px;-->

<!--    &-ipt {-->
<!--      padding: 0 10px;-->
<!--      width: 100%;-->
<!--      height: 40px;-->
<!--      border-radius: 4px;-->
<!--      border: 1px solid #DBDCFC;-->
<!--    }-->

<!--    &-btn {-->
<!--      margin-top: 16px;-->
<!--      display: flex;-->
<!--      align-items: center;-->
<!--      justify-content: space-between;-->

<!--      &-back {-->
<!--        cursor: pointer;-->
<!--        padding: 2px 10px;-->
<!--        color: #495EEB;-->
<!--        border-radius: 4px;-->
<!--        border: 1px solid #495EEB;-->
<!--      }-->

<!--      &-next {-->
<!--        cursor: pointer;-->
<!--        padding: 2px 10px;-->
<!--        border-radius: 4px;-->
<!--        color: #fff;-->
<!--        background-color: #495EEB;-->
<!--      }-->
<!--    }-->
<!--  }-->
<!--}-->

<!--@media screen and (max-width: 720px) {-->
<!--  .card-body {-->
<!--    padding: 0;-->
<!--  }-->
<!--}-->


<!--</style>-->

<!--<style>-->
<!--.el-step__description {-->
<!--  margin-top: 5px;-->
<!--}-->
<!--</style>-->
