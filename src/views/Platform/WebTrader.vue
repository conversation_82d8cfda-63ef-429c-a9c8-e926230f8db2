<template>
  <div class="card-body">

    <el-link type="primary" href="https://clientoffice.myfxmarkets.com/webTerminal.html" target="_blank"> {{$t('sidebar.link')}}</el-link>
    {{$t('platform.trader_test2')}}
  </div>
</template>

<script>
export default {
  name: 'WebTrader',
  data () {
    return {}
  },
  methods: {
    open () {
      let tempwindow = window.open('_blank')
      tempwindow.location = 'https://clientoffice.myfxmarkets.com/webTerminal.html'

    }
  },
  created () {
    this.open()
  }
}
</script>

<style scoped>

</style>
