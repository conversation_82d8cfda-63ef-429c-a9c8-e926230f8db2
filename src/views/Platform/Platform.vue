<template>
  <!--  <b-container fluid>-->
  <!--    <b-row>-->
  <!--      <b-col cols="12">-->
  <!--        <iq-card>-->
  <!--          <template v-slot:headerTitle>-->
  <!--            <div class="hidden-sm-and-down">-->
  <!--              <button :class="mt4DownloadIndex  ? 'mt4DownloadOne' : 'mt4Download' " @click="mt4DownloadClick">-->
  <!--                {{ $t('platform.button_test1') }}-->
  <!--              </button>-->
  <!--              <button :class="mt5DownloadIndex ? 'mt5DownloadOne': 'mt5Download'" @click="mt5DownloadClick">-->
  <!--                {{ $t('platform.button_test2') }}-->
  <!--              </button>-->
  <!--              <button :class="webTraderIndex? 'webTraderOne' : 'webTrader'" @click="webTraderClick">-->
  <!--                {{ $t('platform.button_test3') }}-->
  <!--              </button>-->
  <!--            </div>-->
  <!--            <wd-radio-group v-model="value" style="text-align: center;" class="hidden-md-and-up">-->
  <!--              <wd-radio value="mt4" shape="button" style="margin-top: 6px;" @change="mt4DownloadClick">-->
  <!--                {{ $t('platform.wd_button_test1') }}-->
  <!--              </wd-radio>-->
  <!--              <wd-radio value="mt5" shape="button" style="margin-top: 6px;" @change="mt5DownloadClick">-->
  <!--                {{ $t('platform.wd_button_test2') }}-->
  <!--              </wd-radio>-->
  <!--              <wd-radio value="webTrader" shape="button" style="margin-top: 6px;" @change="webTraderClick">-->
  <!--                {{ $t('platform.button_test3') }}-->
  <!--              </wd-radio>-->
  <!--            </wd-radio-group>-->
  <!--          </template>-->
  <!--          <template v-slot:body>-->
  <!--            <div v-if="mt4DownloadActive">-->
  <!--              <Mt4Download></Mt4Download>-->
  <!--            </div>-->
  <!--            <div v-if="mt5DownloadActive">-->
  <!--              <Mt5Download></Mt5Download>-->
  <!--            </div>-->
  <!--            <div v-if="webTraderActive">-->
  <!--              <WebTrader></WebTrader>-->
  <!--            </div>-->
  <!--          </template>-->
  <!--        </iq-card>-->
  <!--      </b-col>-->
  <!--    </b-row>-->
  <!--  </b-container>-->

  <div class="app-container">
    <div class="type">
      <div
        class="type-item btn-common-shadow"
        :class="{ 'type-active': item.id === typeActive }"
        v-for="item in typeOption"
        :key="item.id"
        v-viewpoint.click="{ message: `[Platform] Click tab ${item.name}` }"
        @click="typeChange(item.id)"
      >
        <img :src="item.icon">
        {{ $t('platform.type.' + item.name) }}
      </div>
    </div>

    <div>{{ $t('platform.hint_01') }}</div>
    <div>{{ $t('platform.hint_02') }}</div>
    <!--    <div style="margin-top: 10px;">-->
    <!--      WebTrader-->
    <!--      <a-->
    <!--        target="_blank"-->
    <!--        href="https://clientoffice.myfxmarkets.com/webTerminal.html">-->
    <!--        {{ $t('platform.goto') }}-->
    <!--      </a>-->
    <!--    </div>-->

    <!--    <div class="content">-->
    <!--      <div class="platform">-->
    <!--        <div class="platform-row">-->
    <!--          <div class="platform-item"-->
    <!--               v-for="item in (typeActive === 1 ? MT4PlatformList : MT5PlatformList)"-->
    <!--               :key="item.id">-->
    <!--            <div class="platform-item-bg"></div>-->
    <!--            <div class="platform-item-box" style="position:relative; z-index: 10;width: 100%;height: 100%;">-->
    <!--              <div class="platform-item-icon-box">-->
    <!--                <img class="platform-item-icon" :src="item.icon">-->
    <!--              </div>-->
    <!--              <div class="platform-item-title">-->
    <!--                <span>{{ $t(item.client) }}</span>-->
    <!--                <div style="text-align: center">{{ item.system }}</div>-->
    <!--              </div>-->
    <!--              <div-->
    <!--                v-viewpoint.click="{ message: `[Platform] Click download ${item.system} ${typeActive == 1 ? 'MT4' : 'MT5'}.` }"-->
    <!--                class="platform-item-btn"-->
    <!--                @click="downloadFile(item.url)"-->
    <!--              >-->
    <!--                {{ item.id === 5 ? $t('platform.open') : $t('platform.download') }}-->
    <!--              </div>-->
    <!--            </div>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <b-col xl="4" lg="4" md="12" sm="12" xm="12" class="placeholder hidden-sm-and-down" style="height: 560px">-->
    <!--        <img :src="placeholder" style="width: 230px; height: 400px;position: absolute;top: -120px; right: 20px">-->
    <!--      </b-col>-->
    <!--    </div>-->

    <b-row class="content">
      <b-col xl="12" lg="12" md="12" sm="12" xm="12" class="platform">
        <b-form-row style="gap: 20px;width: 100%;flex-wrap: unset">
          <b-col xl="5" lg="5" md="12" sm="12" xm="12" class="platform-item"
                 v-for="item in (typeActive === 1 ? MT4PlatformList : MT5PlatformList)"
                 :key="item.id">
            <img width="60" class="platform-item-icon" :src="item.icon">
            <div class="platform-item-title">
              <span>{{ $t(item.client) }}</span>
              <div
                style="text-align: center"
                :style="{
                  opacity: item.system === 'ALL' ? 0 : 1
                }"
              >{{ item.system }}
              </div>
            </div>
            <div
              v-viewpoint.click="{ message: `[Platform] Click download ${item.system} ${typeActive == 1 ? 'MT4' : 'MT5'}.` }"
              class="platform-item-btn btn-common-shadow"
              style="padding: 0;"
              @click="downloadFile(item.url)"
            >
              {{ item.id === 5 ? $t('platform.open') : $t('platform.download') }}
            </div>
          </b-col>
        </b-form-row>
      </b-col>
      <!--      <b-col xl="4" lg="4" md="12" sm="12" xm="12" class="placeholder hidden-sm-and-down" style="height: 560px">-->
      <!--        <img :src="placeholder" style="width: 230px; height: 400px;position: absolute;top: -120px; right: 20px">-->
      <!--      </b-col>-->
    </b-row>
  </div>
</template>

<script>
// import Mt4Download from '@/views/Platform/Mt4Download'
// import Mt5Download from '@/views/Platform/Mt5Download'
// import WebTrader from '@/views/Platform/WebTrader'

export default {
  name: 'Platform',
  components: {
    // WebTrader,
    // Mt5Download,
    // Mt4Download
  },
  data () {
    return {
      placeholder: require('@/assets/images/placholder1.png'),
      typeActive: 1,
      typeOption: [
        { id: 1, name: 'MT4 Download', icon: require('@/assets/images/mt-icon.png') },
        { id: 2, name: 'MT5 Download', icon: require('@/assets/images/mt-icon.png') }
      ],

      MT4PlatformList: [
        {
          id: 1,
          client: 'platform.desktop',
          system: 'WINDOWS',
          icon: require('@/assets/images/p-win.png'),
          url: 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/mt_assets/myfx4setup.exe'
        },
        // {
        //   id: 2,
        //   client: 'platform.desktop',
        //   system: 'MAC',
        //   icon: require('@/assets/images/p-mac.png'),
        //   url: 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/mt_assets/MetaTrader4.pkg.zip'
        // },
        {
          id: 3,
          client: 'platform.mobile',
          system: 'ANDROID',
          icon: require('@/assets/images/p-and.png'),
          url: 'https://play.google.com/store/apps/details?id=net.metaquotes.metatrader4'
        },
        {
          id: 4,
          client: 'platform.mobile',
          system: 'IOS',
          icon: require('@/assets/images/p-ios.png'),
          url: 'https://apps.apple.com/au/app/metatrader-4-forex-trading/id496212596'
        },
        {
          id: 5,
          client: 'Web Trader',
          system: 'ALL',
          icon: require('@/assets/images/p-mn.png'),
          url: 'https://clientoffice.myfxmarkets.com/mt4WebTerminal.html'
        }
      ],

      MT5PlatformList: [
        {
          id: 1,
          client: 'platform.desktop',
          system: 'WINDOWS',
          icon: require('@/assets/images/p-win.png'),
          url: 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/mt_assets/myfxmarkets5setup.exe'
        },
        // {
        //   id: 2,
        //   client: 'platform.desktop',
        //   system: 'MAC',
        //   icon: require('@/assets/images/p-mac.png'),
        //   url: 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/mt_assets/MetaTrader5.pkg.zip'
        // },
        {
          id: 3,
          client: 'platform.mobile',
          system: 'ANDROID',
          icon: require('@/assets/images/p-and.png'),
          url: 'https://download.mql5.com/cdn/mobile/mt5/android?server=MyFXMarkets-Live'
        },
        {
          id: 4,
          client: 'platform.mobile',
          system: 'IOS',
          icon: require('@/assets/images/p-ios.png'),
          url: 'https://download.mql5.com/cdn/mobile/mt5/ios?server=MyFXMarkets-Live'
        },
        {
          id: 5,
          client: 'Web Trader',
          system: 'ALL',
          icon: require('@/assets/images/p-mn.png'),
          url: 'https://clientoffice.myfxmarkets.com/mt5WebTerminal.html'
        }
      ],

      value: 'mt4',
      index: true,
      mt4DownloadActive: false,
      mt5DownloadActive: false,
      webTraderActive: false,
      mt4DownloadIndex: true,
      mt5DownloadIndex: true,
      webTraderIndex: true
    }
  },
  watch: {
    'value' (newVal, oldVal) {
      if (newVal == 'mt4') {
        this.mt4DownloadClick()
      } else if (newVal == 'mt5') {
        this.mt5DownloadClick()
      } else if (newVal == 'webTrader') {
        this.webTraderClick()
      }
    }
  },
  created () {
    this.mt4DownloadClick()
  },
  methods: {

    /**
     * @description 平台选择
     * <AUTHOR>
     */
    typeChange (id) {
      if (id === this.typeActive) return
      this.typeActive = id
    },

    /**
     * @description 下载文件
     * <AUTHOR>
     */
    downloadFile (url) {
      window.open(url)
    },

    mt4DownloadClick () {
      this.mt4DownloadIndex = true
      this.mt4DownloadActive = true
      this.mt5DownloadActive = false
      this.webTraderActive = false
      this.mt5DownloadIndex = false
      this.webTraderIndex = false
    },
    mt5DownloadClick () {
      this.mt4DownloadIndex = false
      this.mt4DownloadActive = false
      this.mt5DownloadActive = true
      this.mt5DownloadIndex = true
      this.webTraderActive = false
      this.webTraderIndex = false
    },
    webTraderClick () {
      this.mt4DownloadIndex = false
      this.mt4DownloadActive = false
      this.mt5DownloadActive = false
      this.mt5DownloadIndex = false
      this.webTraderActive = true
      this.webTraderIndex = true
    }
  }
}
</script>

<style scoped lang="scss">
.webTrader,
.mt5Download,
.mt4Download {
  border: 1px #DCDFE6 solid;
  background-color: #FFFFFF;
  margin-left: 10px;
  height: 50px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
}

.webTrader {
  width: 180px;
}

.mt5Download {
  width: 180px;
}

.mt4Download {
  width: 180px;
}

.webTrader:hover {
  font-weight: 1000;
  background-color: #ebeefb;
}

.mt5Download:hover {
  font-weight: 1000;
  background-color: #ebeefb;
}

.mt4Download:hover {
  font-weight: 1000;
  background-color: #ebeefb;
}

.mt4DownloadOne, .mt5DownloadOne, .webTraderOne {
  border: 1px #DCDFE6 solid;
  margin-left: 10px;
  height: 50px;
  width: 180px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
  font-weight: 1000;
  background-color: #ebecfb;
}

// 2024-04-17
.app-container {
  margin: 0 15px;
  padding: 30px 30px 100px;
  border-radius: 5px;
  background-color: #fff;
  //background-color: #F8FAFF;
  box-shadow: 0px 2px 8px rgba(191, 190, 253, 0.07), 0px 4px 16px rgba(193, 190, 253, 0.07);

  .type {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    line-height: 44px;
    font-size: 12px;

    &-item {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      padding: 0 10px;
      margin-right: 14px;
      color: #333;
      border: 1px solid #DBDCFC;
      border-radius: 8px;
      white-space: nowrap;
      background-color: #fff;

      img {
        margin-right: 10px;
        width: 24px;
      }

      &:nth-child(2) {
        margin-right: 0;
      }
    }

    &-active {
      color: #fff;
      border-color: #495EEB;
      background-color: #495EEB;
    }
  }

  .content {
    margin-top: 10px;
    align-items: center;
  }

  ::v-deep .platform {
    display: flex;

    &-item {
      //margin-right: 20px;
      //margin-bottom: 20px;
      flex: 1;
      padding: 30px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      //border: 1px solid #495EEB;

      &:hover {
        transform: translateY(-6px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      &:nth-child(2n) {
        margin-right: 0;
      }

      &-icon {

      }

      &-title {
        margin: 8px 0 20px 0;
        font-weight: bold;
        word-break: break-all;
        text-align: center;
        white-space: nowrap;

        span {
          color: #495EEB;
        }

        div {
          font-weight: normal;
        }
      }

      &-btn {
        cursor: pointer;
        width: 130px;
        color: #fff;
        text-align: center;
        background-color: #495EEB;
        line-height: 36px;
        border-radius: 5px;
      }
    }

    @media screen and (max-width: 1200px) {
      .form-row {
        flex-wrap: wrap !important;
      }

      .platform-item {
        flex: unset;
        width: 48.5% !important;
      }
    }

    @media screen and (max-width: 768px) {

      .form-row {
        flex-wrap: wrap !important;
      }

      .platform-item {
        flex: unset;
        width: 100% !important;
      }
    }
  }
}

</style>
