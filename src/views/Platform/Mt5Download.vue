<template>
  <b-container fluid>
    <b-row>
      <b-col>
        <iq-card>
          <template v-slot:headerTitle>
            <h4 class="card-title">
              <el-image :src="require('@/assets/images/metatrader-5-platform-logo-200x200.png')"></el-image>
            </h4>
          </template>
          <template v-slot:body>
            <el-row class="card-body hidden-sm-and-down">
              <h5 class="card-title">{{$t('platform.title1')}}</h5>
              <DownloadButton button-name="Windows" right-icon-class="el-icon-download" :link-to="linkTo"
                link="https://clientoffice.myfxmarkets.com/files/myfxmarkets5setup.exe" :icon-class="windowsIconClass">
              </DownloadButton>
              <DownloadButton button-name="Mac" right-icon-class="el-icon-download" :link-to="linkTo"
                link="https://myfxmarkets.com/tradingDesk/MetaTrader5.dmg" :icon-class="appleIconClass">
              </DownloadButton>
            </el-row>

            <el-row class="card-body hidden-md-and-up">
              <h5 class="card-title">{{$t('platform.title1')}}</h5>
              <b-col style="text-align: center; margin-top: 30px;">
                <i class="el-icon-windows"></i>
                <span> {{$t('platform.desktop_laptop')}}</span></br>
                <span style="font-size: 25px; font-weight: 600px;"> {{$t('platform.for_windows')}}</span></br>
                <b-button @click="linkTo('https://clientoffice.myfxmarkets.com/files/myfxmarkets5setup.exe')"variant="primary">
                  {{$t('dropdown.download')}}
                </b-button>
              </b-col>

              <b-col style="text-align: center; margin-top: 30px;">
                <i class="el-icon-mac"></i>
                <span> {{$t('platform.desktop_laptop')}}</span></br>
                <span style="font-size: 25px; font-weight: 600px;"> {{$t('platform.for_mac')}}</span></br>
                <b-button @click="linkTo('https://myfxmarkets.com/tradingDesk/MetaTrader5.dmg')"
                  variant="primary">
                  {{$t('dropdown.download')}}
                </b-button>
              </b-col>
            </el-row>

            <!--            <el-row class="card-body">
                          <h5 class="card-title">WebTrader</h5>
                          <DownloadButton
                            button-name="WebTrader MT5"
                            right-icon-class="fa fa-link"
                            :link-to="linkTo"
                            link="https://clientoffice.myfxmarkets.com/webTerminal.html"
                            icon-class="fa fa-window-maximize"
                          ></DownloadButton>
                        </el-row>-->
            <el-row class="card-body hidden-sm-and-down">
              <h5 class="card-title">{{$t('platform.title2')}}</h5>
              <DownloadButton button-name="Android" right-icon-class="el-icon-download" :link-to="linkTo"
                link="https://download.mql5.com/cdn/mobile/mt5/android?server=MyFXMarkets-Live"
                icon-class="fab fa-google-play"></DownloadButton>
              <DownloadButton button-name="IOS" right-icon-class="el-icon-download" :link-to="linkTo"
                link="https://download.mql5.com/cdn/mobile/mt5/ios?server=MyFXMarkets-Live"
                icon-class="fab fa-app-store-ios"></DownloadButton>
            </el-row>

            <el-row class="card-body hidden-md-and-up">
              <h5 class="card-title">{{$t('platform.title2')}}</h5>
              <b-col style="text-align: center; margin-top: 30px;">
                <i class="el-icon-android"></i>
                <span> {{$t('platform.smartphone_tablet')}}</span></br>
                <span style="font-size: 25px; font-weight: 600px;"> {{$t('platform.for_android')}}</span></br>
                <b-button @click="linkTo('https://download.mql5.com/cdn/mobile/mt5/android?server=MyFXMarkets-Live')"variant="primary">
                  {{$t('dropdown.download')}}
                </b-button>
              </b-col>

              <b-col style="text-align: center; margin-top: 30px;">
                <i class="el-icon-ios"></i>
                <span> {{$t('platform.smartphone_tablet')}}</span></br>
                <span style="font-size: 25px; font-weight: 600px;"> {{$t('platform.for_IOS')}}</span></br>
                <b-button @click="linkTo('https://download.mql5.com/cdn/mobile/mt5/ios?server=MyFXMarkets-Live')" variant="primary">
                  {{$t('dropdown.download')}}
                </b-button>
              </b-col>
            </el-row>
          </template>
        </iq-card>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>
  import DownloadButton from '@/components/MyfxComponent/DownloadButton'

  export default {
    name: 'Mt5Download',
    components: {
      DownloadButton
    },
    data() {
      return {
        windowsIconClass: 'fab fa-windows',
        appleIconClass: 'fab fa-apple'
      }
    },
    methods: {
      linkTo(link) {
        window.open(link, '_blank')
      }
    }
  }
</script>

<style lang="scss">
  .el-icon-windows {
    background: url(~@/assets/images/platform/icon_windows.png) center no-repeat;
    /*使用自己的图片来替换*/
    width: 100%;
    background-size: contain;
  }

  .el-icon-windows:before {
    content: "dog";
    font-size: 40px;
    visibility: hidden;
  }


  .el-icon-mac {
    background: url(~@/assets/images/platform/icon_mac.png) center no-repeat;
    /*使用自己的图片来替换*/
    width: 100%;
    background-size: contain;
  }

  .el-icon-mac:before {
    content: "dog";
    font-size: 40px;
    visibility: hidden;
  }


  .el-icon-ios {
    background: url(~@/assets/images/platform/icon_ios.png) center no-repeat;
    /*使用自己的图片来替换*/
    width: 100%;
    background-size: contain;
  }

  .el-icon-ios:before {
    content: "dog";
    font-size: 40px;
    visibility: hidden;
  }


  .el-icon-android {
    background: url(~@/assets/images/platform/icon_android.png) center no-repeat;
    /*使用自己的图片来替换*/
    width: 100%;
    background-size: contain;
  }

  .el-icon-android:before {
    content: "dog";
    font-size: 40px;
    visibility: hidden;
  }
</style>
