<template>
  <div class="promotion">
    <div class="tab">
      <div
        class="tab-item btn-common-shadow"
        :class="{ 'tab-active': item.id === tabActive }"
        v-for="item in tabOption"
        :key="item.id"
        v-viewpoint.click="{ message: `[News] Click ${item.en} button.` }"
        @click="tabChange(item.id)"
      >
        <img :src="item.id === tabActive ? item.iconSelect : item.icon">
        {{ $t(item.name) }}
      </div>
    </div>

<!--    <div v-loading="loading">
      <div v-if="filterList.length">
        <div class="promotion-box" v-for="item in filterList" :key="item.id">
          <div class="container-box">
            <div v-if="item.img" class="left" style="border-radius: 5px !important;overflow: hidden">
              <img v-if="item.isImgUrl" :src="item.img" alt="" @error="changeUrl">
              <img v-else :src="item.img + $i18n.locale + '.png'" alt="" @error="changeUrl">
            </div>
            <div class="left" v-else style="background-color: #d9ccf4">
              {{ item.imgTitle }}
            </div>
            <div class="center">
              <p class="title" v-html="$t(item.title)"></p>
              <P v-if="item.tag" class="tag">{{ $t('promotion.activityList.permission') }}</P>
              <P v-if="item.halloTag" class="tag">{{ $t('promotion.halloween.tag') }}</P>
              <P class="desc" v-html="$t(item.desc)"></p>
            </div>
            <div class="right">
              &lt;!&ndash;          <div class="view" @click="viewDetail">VIEW PROMOTION</div>&ndash;&gt;
              <div class="view bubbly-button animate" @click="viewDetail(item)">
                {{ $t(item.buttonText) }}

                <img v-if="item.isExpired" class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="">
              </div>
              <div v-if="item.countdown" class="time">
                &lt;!&ndash;            <div class="day">Offer Ends in <span style="color: red; font-weight: 700">{{ item.day }} days</span></div>&ndash;&gt;
                <div v-if="!item.isExpired" class="day"
                     v-html="$t('promotion.activityList.endDay', { day: item.day })"></div>
                &lt;!&ndash;            <div class="countdown">{{ item.time }}</div>&ndash;&gt;
              </div>
              &lt;!&ndash;            <img v-if="item.isExpired" class="expired" src="@/assets/images/promotion/expired.gif" alt="">&ndash;&gt;
            </div>
          </div>
        </div>
      </div>

      &lt;!&ndash;    display: flex; align-items: center; justify-content: center;&ndash;&gt;
      <div v-else style="width: 100%; height: 100%; font-size: 20px; margin-top: 20px">{{ $t('promotion.notHave') }}</div>
    </div>-->

    <div  v-loading="loading" >
      <div class="promotion-container" v-if="filterList.length">
        <div class="promotion-container-item"
             v-for="item in filterList">
          <div class="promotion-container-item-top" v-if="item.img" >
            <img v-if="item.isImgUrl" :src="item.img" alt="" @error="changeUrl">
            <img v-else :src="item.img + $i18n.locale + '.png'" alt="" @error="changeUrl">
            <div
              class="expired"
              :style="{ backgroundColor: new Date(item.countdown) > new Date() || !item.countdown ? '#79FB9D' : '#212172',
              color: new Date(item.countdown) > new Date() || !item.countdown ? 'black' : 'white'}">
              {{ new Date(item.countdown) > new Date() || !item.countdown ? $t('promotion.tab.inProcess') : $t('promotion.tab.expired') }}
            </div>
          </div>
          <div class="promotion-container-item-content">
<!--            <P v-if="item.halloTag" class="tag">{{ $t('promotion.halloween.tag') }}</P>-->
            <p class="title" v-html="$t(item.title)"></p>
            <P v-if="item.tag" class="tag">{{ $t('promotion.activityList.permission') }}</P>
            <P class="desc" v-html="$t(item.desc)"></p>
          </div>

          <div style="display: flex; justify-content: center; align-items: center;">
            <div class="view bubbly-button animate" @click="viewDetail(item)">
              {{ $t(item.buttonText) }}
            </div>
          </div>

          <div class="expired-mask" v-if="new Date(item.countdown) < new Date() && item.countdown" ></div>
        </div>
      </div>

      <div v-else style="width: 100%; height: 100%; font-size: 20px; margin-top: 20px">{{ $t('promotion.notHave') }}</div>
    </div>


  </div>
</template>

<script>
import { checkPromotionAuth, getWelcomeBonus } from '@/services/promotion'
import { getLocal } from '@/Utils/authLocalStorage'

export default {
  data () {
    return {
      list: [],
      filterList: [],
      isShow: false,
      isVerify: true,
      tabActive: 1,
      tabOption: [
        {
          id: 1,
          en: 'ALL',
          name: 'promotion.tab.all',
          icon: require('@/assets/images/promotion/all.png'),
          iconSelect: require('@/assets/images/promotion/all-active.png')
        },
        {
          id: 2,
          en: 'In Process',
          name: 'promotion.tab.inProcess',
          icon: require('@/assets/images/promotion/in-process.png'),
          iconSelect: require('@/assets/images/promotion/in-process-active.png')
        },
        {
          id: 3,
          en: 'Expired',
          name: 'promotion.tab.expired',
          icon: require('@/assets/images/promotion/expired.png'),
          iconSelect: require('@/assets/images/promotion/expired-active.png')
        }
      ],
      loading: true,
      cashback2025MayVisibleCountry: ['Indonesia', 'India', 'Malaysia', 'Thailand', 'Vietnam']
    }
  },
  created () {
  },
  async mounted () {
    this.checkCashbackCampaign2025May()
    await this.checkPromotionAuthHandle()
    await this.checkPromotionAuthHandle2()
    this.checkLine1000()
    this.checkNewYear()
    this.checkChristmas()
    this.checkCyberMonday()
    this.checkBlackFriday()
    this.checkHalloween()

    this.filterPromotionList()
  },
  methods: {
    //筛选数组
    filterPromotionList () {
      this.loading = true
      this.list = this.list.filter(f => f.show)
      this.filterList = []
      if (this.tabActive === 1) {
        this.filterList = [...this.list]
      } else if (this.tabActive === 2) {
        this.filterList = this.list.filter(item => new Date(item.countdown) > new Date() || !item.countdown)
      } else {
        this.filterList = this.list.filter(item => item.isExpired)
      }
      this.loading = false
    },
    //切换tab栏
    tabChange (id) {
      if (this.tabActive === id) return
      this.tabActive = id
      this.filterPromotionList()
    },
    //图片是否存在
    changeUrl (event) {
      event.target.src = '/img/promotion/img-en_US.png'
    },
    // 是否显示promotion
    async checkPromotionAuthHandle () {
      try {
        const { code, data, promotionStatus, isParticipatedIn, isExpired } = await checkPromotionAuth({
          coguid: JSON.parse(getLocal('user')).myguid,
          promotionguid: 'ba3a9cf22ed011ef9bdf024d253502b9'
        })
        if (code == 200) {
          this.list.push({
            id: 1,
            img: '/img/promotion/img-',
            imgTitle: '',
            title: 'promotion.activityList.title1',
            tag: true,
            desc: 'promotion.activityList.content1',
            countdown: data.endTime,
            buttonText: 'promotion.activityList.buttonText3',
            day: '',
            time: '',
            show: !!promotionStatus && isParticipatedIn,
            isExpired: isExpired,
            url: `/promotion/promotionInfo?countdown=${ +new Date(data.endTime) }`
          })

          this.getList()

          this.$nextTick(() => {
            this.animationButton()
          })

        } else {

        }
      } catch (err) {
        console.log(err)
      }
    },

    async checkPromotionAuthHandle2 () {

      const result = await getWelcomeBonus({ coGuid: JSON.parse(getLocal('user')).myguid })
      if (result.code == 200) {

        if (result.data.promotionStatus != -1) {

          this.list.push({
            id: 2,
            isImgUrl: true,
            img: result.data.promotionStatus == 1 ? 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/campaign/welcome-bonus/up600_4.png' : 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/campaign/welcome-bonus/up600_1.png',
            imgTitle: '',
            title: 'promotion.activityList.title4',
            tag: true,
            desc: result.data.promotionStatus == 1 ? 'promotion.activityList.content4_100' : 'promotion.activityList.content4',
            countdown: null,
            buttonText: 'promotion.activityList.buttonText3',
            day: '',
            time: '',
            show: true,
            isExpired: false,
            url: `/promotion/promotionInfo-up600?countdown=`
          })

          this.getList()

          this.$nextTick(() => {
            this.animationButton()
          })
        }
      } else {

      }


    },

    checkHalloween() {

      // if (new Date() > new Date('2024-11-01 00:00:00')) return

      this.list.push({
        id: 3,
        isImgUrl: false,
        img: '/img/promotion/halloween-',
        imgTitle: '',
        title: 'promotion.halloween.title',
        tag: false,
        halloTag: true,
        desc: 'promotion.halloween.desc',
        countdown: '2024-11-01 00:00:00',
        buttonText: 'promotion.activityList.buttonText3',
        day: '',
        time: '',
        show: true,
        isExpired: true,
        url: `/promotion/halloween`
      })
      this.getList()
    },

    checkBlackFriday() {

      this.list.push({
        id: 4,
        isImgUrl: false,
        img: '/img/promotion/blackFriday-',
        imgTitle: '',
        title: 'promotion.blackFriday.title',
        tag: false,
        halloTag: true,
        desc: 'promotion.blackFriday.desc',
        countdown: '2024-11-29 23:59:59',
        buttonText: 'promotion.activityList.buttonText3',
        day: '',
        time: '',
        show: true,
        isExpired: new Date() > new Date('2024-11-29 23:59:59'),
        url: `/promotion/black-friday`
      })
      this.getList()

      this.$nextTick(() => {
        this.animationButton()
      })
    },

    checkCyberMonday() {

      this.list.push({
        id: 5,
        isImgUrl: false,
        img: '/img/promotion/cyberMonday-',
        imgTitle: '',
        title: 'promotion.cyberMonday.title',
        tag: false,
        halloTag: true,
        desc: 'promotion.cyberMonday.desc',
        countdown: '2024-12-03 23:59:59',
        buttonText: 'promotion.activityList.buttonText3',
        day: '',
        time: '',
        show: true,
        isExpired: new Date() > new Date('2024-12-03 23:59:59'),
        url: `/promotion/cyber-monday`
      })
      this.getList()

      this.$nextTick(() => {
        this.animationButton()
      })
    },

    checkChristmas() {
      // 仅日本客户可见
      const userInfo = JSON.parse(getLocal('user'))

      if (userInfo.country.includes('Japan') || userInfo.nationality.includes('Japan')) {
        this.list.push({
          id: 6,
          isImgUrl: true,
          img: '/img/promotion/christmas.gif',
          imgTitle: '',
          title: 'promotion.christmas.title',
          tag: false,
          halloTag: true,
          desc: 'promotion.christmas.desc',
          countdown: '2024-12-27 23:59:59',
          buttonText: 'promotion.activityList.buttonText3',
          day: '',
          time: '',
          show: true,
          isExpired: new Date() > new Date('2024-12-27 23:59:59'),
          url: `/promotion/christmas`
        })
        this.getList()

        this.$nextTick(() => {
          this.animationButton()
        })
      }

    },

    checkNewYear() {
      // 仅日本客户可见
      const userInfo = JSON.parse(getLocal('user'))

      if (userInfo.country.includes('Japan') || userInfo.nationality.includes('Japan')) {
        this.list.push({
          id: 7,
          isImgUrl: true,
          img: '/img/promotion/new-year-2025-ja_JP.png',
          imgTitle: '',
          title: 'promotion.newYear2025.title',
          tag: false,
          halloTag: true,
          desc: 'promotion.newYear2025.desc',
          countdown: '2025-01-31 23:59:59',
          buttonText: 'promotion.activityList.buttonText3',
          day: '',
          time: '',
          show: true,
          isExpired: new Date() > new Date('2025-1-31 23:59:59'),
          url: `/promotion/new-year-2025`
        })
        this.getList()

        this.$nextTick(() => {
          this.animationButton()
        })
      }

    },

    checkLine1000() {
      // 仅日本客户可见
      const userInfo = JSON.parse(getLocal('user'))

      if (userInfo.country.includes('Japan') || userInfo.nationality.includes('Japan')) {
        this.list.push({
          id: 8,
          isImgUrl: true,
          img: '/img/promotion/LINE_1000.png',
          imgTitle: '',
          title: 'promotion.line1000.title',
          tag: false,
          halloTag: true,
          desc: 'promotion.line1000.desc',
          countdown: '2025-03-13 23:59:59',
          buttonText: 'promotion.activityList.buttonText3',
          day: '',
          time: '',
          show: true,
          isExpired: new Date() > new Date('2025-03-13 23:59:59'),
          url: `/promotion/line-1000`
        })
        this.getList()

        this.$nextTick(() => {
          this.animationButton()
        })
      }

    },

    checkCashbackCampaign2025May() {
      const userInfo = JSON.parse(getLocal('user'))
      if (!(this.cashback2025MayVisibleCountry.includes(userInfo.country) ||
        this.cashback2025MayVisibleCountry.includes(userInfo.nationality) ||
        userInfo.accounttype === 'Company')) {
        this.list.push({
          id: 8,
          isImgUrl: false,
          img: '/img/promotion/cashback2025May-',
          imgTitle: '',
          title: 'promotion.cashback2025May.title',
          tag: false,
          halloTag: true,
          desc: 'promotion.cashback2025May.desc',
          countdown: '2025-07-04 23:59:59',
          buttonText: 'promotion.activityList.buttonText3',
          day: '',
          time: '',
          show: true,
          isExpired: new Date() > new Date('2025-07-04 23:59:59'),
          url: `/promotion/cashback-2025-may`
        })
        this.getList()

        this.$nextTick(() => {
          this.animationButton()
        })
      }

    },

    getTime (item) {
      const time = item.countdown
      const timer = setInterval(() => {
        const endDate = new Date(time)
        const currentDate = new Date()
        const remainTime = endDate - currentDate

        if (remainTime < 0) {
          clearInterval(timer)
          return
        }
        const days = Math.floor(remainTime / (1000 * 60 * 60 * 24))
        const hours = Math.floor((remainTime / (1000 * 60 * 60)) % 24)
        const minutes = Math.floor((remainTime / (1000 * 60)) % 60)
        const seconds = Math.floor((remainTime / 1000) % 60)
        const h = hours > 9 ? hours : '0' + hours
        const m = minutes > 9 ? minutes : '0' + minutes
        const s = seconds > 9 ? seconds : '0' + seconds

        item.day = days
        item.time = h + 'h : ' + m + 'm : ' + s + 's'

      }, 1000)
    },
    getList () {
      this.list.forEach(item => {
        if (item.countdown) {
          this.getTime(item)
        }
      })
    },
    viewDetail (item) {
      this.$router.push(item.url)
    },
    animationButton () {
      const animateButton = function (e) {
        e.preventDefault()
        // reset animation
        e.target.classList.remove('animate')

        setTimeout(() => {
          e.target.classList.add('animate')
        }, 50)
        setTimeout(function () {
          e.target.classList.remove('animate')
        }, 700)
      }

      const classname = document.getElementsByClassName('bubbly-button')

      for (let i = 0; i < classname.length; i++) {
        classname[i].addEventListener('click', animateButton, false)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tab {
  display: flex;
  flex-wrap: wrap;

  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px 10px 0;
    padding: 6px 16px;
    color: #485FEA;
    border-radius: 6px;
    border: 1px solid #dbdcfc;
    background-color: #fff;
    cursor: pointer;
    transition: all .2s;

    img {
      margin-right: 6px;
      width: 14px;
    }
  }

  &-active {
    color: #fff;
    border: 1px solid #485FEA;
    background-color: #485FEA;
  }
}

.promotion-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  place-items: center;
  gap: 30px 20px;
  //cursor: pointer;
  margin-top: 15px;

  .promotion-container-item {
    position: relative;
    width: 100%;
    height: 100%;
    //max-width: 360px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-radius: 6px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 5px 10px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-out 0s;

  .promotion-container-item-top {
    position: relative;

    img {
      width: 100%;
      height: 100%;
      aspect-ratio: 1200/630;
    }

    .expired {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 20;
      padding: 2px 10px;
      border-radius: 15px;
      font-size: 14px;
      font-weight: 600;
    }
  }

    .promotion-container-item-content {
      flex: 1;
      height: 370px;
      padding: 0 10px;

      .tag {
        width: fit-content;
        background-color: #495eeb;
        padding: 0 5px;
        color: #FFFFFF;
        font-weight: 600;
      }

      .title {
        font-size: 18px;
        font-weight: 700;
        color: #000000;

        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .desc {
        line-height: 1.5rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 8;
        overflow: hidden;
        text-overflow: ellipsis;
      }

    }

    .view {
      width: 65%;
      margin-bottom: 20px;
      text-align: center;
    }

    .expired-mask {
      position: absolute;
      z-index: 10;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.5);
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
  }
}

@media screen and (max-width: 768px) {
  .tab {
    font-size: 12px;

    &-item {
      width: 100%;
    }
  }

  .promotion-container  {
    grid-template-columns: repeat(1, 1fr);

    .promotion-container-item {

      .promotion-container-item-top {

        .expired {
          font-size: 12px;
        }
      }

      .promotion-container-item-content {
        height: 290px;

        .desc {
          -webkit-line-clamp: 10;
        }
      }
    }
  }
}

@media screen and (min-width: 1500px) {

  .promotion-container  {
    grid-template-columns: repeat(4, 1fr);

    .promotion-container-item {

      .promotion-container-item-content {
        height: 290px;
      }
    }
  }
}



.promotion {
  width: calc(100% - 30px);
  height: fit-content;
  border-radius: 5px;
  margin: 0 15px 15px 15px;
  //padding: 30px 60px 60px 60px;
  padding: 20px;
  background-color: #e8eaf7;
  position: relative;

  .promotion-header {
    position: relative;
    top: -20px;
    left: -20px;
    color: #000000;
    font-size: 20px;

  }

  .promotion-box {
    width: 100%;
    box-sizing: border-box;
    padding: 40px 0;
    border-bottom: 4px solid #bfc3c3;
    margin-bottom: 40px;

    .container-box {
      width: 100%;
      height: 175px;
      display: flex;
      justify-content: space-between;
      gap: 20px;

      .left {
        width: 300px;
        height: 175px;
        line-height: 175px;
        text-align: center;
        font-size: 20px;
        color: #000000;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .center {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 10px;

        p {
          margin-bottom: 0;
        }

        .title {
          color: #000000;
          font-size: 20px;
          font-weight: 700;
          line-height: 1.6rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tag {
          width: fit-content;
          background-color: #495eeb;
          padding: 0 5px;
          color: #FFFFFF;
          font-weight: 600;
        }

        .desc {
          color: #000000;
          font-size: 16px;
          line-height: 1.5rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .right {
        width: 200px;
        position: relative;

        .view {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          height: 40px;
          line-height: 40px;
          background-color: #495eeb;
          color: #FFFFFF;
          font-weight: 700;
          font-size: 16px;
          cursor: pointer;
          text-align: center;

          .expired {
            width: 150px;
            height: 160px;
            position: absolute;
            top: -80px;
            right: -30px;
            transform: rotate(-2deg);
          }
        }

        .time {
          width: 100%;
          position: absolute;
          text-align: center;
          top: 70%;

          .countdown {
            margin: 0;
          }
        }

        /*.expired {
          width: 300px;
          height: 160px;
          position: absolute;
          top: -60px;
          right: -45px;
          transform: rotate(9deg);
        }*/
      }
    }

    &:nth-child(2) {
      padding-top: 0;
    }

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }
}

@media screen and (max-width: 1100px) {
  .promotion {
    //padding: 30px;

    .promotion-header {
      top: -20px;
    }

    .promotion-box {
      .container-box {
        .center {
          .title {
            font-size: 16px;
          }

          .tag {
            font-size: 12px;
          }

          .desc {
            font-size: 14px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1000px) {
  .promotion {
    .promotion-box {
      .container-box {
        .left {
          width: 250px;
        }

        .right {
          width: 150px;

          .view {
            font-size: 14px;

            .expired {
              width: 140px;
              height: 140px;
              top: -70px;
            }
          }

          /*.expired {
            width: 180px;
            height: 80px;
            top: -0px;
            right: -25px;
          }*/
        }
      }
    }
  }
}

@media screen and (max-width: 720px) {
  .promotion {
    height: fit-content;

    .promotion-box {
      .container-box {
        height: fit-content;
        flex-direction: column;

        .left {
          width: 100%;
          height: 180px;
          text-align: center;
        }

        .center {
          width: 100%;

          .title {
            display: initial;
            text-overflow: initial;
            overflow: initial;
          }

          .desc {
            display: initial;
            text-overflow: initial;
            overflow: initial;
          }
        }

        .right {
          width: 100%;
          height: 80px;

          .view {
            width: 80%;
            margin-bottom: 10px;
            top: 0;
            left: 50%;
            transform: translate(-50%, 0);

            .expired {
              top: -64px;
              right: -30px;
            }
          }

          /*.expired {
            top: 40px;
            right: 35%;
          }*/
        }
      }
    }
  }
}

body {
  font-size: 16px;
  font-family: "Helvetica", "Arial", sans-serif;
  text-align: center;
  background-color: #fff;
}

.bubbly-button {
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-family: "Helvetica", "Arial", sans-serif;
  display: inline-block;
  font-size: 1em;
  -webkit-appearance: none;
  appearance: none;
  background-color: #495eeb;
  color: #fff;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  position: relative;
  transition: transform ease-in 0.1s, box-shadow ease-in 0.25s;
  box-shadow: 0px 11px 5px -8px rgba(0, 0, 0, 0.5);
}

.bubbly-button:focus {
  outline: 0;
}

.bubbly-button:before, .bubbly-button:after {
  position: absolute;
  content: "";
  display: block;
  width: 140%;
  height: 100%;
  left: -20%;
  z-index: -1000;
  transition: all ease-in-out 0.5s;
  background-repeat: no-repeat;
}

.bubbly-button:before {
  display: none;
  top: -75%;
  background-image: radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, transparent 20%, #495eeb 20%, transparent 30%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, transparent 10%, #495eeb 15%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%);
  background-size: 5% 5%, 10% 10%, 10% 10%, 10% 10%, 18% 18%, 5% 5%, 10% 10%, 5% 5%, 9% 9%;
}

.bubbly-button:after {
  display: none;
  bottom: -75%;
  background-image: radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, transparent 10%, #495eeb 15%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%), radial-gradient(circle, #495eeb 20%, transparent 20%);
  background-size: 10% 10%, 10% 10%, 9% 9%, 10% 10%, 10% 10%, 5% 5%, 10% 10%;
}

.bubbly-button:active {
  transform: scale(0.9);
  background-color: #495eeb;
  box-shadow: 0 2px 25px rgb(73, 94, 235);
}

.bubbly-button.animate:before {
  display: block;
  animation: topBubbles ease-in-out 0.75s forwards;
}

.bubbly-button.animate:after {
  display: block;
  animation: bottomBubbles ease-in-out 0.75s forwards;
}

//.bubbly-button.animateOnce:before {
//  display: block;
//  animation: topBubbles ease-in-out 0.75s forwards;
//}
//
//.bubbly-button.animateOnce:after {
//  display: block;
//  animation: bottomBubbles ease-in-out 0.75s forwards;
//}

@keyframes topBubbles {
  0% {
    background-position: 5% 90%, 10% 90%, 10% 90%, 15% 90%, 25% 90%, 25% 90%, 40% 90%, 55% 90%, 70% 90%;
  }
  50% {
    background-position: 0% 80%, 0% 20%, 10% 40%, 20% 0%, 30% 30%, 22% 50%, 50% 50%, 65% 20%, 90% 30%;
  }
  100% {
    background-position: 0% 70%, 0% 10%, 10% 30%, 20% -10%, 30% 20%, 22% 40%, 50% 40%, 65% 10%, 90% 20%;
    background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }
}

@keyframes bottomBubbles {
  0% {
    background-position: 10% -10%, 30% 10%, 55% -10%, 70% -10%, 85% -10%, 70% -10%, 70% 0%;
  }
  50% {
    background-position: 0% 80%, 20% 80%, 45% 60%, 60% 100%, 75% 70%, 95% 60%, 105% 0%;
  }
  100% {
    background-position: 0% 90%, 20% 90%, 45% 70%, 60% 110%, 75% 80%, 95% 70%, 110% 10%;
    background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }
}
</style>
