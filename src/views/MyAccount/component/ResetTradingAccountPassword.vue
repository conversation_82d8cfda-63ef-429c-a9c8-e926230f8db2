<template>
  <el-dialog :title="$t('account_information.dialog.title', { type: type }) + ' ' + object.login" @close="closeDialog"
             :width="dialogWidth"
             :destroy-on-close="true" :visible.sync="visible" :modal="true" :lock-scroll="true"
             :close-on-click-modal="false">
    <div style="height: 60vh;overflow-y: auto">
      <el-steps style="margin-top: 10px;width: 90%" finish-status="success" :active="active">
        <el-step title="step 1" :description="$t('account_information.dialog.question1')"></el-step>
        <el-step title="step 2" :description="$t('account_information.dialog.question2', {type})"></el-step>
      </el-steps>
      <ValidationObserver v-if="active === 0" ref="form" v-slot="{ handleSubmit }">
        <b-form @submit.stop.prevent="handleSubmit(verifySecurityQuestion)">
          <b-form-group
            v-loading="questionLoading"
            class="col-sm-8"
            :label="$t('reset_password.label2')"
            label-cols-sm="4">
            <span>{{ securityQuestion.question }}</span>
          </b-form-group>
          <ValidationProvider name="reset_password.label3" rules="required" v-slot="{ errors }">
            <b-form-group
              class="col-sm-8"
              :label="$t('reset_password.label3')"
              label-cols-sm="4">
              <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                            v-bind:disabled="submitLoading" v-model="answer.answer"></b-form-input>
              <div class="validation">
                <span>{{ errors[0] }}</span>
              </div>
            </b-form-group>
          </ValidationProvider>
          <b-button variant="primary" type="submit" style="margin-top: 12px;">
            {{ $t('account_information.dialog.next') }}
            <i v-if="submitLoading"
               style="padding: 0;margin-left: 5px"
               class="el-icon-loading"/>
          </b-button>
        </b-form>
      </ValidationObserver>
      <ValidationObserver v-if="active === 1" ref="form" v-slot="{ handleSubmit }">
        <b-form @submit.stop.prevent="handleSubmit(submitNewPassword)">
          <ValidationProvider name="reset_password.label4" rules="required||newPassword" v-slot="{ errors }">
            <b-form-group
              class="col-sm-8"
              :label="$t('reset_password.label4')"
              label-cols-sm="4">
              <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')" type="password"
                            v-model="newPassword.newPassword"></b-form-input>
              <div class="validation">
                <span>{{ errors[0] }}</span>
              </div>
            </b-form-group>
            <b-form-group
              class="col-sm-8"
              label=""
              label-cols-sm="4">
              <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="flex: 1">
                  <b-checkbox :disabled="true" v-for="item in validateSelection1" v-model="item.checked"
                              :key="item.index">
                    {{ item.name }}
                  </b-checkbox>
                </div>
                <div style="flex: 1">
                  <b-checkbox :disabled="true" v-for="item in validateSelection2" v-model="item.checked"
                              :key="item.index">
                    {{ item.name }}
                  </b-checkbox>
                </div>
              </div>

            </b-form-group>
          </ValidationProvider>
          <ValidationProvider name="reset_password.label5" rules="required" v-slot="{ errors }">
            <b-form-group
              class="col-sm-8"
              :label="$t('reset_password.label5')"
              label-cols-sm="4">
              <b-form-input type="password" :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                            v-model="newPassword.newPasswordConfirm"></b-form-input>
              <div class="validation">
                <span>{{ errors[0] }}</span>
              </div>
              <div v-if="inconsistency" class="validation">
                <span>{{ $t('validation.passwordInconsistency') }}</span>
              </div>
            </b-form-group>
          </ValidationProvider>
          <div v-if="googleRecaptchaVisible" id="google-captcha">
            <GoogleRecaptcha ref="recaptcha" @getValidateCode="clickGoogleCaptcha"
                             :captchaType="captchaType"
                             key="6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_"></GoogleRecaptcha>
          </div>
          <ValidationProvider v-else name="reset_password.label6" rules="required" v-slot="{ errors }">
            <b-form-group
              class="col-sm-8"
              :label="$t('reset_password.label6')"
              label-cols-sm="4">
              <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                            v-model="newPassword.checkCode"></b-form-input>
              <div class="validation">
                <span>{{ errors[0] }}</span>
              </div>
            </b-form-group>
            <b-form-group
              class="col-sm-8"
              label=""
              label-cols-sm="4">
              <el-image
                :fit="codePictureStyle"
                @click="clickCodeImage"
                :src="codePicture"
                class="captcha-picture"
                v-loading="codeLoading"
                element-loading-spinner="el-icon-loading"></el-image>
            </b-form-group>
          </ValidationProvider>

          <b-button variant="primary" type="submit" style="margin-top: 12px;">
            {{ $t('account_information.dialog.next') }}
            <i v-if="submitLoading"
               style="padding: 0;margin-left: 5px"
               class="el-icon-loading"/>
          </b-button>
        </b-form>
      </ValidationObserver>
      <div v-if="active === 2">
        <el-result icon="success" :title="$t('account_information.dialog.success')">
          <template slot="extra">
            <b-button variant="primary" size="medium" @click="closeDialog">{{
                $t('account_information.dialog.close')
              }}
            </b-button>
          </template>
        </el-result>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getLocal } from '@/Utils/authLocalStorage'
import { getQueryAll, questionVerification } from '@/services/securityQuestionContent'
import { Message } from 'element-ui'
import GoogleRecaptcha from '@/components/MyfxComponent/GoogleRecaptcha'
import auth from '@/services/auth'
import { passwordValid } from '@/Utils/NewPasswordValidater'
import { mapGetters } from 'vuex'
import AESCrypto from '@/Utils/AESCrypto'
import { submitChangeTradingAccountPassword } from '@/services/account'

export default {
  name: 'ResetTradingAccountPassword',
  components: {
    GoogleRecaptcha
  },
  props: {
    visible: Boolean,
    close: Function,
    object: Object,
    type: String
  },
  watch: {
    active (newVal, oldVal) {
      if (newVal === 1) {
        this.getCheckCode()
      }
    },
    'newPassword.newPassword': {
      handler (newVal, oldVal) {
        this.validateNewPassword = passwordValid(newVal)
        console.log(this.validateNewPassword)
        this.validateSelection1.forEach(item => {
          item.checked = this.validateNewPassword.indexOf(item.key) !== -1
          console.log(item.key)
        })
        this.validateSelection2.forEach(item => {
          item.checked = this.validateNewPassword.indexOf(item.key) !== -1
          console.log(item.key)
        })
      }
    },
    'newPassword.newPasswordConfirm': {
      handler (newVal, oldVal) {
        this.inconsistency = !(newVal === this.newPassword.newPassword && newVal !== null)
      }
    },
    '$i18n.locale' (newValue) {
      if (this.active === 1) {
        this.getCOQuestions()
      }
    }
  },
  computed: {
    ...mapGetters({
      stateCaptcha: 'Setting/captchaState'
    }),
    validateSelection1 () {
      return [
        {
          index: 0,
          key: this.$t('reset_password.eight_character'),
          name: this.$t('accountRegister.checkbox4'),
          checked: false
        },
        {
          index: 1,
          key: this.$t('reset_password.one_number'),
          name: this.$t('accountRegister.checkbox3'),
          checked: false
        }
      ]
    },
    validateSelection2 () {
      return [
        {
          index: 2,
          key: this.$t('reset_password.one_lowerCase'),
          name: this.$t('accountRegister.checkbox2'),
          checked: false
        },
        {
          index: 3,
          key: this.$t('reset_password.one_upperCase'),
          name: this.$t('accountRegister.checkbox1'),
          checked: false
        }
      ]
    }
  },
  mounted () {
    window.onresize = () => {
      return (() => {
        this.setDialogWidth()
      })()
    }
    const visible = this.visible
    if (this.active === 0 && this.visible) {
      this.getCOQuestions()
    }
  },
  created () {
    this.setDialogWidth()
    console.log('type', this.type)
  },
  data () {
    return {
      active: 0,
      questionLoading: false,
      submitLoading: false,
      securityQuestion: {
        code: 0,
        lang: '',
        question: ''
      },
      answer: {
        answer: '',
        code: 0,
        myguid: ''
      },
      newPassword: {
        newPassword: '',
        newPasswordConfirm: '',
        checkCode: ''
      },
      codePicture: '',
      codePictureStyle: 'contains',
      validateNewPassword: [],
      googleRecaptchaVisible: false,
      codeLoading: false,
      inconsistency: false,
      captchaLoading: false,
      dialogWidth: '',
      captchaType: 'cloudflare' // 默认使用cloudflare
    }
  },

  methods: {
    setDialogWidth () {
      const val = document.body.clientWidth
      const def = 800 // 默认宽度
      if (val < def) {
        this.dialogWidth = '100%'
      } else {
        this.dialogWidth = def + 'px'
      }
    },
    next () {
      this.active++
    },
    getCOQuestions () {
      this.questionLoading = true
      const myguid = JSON.parse(getLocal('user')).myguid
      getQueryAll(myguid).then(res => {
        this.securityQuestion = res.data
        this.questionLoading = false
      }).catch(error => {
        this.questionLoading = false
      })
    },
    verifySecurityQuestion () {
      this.$vp.add({ message: '[ResetTradingAccountPassword] Verify security question.' })

      this.submitLoading = true
      this.answer.myguid = JSON.parse(getLocal('user')).myguid
      this.answer.code = this.securityQuestion.code
      questionVerification(this.answer.myguid, this.answer.answer).then(res => {
        Message.success(res.msg)
        this.submitLoading = false
        this.next()
      }).catch(error => {
        this.submitLoading = false
        this.securityQuestion.question = error.question
      })
    },
    getCheckCode () {
      this.captchaLoading = true
      this.googleRecaptchaVisible = false
      auth.getCaptcha().then(res => {
        // 开发环境强制使用cloudflare
        if (process.env.NODE_ENV === 'development') {
          this.captchaType = 'cloudflare'
          this.googleRecaptchaVisible = true
          return
        }

        // 生产环境根据后端返回的type选择
        if (res.type === 'googleCaptcha') {
          this.captchaType = 'googleCaptcha'
          this.googleRecaptchaVisible = true
          return
        } else {
          this.captchaType = 'cloudflare'
          this.googleRecaptchaVisible = true
          return
        }

        // 兼容旧的图片验证码逻辑（如果后端没有返回type或者返回其他值）
        if (this.stateCaptcha.token) {
          this.$store.dispatch('Setting/authCaptchaAction', {
            codeImage: '',
            token: ''
          })
        }
        const codeData = res.data
        if (!codeData) {
          Message.error('Error: Get check code error!')
          return
        }
        this.$store.dispatch('Setting/authCaptchaAction', {
          codeImage: codeData.codeImage,
          token: codeData.token
        })
        this.codePicture = this.stateCaptcha.codeImage
        this.captchaLoading = false
      })
    },
    clickGoogleCaptcha (val) {
      this.$store.dispatch('Setting/authCaptchaAction', {
        codeImage: '',
        token: val
      })
    },
    clickCodeImage () {
      this.getCheckCode()
    },
    submitNewPassword () {
      this.$vp.add({ message: '[ResetTradingAccountPassword] Confirm reset password.' })
      if (this.inconsistency) {
        return
      }

      this.submitLoading = true
      const currentPassword = AESCrypto.Encrypt(this.newPassword.newPassword)
      const myguid = this.object.myguid
      const code = this.newPassword.checkCode
      const token = this.stateCaptcha.token
      submitChangeTradingAccountPassword({
        currentPassword,
        myguid
      }, code, token).then(res => {
        // Message.success(res.msg)
        this.next()
      }).catch(error => {
        this.submitLoading = false
        this.getCheckCode()
      })
    },
    back () {

    },
    closeDialog () {
      this.$vp.add({
        message: '[ResetTradingAccountPassword] Dialog close.'
      })

      this.close()
      this.active = 0
    }

  }
}
</script>

<style>
.validation {
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #dc3545
}

.captcha-picture {
  padding-top: 2px;
  padding-bottom: 5px;
}
</style>
