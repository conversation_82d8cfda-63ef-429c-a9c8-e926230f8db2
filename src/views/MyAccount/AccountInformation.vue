<template>
  <div class="app-container">
    <!--     class="hidden-md-and-down"-->
    <b-container fluid>
      <b-row>
        <b-col>
          <iq-card style="background-color: transparent">
            <template v-slot:headerTitle>
              <h4 class="card-title">{{ $t('sidebar.accountInformation') }}</h4>
            </template>
            <template v-slot:body>
              <div>
<!--                <b-alert variant="primary" :show="true" class="idCard">
                  <div style="width: 150px;float: left">
                    <b-card-text>{{ $t('sidebar.name') }}：</b-card-text>
                    <b-card-text style="color: #000000;margin-top: -10px;">
                      {{ generalInformation.name }}
                    </b-card-text>
                  </div>
                  <div style="width: 150px;float: left">
                    <b-card-text>{{ $t('sidebar.phone') }}：</b-card-text>
                    <b-card-text style="color: #000000;margin-top: -10px;">
                      {{ generalInformation.phoneNumber }}
                    </b-card-text>
                  </div>
                  <div style="width: 150px;float: left">
                    <b-card-text>{{ $t('sidebar.email') }}：</b-card-text>
                    <b-card-text style="color: #000000;margin-top: -10px;">
                      {{ generalInformation.emailAddress }}
                    </b-card-text>
                  </div>
                </b-alert>-->
                <div class="typeChange" style="display: flex;gap: 20px;align-items: flex-end;">
                  <div>
                    <button :class="mt4Index  ? 'mt4One' : 'mt4' " @click="mt4Click"
                            v-viewpoint.click="{ message: `[AccountInformation] Click MT4 type` }"> MT4
                    </button>
                    <button :class="mt5Index ?  'mt5One' : 'mt5' " @click="mt5Click"
                            v-viewpoint.click="{ message: `[AccountInformation] Click MT5 type` }"> MT5
                    </button>
                  </div>
                  <div style="color: #495EEB;text-decoration: underline;cursor: pointer" @click="lookAll">
                    <i class="el-icon-loading" v-if="viewAllMT4Loading && mt4Activet"></i>
                    <i class="el-icon-loading" v-if="viewAllMT5Loading && !mt4Activet"></i>
                    {{ $t('account_information.Table.viewAll') }}
                  </div>
                </div>

                <div class="account-list" v-loading="tradingAccountLoading">
                  <div
                    class="account-list-item"
                    v-for="(item, idx) in (mt4Activet ? mt4VisibleCardList : mt5VisibleCardList)"
                    :key="idx"
                    v-loading="item.loading"
                  >
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">{{ $t('account_information.Table.column_02') }}</div>
                      <div class="account-list-item-column-value">{{ item.data.accountName }}</div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">{{ $t('account_information.Table.column_01') }}</div>
                      <div class="account-list-item-column-value">{{ item.data.login }}</div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">{{ $t('account_information.Table.column_08') }}</div>
                      <div class="account-list-item-column-value">{{ item.data.accountType ? $t(`account_information.Table.accountType.${item.data.accountType}`) : '' }}</div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">{{ $t('account_information.Table.column_03') }}</div>
                      <div class="account-list-item-column-value">{{ item.data.leverage }}</div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">{{ $t('account_information.Table.column_05') }}</div>
                      <div class="account-list-item-column-value">
                        {{ $t(`account_information.Status.status_${ item.data.status }`) }}
                      </div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">{{ $t('account_information.Table.column_04') }}</div>
                      <div class="account-list-item-column-value">{{ item.data.currency }}</div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">
                        {{ $t('account_information.Table.column_06') }}
                      </div>
                      <div class="account-list-item-column-value">
                        <i class="el-icon-loading" v-if="item.data.loading"></i>
                        <span v-else>{{ item.data.balance || '--' }}</span>
                      </div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">
                        {{ $t('account_information.Table.column_07') }}
                      </div>
                      <div class="account-list-item-column-value">
                        <i class="el-icon-loading" v-if="item.data.loading"></i>
                        <span v-else>{{ item.data.floatingPnl || '--' }}</span>
                      </div>
                    </div>
                    <div class="account-list-item-column">
                      <div class="account-list-item-column-label">
                        <span
                          v-if="item.isExit"
                          style="color: #495EEB; cursor: pointer;text-decoration: underline;"
                          @click="sendExitRequest(item, item.data.myguid, item.data.accountName, item.data.login, item.data.server)"
                        >{{ $t('account_information.exit') }}</span>
                      </div>
                      <div class="account-list-item-column-value">
                        <span
                          v-if="item.isRecover"
                          style="color: #495EEB;text-decoration: underline;font-weight: normal;"
                          @click="recoveryMTAccount(item, item.data.login, item.data.accountName)"
                        >{{ $t('account_information.recover') }}</span>
                      </div>
                      <div class="account-list-item-column-value">
                        <!-- isBeingProcessed isJoin-->
                        <span
                          v-if="item.isJoin"
                          style="color: #495EEB;text-decoration: underline;font-weight: normal;cursor: pointer"
                          @click="joinAgain(item)"
                        >{{ $t('account_information.join') }}</span>
                      </div>
                      <div
                        v-if="item.isBeingProcessed"
                        style="color: #495EEB;text-decoration: underline;font-weight: normal;"
                        class="account-list-item-column-value"
                      >
                        {{ $t('account_information.beingProcessed') }}
                      </div>
                      <div
                        v-if="item.isRecoveryBeingProcessed"
                        style="color: #495EEB;text-decoration: underline;font-weight: normal;"
                        class="account-list-item-column-value"
                      >
                        {{ $t('account_information.recoveryBeingProcessed') }}
                      </div>
                      <div
                        v-if="item.isJoinBeingProcessed"
                        style="color: #495EEB;text-decoration: underline;font-weight: normal;"
                        class="account-list-item-column-value"
                      >
                        {{ $t('account_information.joinBeingProcessed') }}
                      </div>
                    </div>
                  </div>
                </div>

                <!--                <el-table v-loading="tradingAccountLoading" style="margin-top: 20px;"-->
                <!--                          :data="mt4Activet ? mt4VisibleCardList : mt5VisibleCardList" border-->
                <!--                          stripe>-->
                <!--                  <el-table-column prop="data.login" :label="$t('account_information.Table.column_01')" width="110px"/>-->
                <!--                  <el-table-column :label="$t('account_information.Table.column_02')" min-width="300px">-->
                <!--                    <template slot-scope="{row}">-->
                <!--                      <div style="display: flex; justify-content: space-between">-->
                <!--                        <div>{{ row.data.accountName }}</div>-->
                <!--                        <span-->
                <!--                          v-if="row.isExit"-->
                <!--                          style="color: #495EEB; cursor: pointer"-->
                <!--                          @click="sendExitRequest(row.data.myguid, row.data.accountName, row.data.login, row.data.server)"-->
                <!--                        >-->
                <!--                                          {{ $t('account_information.exit') }}-->
                <!--                                        </span>-->
                <!--                        <span-->
                <!--                          v-if="row.isRecover"-->
                <!--                          style="color: #495EEB"-->
                <!--                          @click="recoveryMTAccount(row.data.login, row.data.accountName)"-->
                <!--                        >-->
                <!--                                          {{ $t('account_information.recover') }}-->
                <!--                                        </span>-->
                <!--                      </div>-->
                <!--                    </template>-->
                <!--                  </el-table-column>-->
                <!--                  <el-table-column prop="data.leverage" :label="$t('account_information.Table.column_03')" width="100px"-->
                <!--                                   align="center"/>-->
                <!--                  &lt;!&ndash;                  <el-table-column prop="name" label="账户类型"/>&ndash;&gt;-->
                <!--                  <el-table-column prop="data.currency" :label="$t('account_information.Table.column_04')" width="100px"-->
                <!--                                   align="center"/>-->
                <!--                  <el-table-column width="200px" align="center" fixed="right">-->
                <!--                    <template slot="header">-->
                <!--                      {{ $t('account_information.Table.column_05_01') }}-->
                <!--                      <el-button type="text"-->
                <!--                                 style="font-size: 12px;padding: 0;color: #fff;font-weight: bold;text-decoration: underline"-->
                <!--                                 @click="lookAll">-->
                <!--                        {{ $t('account_information.Table.column_05_02') }}-->
                <!--                      </el-button>-->
                <!--                    </template>-->
                <!--                    <template slot-scope="{row}">-->
                <!--                      <i v-if="row.data.loading" class="el-icon-loading"></i>-->
                <!--                      <template v-else>-->
                <!--                                        <span v-if="row.data.balance!==''" style="font-weight: bold;color: #495EEB">-->
                <!--                                        {{ row.data.balance }} / {{ row.data.floatingPnl }}-->
                <!--                                      </span>-->
                <!--                        <el-button-->
                <!--                          v-else-->
                <!--                          type="text"-->
                <!--                          style="font-size: 12px;padding: 0;"-->
                <!--                          @click="getTradingAccountBalanceAndFloatingProfit(row)"-->
                <!--                        >{{ $t('account_information.Table.column_05_03') }}-->
                <!--                        </el-button>-->
                <!--                      </template>-->
                <!--                    </template>-->
                <!--                  </el-table-column>-->
                <!--                </el-table>-->
                <!--                <div style="float: right;">-->
                <!--                  <i class="el-icon-loading" style="font-size: 20px" v-if="tradingAccountLoading"></i>-->
                <!--                  <b-button variant="outline-link" style="color:rgba(0,0,0,0.8);font-size: 20px;"-->
                <!--                            @click="showMt4HiddenTradingAccountList">-->
                <!--                    <div v-if="!mt4Hidden">-->
                <!--                      <span>{{ $t('account_information.show_hidden_account') }}</span>-->
                <!--                      <i class="fas fa-eye" style="margin-left: 10px"></i>-->
                <!--                    </div>-->
                <!--                    <div v-else>-->
                <!--                      <span>{{ $t('account_information.show_visible_account') }}</span>-->
                <!--                      <i class="fas fa-eye-slash" style="margin-left: 10px"></i>-->
                <!--                    </div>-->
                <!--                  </b-button>-->
                <!--                </div>-->
                <!--                <div v-if="mt4Activet" style="margin-top: 80px">-->
                <!--                  <div v-if="!mt4Hidden">-->
                <!--                    <card-dragger-->
                <!--                      v-if="mt4VisibleCardList.length!==0"-->
                <!--                      :data="mt4VisibleCardList"-->
                <!--                      :colNum="2"-->
                <!--                      :cardInsideHeight="150"-->
                <!--                      :cardOutsideHeight="170">-->
                <!--                      <template v-slot:header="slotProps">-->
                <!--                        <div style="margin-left: 20px;padding-top: 10px;">-->
                <!--                          <span style="color: #0b0b0b">{{ $t('sidebar.name') }}：{{-->
                <!--                              slotProps.item.data.accountName-->
                <!--                            }}</span>-->

                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.hide_account')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="hideMt4Account(slotProps.item)" class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-eye"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          <b-button variant="outline-link"-->
                <!--                                    v-b-tooltip.hover.top="$t('account_information.balance_floating_pnl')"-->
                <!--                                    @click="getTradingAccountBalanceAndFloatingProfit(slotProps.item)"-->
                <!--                                    class="card-button">-->
                <!--                            <i class="fas fa-search-dollar"></i>-->
                <!--                          </b-button>-->
                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.reset_mt_password')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="resetTradingAccountPassword(slotProps.item)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-key"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkWithdrawal(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.withdrawal')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToWithdrawalPage"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-money-check-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkDeposit(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.deposit')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToDepositPage(slotProps.item.data)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="far fa-money-bill-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                        </div>-->
                <!--                        <div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('dashboard1.balance') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.balance===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.balance!==null || slotProps.item.data.balance!==undefined">{{-->
                <!--                                slotProps.item.data.balance-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('account_information.floating') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.floatingPnl===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.floatingPnl!==null || slotProps.item.data.floatingPnl!==undefined">{{-->
                <!--                                slotProps.item.data.floatingPnl-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                        </div>-->

                <!--                      </template>-->
                <!--                      <template v-slot:content="slotProps">-->
                <!--                        <div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.mt4login') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.login }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.account_type') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.accountType }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.leverage') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.leverage }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          &lt;!&ndash;                        <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                                                    <div>-->
                <!--                                                      <span style="color: #b9b9be">Group</span>-->
                <!--                                                    </div>-->
                <!--                                                    <div>-->
                <!--                                                      <span style="color: #0b0b0b">{{ slotProps.item.data.mt4group }}</span>-->
                <!--                                                    </div>-->
                <!--                                                  </div>&ndash;&gt;-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.currency') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.currency }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span v-if="slotProps.item.isExit" style="color: #495EEB; cursor: pointer"-->
                <!--                                    @click="sendExitRequest(slotProps.item.data.myguid, slotProps.item.data.accountName, slotProps.item.data.login, slotProps.item.data.server)">{{-->
                <!--                                  $t('account_information.exit')-->
                <!--                                }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span v-if="slotProps.item.isRecover" style="color: #495EEB"-->
                <!--                                    @click="recoveryMTAccount(slotProps.item.data.login, slotProps.item.data.accountName)">{{-->
                <!--                                  $t('account_information.recover')-->
                <!--                                }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                        </div>-->
                <!--                      </template>-->
                <!--                    </card-dragger>-->
                <!--                    <el-empty v-else :description="$t('account_information.empty')"></el-empty>-->
                <!--                  </div>-->
                <!--                  <div v-else>-->
                <!--                    <card-dragger-->
                <!--                      v-if="mt4HiddenCardList.length!==0"-->
                <!--                      :data="mt4HiddenCardList"-->
                <!--                      :colNum="2"-->
                <!--                      :cardInsideHeight="150"-->
                <!--                      :cardOutsideHeight="170"-->
                <!--                    >-->
                <!--                      <template v-slot:header="slotProps">-->
                <!--                        <div style="margin-left: 20px;padding-top: 10px">-->
                <!--                          <span style="color: #0b0b0b">{{ $t('sidebar.name') }}: {{-->
                <!--                              slotProps.item.data.accountName-->
                <!--                            }}</span>-->

                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.show_account')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="hideMt4Account(slotProps.item)" class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-eye-slash"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          <b-button variant="outline-link"-->
                <!--                                    v-b-tooltip.hover.top="$t('account_information.balance_floating_pnl')"-->
                <!--                                    @click="getTradingAccountBalanceAndFloatingProfit(slotProps.item)"-->
                <!--                                    class="card-button">-->
                <!--                            <i class="fas fa-search-dollar"></i>-->
                <!--                          </b-button>-->
                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.reset_mt_password')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="resetTradingAccountPassword(slotProps.item)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-key"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkWithdrawal(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.withdrawal')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToWithdrawalPage"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-money-check-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkDeposit(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.deposit')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToDepositPage(slotProps.item.data)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="far fa-money-bill-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                        </div>-->
                <!--                        <div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('dashboard1.balance') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.balance===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.balance!==null || slotProps.item.data.balance!==undefined">{{-->
                <!--                                slotProps.item.data.balance-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('account_information.floating') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.floatingPnl===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.floatingPnl!==null || slotProps.item.data.floatingPnl!==undefined">{{-->
                <!--                                slotProps.item.data.floatingPnl-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                        </div>-->

                <!--                      </template>-->
                <!--                      <template v-slot:content="slotProps">-->
                <!--                        <div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.mt4login') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.login }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.account_type') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.accountType }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.leverage') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.leverage }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          &lt;!&ndash;                        <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                                                    <div>-->
                <!--                                                      <span style="color: #b9b9be">Group</span>-->
                <!--                                                    </div>-->
                <!--                                                    <div>-->
                <!--                                                      <span style="color: #0b0b0b">{{ slotProps.item.data.mt4group }}</span>-->
                <!--                                                    </div>-->
                <!--                                                  </div>&ndash;&gt;-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.currency') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.currency }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->

                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span v-if="slotProps.item.isExit" style="color: #495EEB; cursor: pointer"-->
                <!--                                    @click="sendExitRequest(slotProps.item.data.myguid, slotProps.item.data.accountName, slotProps.item.data.login, slotProps.item.data.server)">{{-->
                <!--                                  $t('account_information.exit')-->
                <!--                                }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span v-if="slotProps.item.isRecover" style="color: #495EEB"-->
                <!--                                    @click="recoveryMTAccount(slotProps.item.data.login , slotProps.item.data.accountName)">{{-->
                <!--                                  $t('account_information.recover')-->
                <!--                                }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->

                <!--                        </div>-->
                <!--                      </template>-->
                <!--                    </card-dragger>-->
                <!--                    <el-empty v-else :description="$t('account_information.empty')"></el-empty>-->
                <!--                  </div>-->
                <!--                </div>-->
                <!--                <div v-if="mt5Activet" style="margin-top: 80px">-->
                <!--                  <div v-if="!mt5Hidden">-->
                <!--                    <card-dragger-->
                <!--                      v-if="mt5VisibleCardList.length!==0"-->
                <!--                      :data="mt5VisibleCardList"-->
                <!--                      :colNum="2"-->
                <!--                      :cardInsideHeight="150"-->
                <!--                      :cardOutsideHeight="170"-->
                <!--                    >-->
                <!--                      <template v-slot:header="slotProps">-->
                <!--                        <div style="margin-left: 20px;padding-top: 10px;">-->
                <!--                          <span style="color: #0b0b0b">{{ $t('sidebar.name') }}: {{-->
                <!--                              slotProps.item.data.accountName-->
                <!--                            }}</span>-->
                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.hide_account')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="hideMt5Account(slotProps.item)" class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-eye"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          <b-button variant="outline-link"-->
                <!--                                    v-b-tooltip.hover.top="$t('account_information.balance_floating_pnl')"-->
                <!--                                    @click="getTradingAccountBalanceAndFloatingProfit(slotProps.item)"-->
                <!--                                    class="card-button">-->
                <!--                            <i class="fas fa-search-dollar"></i>-->
                <!--                          </b-button>-->
                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.reset_mt_password')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="resetTradingAccountPassword(slotProps.item)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-key"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkWithdrawal(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.withdrawal')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToWithdrawalPage"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-money-check-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkDeposit(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.deposit')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToDepositPage(slotProps.item.data)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="far fa-money-bill-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                        </div>-->
                <!--                        <div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('dashboard1.balance') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.balance===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.balance!==null || slotProps.item.data.balance!==undefined">{{-->
                <!--                                slotProps.item.data.balance-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('account_information.floating') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.floatingPnl===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.floatingPnl!==null || slotProps.item.data.floatingPnl!==undefined">{{-->
                <!--                                slotProps.item.data.floatingPnl-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                        </div>-->

                <!--                      </template>-->
                <!--                      <template v-slot:content="slotProps">-->
                <!--                        <div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.mt5login') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.login }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.account_type') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.accountType }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.leverage') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.leverage }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.currency') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.currency }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            &lt;!&ndash;                            <div>&ndash;&gt;-->
                <!--                            &lt;!&ndash;                              <span v-if="slotProps.item.isExit" style="color: #495EEB; cursor: pointer"&ndash;&gt;-->
                <!--                            &lt;!&ndash;                                    @click="sendExitRequest(slotProps.item.data.myguid)">{{ $t('account_information.exit') }}</span>&ndash;&gt;-->
                <!--                            &lt;!&ndash;                            </div>&ndash;&gt;-->
                <!--                            <div>-->
                <!--                              <span v-if="slotProps.item.isRecover" style="color: #495EEB"-->
                <!--                                    @click="recoveryMTAccount(slotProps.item.data.login, slotProps.item.data.accountName)">{{-->
                <!--                                  $t('account_information.recover')-->
                <!--                                }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                        </div>-->
                <!--                      </template>-->
                <!--                    </card-dragger>-->
                <!--                    <el-empty v-else :description="$t('account_information.empty')"></el-empty>-->
                <!--                  </div>-->
                <!--                  <div v-else>-->
                <!--                    <card-dragger-->
                <!--                      v-if="mt5HiddenCardList.length!==0"-->
                <!--                      :data="mt5HiddenCardList"-->
                <!--                      :colNum="2"-->
                <!--                      :cardInsideHeight="150"-->
                <!--                      :cardOutsideHeight="170"-->
                <!--                    >-->
                <!--                      <template v-slot:header="slotProps">-->
                <!--                        <div style="margin-left: 20px;padding-top: 10px">-->
                <!--                          <span style="color: #0b0b0b">{{ $t('sidebar.name') }}: {{-->
                <!--                              slotProps.item.data.accountName-->
                <!--                            }}</span>-->
                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.show_account')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="hideMt5Account(slotProps.item)" class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-eye-slash"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          <b-button variant="outline-link"-->
                <!--                                    v-b-tooltip.hover.top="$t('account_information.balance_floating_pnl')"-->
                <!--                                    @click="getTradingAccountBalanceAndFloatingProfit(slotProps.item)"-->
                <!--                                    class="card-button">-->
                <!--                            <i class="fas fa-search-dollar"></i>-->
                <!--                          </b-button>-->
                <!--                          &lt;!&ndash;                          <b-button variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.reset_mt_password')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="resetTradingAccountPassword(slotProps.item)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-key"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkWithdrawal(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.withdrawal')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToWithdrawalPage"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="fas fa-money-check-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          <b-button v-if="checkDeposit(slotProps.item.data)" variant="outline-link"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    v-b-tooltip.hover.top="$t('account_information.deposit')"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    @click="jumpToDepositPage(slotProps.item.data)"&ndash;&gt;-->
                <!--                          &lt;!&ndash;                                    class="card-button">&ndash;&gt;-->
                <!--                          &lt;!&ndash;                            <i class="far fa-money-bill-alt"></i>&ndash;&gt;-->
                <!--                          &lt;!&ndash;                          </b-button>&ndash;&gt;-->
                <!--                        </div>-->
                <!--                        <div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('dashboard1.balance') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.balance===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.balance!==null || slotProps.item.data.balance!==undefined">{{-->
                <!--                                slotProps.item.data.balance-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                          <div style="margin-left: 20px;padding-bottom: 10px;float: left">-->
                <!--                            <span>{{ $t('account_information.floating') }} : </span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="!slotProps.item.balanceAndFloatingPnlVisible && slotProps.item.data.floatingPnl===undefined">&#45;&#45;</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.data.floatingPnl!==null || slotProps.item.data.floatingPnl!==undefined">{{-->
                <!--                                slotProps.item.data.floatingPnl-->
                <!--                              }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px">{{ slotProps.item.data.currency }}</span>-->
                <!--                            <span style="color: #0b0b0b;margin-right: 5px"-->
                <!--                                  v-if="slotProps.item.balanceAndFloatingPnlVisible"><i-->
                <!--                              class="el-icon-loading"></i></span>-->
                <!--                          </div>-->
                <!--                        </div>-->

                <!--                      </template>-->
                <!--                      <template v-slot:content="slotProps">-->
                <!--                        <div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.mt5login') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.login }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.account_type') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.accountType }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.leverage') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.leverage }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            <div>-->
                <!--                              <span style="color: #b9b9be">{{ $t('account_information.currency') }}</span>-->
                <!--                            </div>-->
                <!--                            <div>-->
                <!--                              <span style="color: #0b0b0b">{{ slotProps.item.data.currency }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                          <div style="padding: 10px 10px 10px 10px;float: left">-->
                <!--                            &lt;!&ndash;                            <div>&ndash;&gt;-->
                <!--                            &lt;!&ndash;                              <span v-if="slotProps.item.isExit" style="color: #495EEB; cursor: pointer"&ndash;&gt;-->
                <!--                            &lt;!&ndash;                                    @click="sendExitRequest(slotProps.item.data.myguid)">{{ $t('account_information.exit') }}</span>&ndash;&gt;-->
                <!--                            &lt;!&ndash;                            </div>&ndash;&gt;-->
                <!--                            <div>-->
                <!--                              <span v-if="slotProps.item.isRecover" style="color: #495EEB"-->
                <!--                                    @click="recoveryMTAccount(slotProps.item.data.login, slotProps.item.data.accountName)">{{-->
                <!--                                  $t('account_information.recover')-->
                <!--                                }}</span>-->
                <!--                            </div>-->
                <!--                          </div>-->
                <!--                        </div>-->
                <!--                      </template>-->
                <!--                    </card-dragger>-->
                <!--                    <el-empty v-else :description="$t('account_information.empty')"></el-empty>-->
                <!--                  </div>-->
                <!--                </div>-->
              </div>
            </template>
          </iq-card>
        </b-col>
      </b-row>
    </b-container>
    <!--    <wd-loading v-if="tradingAccountLoading" class="hidden-lg-and-up mobile-loading"></wd-loading>-->
    <!--    <MobileAccountInformationPage @resetPassword="resetTradingAccountPasswordMobile" v-if="!tradingAccountLoading"-->
    <!--                                  @isType="changeType"-->
    <!--                                  :trading-account-list="tradingAccountList"-->
    <!--                                  :cancelSubRequest="cancelSubRequest"-->
    <!--                                  class="hidden-lg-and-up"></MobileAccountInformationPage>-->

    <!--    <ResetTradingAccountPassword v-if="resetTradingAccountPasswordVisible" :object="tradingAccountObject"-->
    <!--                                 :type="mt4Activet ? 'MT4' : 'MT5'"-->
    <!--                                 :close="closeDialog"-->
    <!--                                 :visible="resetTradingAccountPasswordVisible"></ResetTradingAccountPassword>-->

    <!--  LPOA  -->
    <el-dialog
      width="800"
      :show-close="false"
      :close-on-click-modal="false"
      :visible.sync="LPOAShow"
      title="MYFX Markets Limited Power of Attorney"
    >
      <JoinAgainLPOA
        @submitFormStep3="lpoaSubmit"
        :LPOAVisible.sync="LPOAShow"
        :country="lpoaForm.country"
        :contractid="lpoaForm.lpoaContractId"
        :actionparam="lpoaForm.actionParam"
        :actioncode="lpoaForm.actionMessage"
        :firstname="lpoaForm.firstname"
        :lastname="lpoaForm.lastname"
        :ibname="lpoaForm.ibName"
        :ib-code="lpoaForm.ibcode"
        :coguid="lpoaForm.myguid"
        :mt4AccountGuid="lpoaForm.mt4AccountGuid"
      />
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  fetchTradingAccountBalance,
  fetchTradingAccountParams,
  getMamPamCancelSubRequest,
  getStrategyName,
  recoveryMTAccount,
  iBCodeMapping,
  sendExitRequest
} from '@/services/account.js'
import { getLocal } from '@/Utils/authLocalStorage'
import { cardDragger } from 'carddragger'
import { Message, MessageBox } from 'element-ui'
import ResetTradingAccountPassword from '@/views/MyAccount/component/ResetTradingAccountPassword'
import MobileAccountInformationCard from '@/views/MyAccount/component/MobileAccountInformationCard'
import MobileAccountInformationPage from '@/views/MyAccount/component/MobileAccountInformationPage'
import JoinAgainLPOA from '@/views/MyAccount/component/JoinAgainLPOA.vue'
import sha256 from 'crypto-js/sha256'
import md5 from 'crypto-js/md5'

export default {
  name: 'AccountInformation',
  components: {
    MobileAccountInformationPage,
    MobileAccountInformationCard,
    ResetTradingAccountPassword,
    cardDragger,
    JoinAgainLPOA
  },

  provide () {
    return {
      updateList: this.getTradingAccountList
    }
  },

  data () {
    return {
      mt4Index: true,
      mt4Activet: true,
      mt5Index: false,
      mt5Activet: false,
      mt4VisibleCardList: [],
      mt4HiddenCardList: [],
      mt4Hidden: false,
      mt5VisibleCardList: [],
      mt5HiddenCardList: [],
      mt5Hidden: false,
      hideDialogVisible: false,
      resetTradingAccountPasswordVisible: false,
      generalInformation: {
        name: '',
        emailAddress: '',
        phoneNumber: ''
      },
      tradingAccountList: [],
      tradingAccount: {
        login: '',
        accountName: '',
        leverage: '',
        accountType: '',
        currency: ''
      },
      tradingAccountLoading: false,
      balanceAndFloatingPnlVisible: false,
      balance: 0.00,
      floatingPnl: 0.00,
      tradingAccountObject: null,

      cancelSubRequest: [],

      // -------------------
      viewAllMT4Loading: false,
      viewAllMT5Loading: false,

      LPOAShow: false,
      lpoaForm: {
        country: '',
        lpoaContractId: '',
        actionParam: '',
        actionMessage: '',
        firstname: '',
        lastname: '',
        mt4AccountGuid: '',
        ibName: '',
        ibcode: '',
        myguid: ''
      }
    }
  },
  computed: {
    ...mapGetters({
      stateUsers: 'Setting/usersState'
    })
  },
  created () {
    this.getGeneralInformation()
    this.getTradingAccountList()
  },
  methods: {
    mt4Click () {
      this.mt4Index = true
      this.mt4Activet = true
      this.mt5Activet = false
      this.mt5Index = false
    },
    mt5Click () {
      this.mt4Index = false
      this.mt4Activet = false
      this.mt5Activet = true
      this.mt5Index = true
    },
    getGeneralInformation () {
      const user = JSON.parse(getLocal('user'))
      this.generalInformation.name = user.firstname + '    ' + user.lastname
      this.generalInformation.emailAddress = user.email
      this.generalInformation.phoneNumber = user.tel

      if (user.accounttype === 'Company') {
        this.generalInformation.name = user.companyname
      }
    },
    getTradingAccountList () {
      this.mt4VisibleCardList = []
      this.mt4HiddenCardList = []
      this.mt5VisibleCardList = []
      this.mt5HiddenCardList = []
      const user = JSON.parse(getLocal('user'))
      this.tradingAccountLoading = true
      const myGuid = user.myguid
      const email = user.email
      fetchTradingAccountParams(myGuid, email).then(res => {
        this.tradingAccountList.push(res.data.mt4AccountVoList)
        this.tradingAccountList.push(res.data.mt5AccountVoList)
        console.log(this.tradingAccountList)
        const mt4TradingAccountList = res.data.mt4AccountVoList
        const mt5TradingAccountList = res.data.mt5AccountVoList
        for (let i = 0; i < mt4TradingAccountList.length; i++) {
          const item = mt4TradingAccountList[i]
          /* if (this.$i18n.locale === 'ja_JP') {
            if (item.accountType === 'MAM Slave') {
              mt4TradingAccountList[i].accountType = 'MAM口座'
            } else if (item.accountType === 'PAMM Slave') {
              mt4TradingAccountList[i].accountType = 'PAMM口座'
            } else if (item.accountType === 'MAM Master') {
              mt4TradingAccountList[i].accountType = 'MAMマスター'
            } else if (item.accountType === 'PAMM Master') {
              mt4TradingAccountList[i].accountType = 'PAMMマスター'
            } else if (item.accountType === 'Standard') {
              mt4TradingAccountList[i].accountType = 'スタンダード'
            } else if (item.accountType === 'Pro') {
              mt4TradingAccountList[i].accountType = 'プロ'
            } else {
              mt4TradingAccountList[i].accountType = 'その他'
            }
          } */

          const cardItem = {
            positionNum: i + 1,
            name: 'card' + (i + 1),
            id: 'card' + (i + 1),
            balanceAndFloatingPnlVisible: false,
            hidden: false,
            isExit: false,
            isJoin: false,
            isBeingProcessed: false,
            isJoinBeingProcessed: false,
            isRecoveryBeingProcessed: mt4TradingAccountList[i]?.status == -5,
            isRecover: mt4TradingAccountList[i]?.status == -3,
            loading: false,
            data: {},
            cancelData: {}
          }

          if (item.mt4group && (item.mt4group.indexOf('-SUB') !== -1 || item.mt4group.indexOf('-CLIENT') !== -1 || item.mt4group.indexOf('-CLT') !== -1)) {
            if (!item.mt4group.endsWith('-REMOVED')) {
              cardItem.isExit = true
            }
          }

          cardItem.data = { ...mt4TradingAccountList[i], balance: '', floatingPnl: '', loading: false }
          this.mt4VisibleCardList.push(cardItem)
        }
        for (let i = 0; i < mt5TradingAccountList.length; i++) {
          const item = mt5TradingAccountList[i]
          const cardItem = {
            positionNum: i + 1,
            name: 'card' + (i + 1),
            id: 'card' + (i + 1),
            balanceAndFloatingPnlVisible: false,
            hidden: false,
            isExit: false,
            isJoin: false,
            isBeingProcessed: false,
            isJoinBeingProcessed: false,
            isRecoveryBeingProcessed: mt4TradingAccountList[i]?.status == -5,
            isRecover: mt5TradingAccountList[i]?.status == -3,
            loading: false,
            data: {},
            cancelData: {}
          }
          /* if (this.$i18n.locale === 'ja_JP') {
            if (item.accountType === 'st_mt5') {
              mt5TradingAccountList[i].accountType = 'スタンダード'
            } else if (item.accountType === 'pro_mt5') {
              mt5TradingAccountList[i].accountType = 'プロ'
            }
          } */

          cardItem.data = { ...mt5TradingAccountList[i], balance: '', floatingPnl: '', loading: false }
          this.mt5VisibleCardList.push(cardItem)
        }
        this.tradingAccountLoading = false

        // TODO: ...
        this.getCancelSubRequest()
      })
    },

    // 查看所有
    async lookAll () {
      if (this.mt4Activet) {
        if (this.viewAllMT4Loading) return
        this.viewAllMT4Loading = true
      } else {
        if (this.viewAllMT5Loading) return
        this.viewAllMT5Loading = true
      }
      for (const item of (this.mt4Activet ? this.mt4VisibleCardList : this.mt5VisibleCardList)) {
        await this.getTradingAccountBalanceAndFloatingProfit(item)
      }
      // this.viewAllLoading = false
      if (this.mt4Activet) {
        this.viewAllMT4Loading = false
      } else {
        this.viewAllMT5Loading = false
      }
    },

    async getTradingAccountBalanceAndFloatingProfit (item) {
      try {
        this.$vp.add({
          message: `[AccountInformation] Click account [${ item.data.accountName } ${ item.data.login }] show balance button.`
        })

        item.data.loading = true
        // item.balanceAndFloatingPnlVisible = true
        const res = await fetchTradingAccountBalance(item.data.myguid)
        // .then(res => {
        if (item.data.currency === 'JPY') {
          this.balance = res.data.balance.toLocaleString()
          this.floatingPnl = res.data.floatingPnl.toLocaleString()
        } else {
          this.balance = res.data.balance.toFixed(2)
          this.floatingPnl = parseFloat(res.data.floatingPnl).toFixed(2)
        }

        item.data.loading = false
        item.data.balance = this.balance
        item.data.floatingPnl = this.floatingPnl
      } catch (err) {
        item.data.loading = false
      }

      // this.mt4VisibleCardList.map(card => {
      //   if (card.data.myguid === item.data.myguid) {
      //     card.data.balance = this.balance
      //     card.data.floatingPnl = this.floatingPnl
      //   }
      // })
      // this.mt4HiddenCardList.map(card => {
      //   if (card.data.myguid === item.data.myguid) {
      //     card.data.balance = this.balance
      //     card.data.floatingPnl = this.floatingPnl
      //   }
      // })
      // this.mt5VisibleCardList.map(card => {
      //   if (card.data.myguid === item.data.myguid) {
      //     card.data.balance = this.balance
      //     card.data.floatingPnl = this.floatingPnl
      //   }
      // })
      // this.mt5HiddenCardList.map(card => {
      //   if (card.data.myguid === item.data.myguid) {
      //     card.data.balance = this.balance
      //     card.data.floatingPnl = this.floatingPnl
      //   }
      // })
      // item.balanceAndFloatingPnlVisible = false
      // })
    },
    showMt4HiddenTradingAccountList () {
      this.mt4Hidden = !this.mt4Hidden
      this.mt5Hidden = !this.mt5Hidden

      this.$vp.add({
        message: '[AccountInformation] Click [Show Hidden Account] button.'
      })
    },
    hideMt4Account (item) {
      this.$vp.add({
        message: `[AccountInformation] Click account [${ item.data.accountName } ${ item.data.login }] hide account button.`
      })

      this.hideDialogVisible = true
      if (!item.hidden) {
        this.mt4VisibleCardList = this.mt4VisibleCardList.filter((card) => {
          return item.data.myguid !== card.data.myguid
        })
        item.hidden = true
        this.mt4HiddenCardList.push(item)
      } else {
        this.mt4HiddenCardList = this.mt4HiddenCardList.filter(card => {
          return item.data.myguid !== card.data.myguid
        })
        item.hidden = false
        this.mt4VisibleCardList.push(item)
      }
      this.resetCardList(this.mt4VisibleCardList, this.mt4HiddenCardList)
    },
    hideMt5Account (item) {
      this.hideDialogVisible = true
      if (!item.hidden) {
        this.mt5VisibleCardList = this.mt5VisibleCardList.filter((card) => {
          return item.data.myguid !== card.data.myguid
        })
        item.hidden = true
        this.mt5HiddenCardList.push(item)
      } else {
        this.mt5HiddenCardList = this.mt5HiddenCardList.filter(card => {
          return item.data.myguid !== card.data.myguid
        })
        item.hidden = false
        this.mt5VisibleCardList.push(item)
      }
      this.resetCardList(this.mt5VisibleCardList, this.mt5HiddenCardList)
    },
    resetCardList (visibleCardList, hiddenCardList) {
      visibleCardList.forEach((item, index) => {
        item.positionNum = index + 1
        item.name = 'card' + (index + 1)
        item.id = 'card' + (index + 1)
      })
      hiddenCardList.forEach((item, index) => {
        item.positionNum = index + 1
        item.name = 'card' + (index + 1)
        item.id = 'card' + (index + 1)
      })
    },
    resetTradingAccountPassword (item) {
      this.$vp.add({
        message: `[AccountInformation] Click account [${ item.data.accountName } ${ item.data.login }] reset password button.`
      })

      this.tradingAccountObject = item.data

      this.resetTradingAccountPasswordVisible = true
    },
    resetTradingAccountPasswordMobile (item) {
      this.tradingAccountObject = item
      this.resetTradingAccountPasswordVisible = true
    },
    changeType (tab) {
      if (tab === 0) {
        this.mt4Activet = true
      } else {
        this.mt4Activet = false
      }
    },
    closeDialog () {
      this.tradingAccountObject = null
      this.resetTradingAccountPasswordVisible = false
    },
    jumpToDepositPage (data) {
      this.$vp.add({
        message: `[AccountInformation] Click account [${ data.accountName } ${ data.login }] deposit button.`
      })

      this.$store.commit('commonSet', { // 通用方法， 多个key就继续往后写,打比方
        keys: ['defaultDepositTarget'], //  ['test', 'test']   ['testArr', 0] 这样都行
        data: data
      })
      this.$router.push({
        name: 'production.transaction',
        params: { name: 'deposit' }
      })
    },
    jumpToWithdrawalPage (data) {
      this.$vp.add({
        message: `[AccountInformation] Click account [${ data.accountName } ${ data.login }] withdrawal button.`
      })

      this.$router.push({
        name: 'production.transaction',
        params: { name: 2 }
      })
    },
    checkWithdrawal (item) {
      return !item.mt4group?.includes('MAM') &&
        !item.mt4group?.includes('PAMM') &&
        !item.mt4group?.toLowerCase()?.includes('rebate') &&
        !item.accountType?.includes('ctrader') &&
        !item.mt4group?.toLowerCase()?.includes('disabled') &&
        !item.mt4group?.toLowerCase()?.includes('removed') &&
        !item.accountType?.toLowerCase()?.includes('master')
    },
    checkDeposit (item) {
      if (!item.mt4group?.toLowerCase()?.includes('rebate') &&
        !item.mt4group?.toLowerCase()?.includes('-pf') &&
        !item.accountType?.toLowerCase()?.includes('ctrader') &&
        !item.mt4group?.toLowerCase()?.includes('disabled') &&
        !item.mt4group?.toLowerCase()?.includes('removed') /* &&
          !item.accountType.toLowerCase().includes('master') */) {
        if ((item.mt4group?.includes('MAM') || item.mt4group?.includes('PAMM'))) {
          if (item.mt4group?.toUpperCase()?.includes('-SUB-') ||
            item.mt4group?.toUpperCase()?.endsWith('-CLT') ||
            item.mt4group?.toUpperCase()?.endsWith('-CLIENT')) {
            // for testing purpose.)
            return true
          }
        } else {
          return true
        }

        return false
      }
    },
    async getCancelSubRequest () {
      try {
        this.tradingAccountLoading = true
        const user = JSON.parse(getLocal('user'))
        const myGuid = user.myguid
        const { code, data } = await getMamPamCancelSubRequest(myGuid)
        if (code === 200) {
          this.cancelSubRequest = data || []

          this.cancelSubRequest.forEach(v => {
            const targetExit = this.mt4VisibleCardList
              .filter(f => f.data.accountType === 'MAM Slave' || f.data.accountType === 'PAMM Slave')
              .find(item => item.data.myguid === v.mt4accountMyguid)
            if (targetExit) {
              targetExit.cancelData = v
              targetExit.isExit = v.status === -2 || v.status === ''
              targetExit.isJoin = v.status == 1
              targetExit.isBeingProcessed = v.status == 0
              targetExit.isJoinBeingProcessed = v.status == 2
            } else {

            }
          })
        }
        this.tradingAccountLoading = false
      } catch (err) {
        this.tradingAccountLoading = false
      }
    },

    async sendExitRequest (item, mt4AccountMyguid, account, login, server) {
      try {
        this.$vp.add({
          message: `[AccountInformation] Click [${ account } ${ login }] exit from strategy.`
        })

        item.loading = true

        // 获取MasterName
        const { data } = await getStrategyName(login, server)
        item.loading = false

        await MessageBox.confirm(
          `<i class=\"el-icon-warning\" style="color: #495eeb; font-size: 20px; transform: translateY(2px)"></i> ${ data } ${ this.$t('account_information.exitMessage') }`,
          '', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('account_information.msgBox.sure'),
            cancelButtonText: this.$t('account_information.msgBox.cancel')
          })

        this.$vp.add({
          message: `[AccountInformation] Click [${ account } ${ login }] exit from strategy [confirm].`
        })

        item.loading = true
        const user = JSON.parse(getLocal('user'))
        const myGuid = user.myguid
        const { code, _, msg } = await sendExitRequest(myGuid, mt4AccountMyguid)
        if (code === 200) {
          Message.success(msg)
          this.getTradingAccountList()
        }
      } catch (err) {
        item.loading = false
        this.$vp.add({
          message: `[AccountInformation] Click [${ account } ${ login }] exit from strategy [${ err }].`
        })
      }
    },

    /**
     * @description 账户恢复
     */
    async recoveryMTAccount (item, login, account) {
      try {
        this.$vp.add({
          message: `[AccountInformation] Click [${ account } ${ login }] recovery MT5 account.`
        })

        await MessageBox.confirm(`<i class=\"el-icon-warning\" style="color: #495eeb; font-size: 20px; transform: translateY(2px)"></i> ${ login } ${ this.$t('account_information.recoverMessage') } `, '',
          {
            // type: 'warning',
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('account_information.msgBox.sure'),
            cancelButtonText: this.$t('account_information.msgBox.cancel')
          }
        )

        this.$vp.add({
          message: `[AccountInformation] Click [${ account } ${ login }] recovery MT5 account [confirm].`
        })

        item.loading = true

        const { code, _, msg } = await recoveryMTAccount(item.data.myguid)
        if (code === 200) {
          Message.success(this.$t('account_information.recoveryAccountSuccessMessage'))
          this.getTradingAccountList()
        }
        item.loading = false
      } catch (err) {
        item.loading = false
        this.$vp.add({
          message: `[AccountInformation] Click [${ account } ${ login }] recovery MT5 account [${ err }].`
        })
      }
    },

    /**
     * @description 重新加入
     */
    async joinAgain (item) {
      try {
        this.lpoaForm.country = item.data.currency
        this.lpoaForm.firstname = JSON.parse(getLocal('user')).firstname
        this.lpoaForm.lastname = JSON.parse(getLocal('user')).lastname
        this.lpoaForm.mt4AccountGuid = item.data.myguid
        this.lpoaForm.ibcode = item.cancelData.mampammmaster
        this.lpoaForm.myguid = JSON.parse(getLocal('user')).myguid

        const tokenSha256 = sha256(`${ this.lpoaForm.ibcode }|${ this.lpoaForm.myguid }asufh2397jdhg`)

        item.loading = true

        const { data } = await iBCodeMapping({
          ibCode: this.lpoaForm.ibcode,
          coGuid: this.lpoaForm.myguid,
          token: md5(`${ tokenSha256.toString() }vgyuw89asd9fsd923`).toString()
        })

        this.lpoaForm.lpoaContractId = ''
        this.lpoaForm.actionParam = data.actionparams
        this.lpoaForm.actionMessage = data.actioncode
        this.lpoaForm.ibName = data.ibname

        this.LPOAShow = true
        item.loading = false
      } catch (err) {

        item.loading = false
      }
    },

    async lpoaSubmit () {
      try {
        this.LPOAShow = false
        this.getTradingAccountList()
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.idCard {
  //margin-left: 15px;
  width: 560px;
}

.app-container {
  ::v-deep .el-table {
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
      .el-table__cell {
        padding: 10px 0;
        color: #fff;
        background: #495EEB;
      }
    }
  }

  .account-list {
    margin-top: 30px;

    &-item {
      gap: 10px;
      display: flex;
      padding: 14px;
      //border: 1px solid #eee;
      border-radius: 10px;
      background-color: #fff;

      &-column {
        flex: 1;

        &-label {
          color: #aaa;
        }

        &-value {
          font-weight: bold;
        }
      }
    }

    .account-list-item ~ .account-list-item {
      margin-top: 20px;
    }
  }
}

@media screen and (max-width: 767px) {
  .idCard {
    transition: all 0.3s;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
  }

  .typeChange {
    align-items: flex-start !important;
    flex-direction: column;
  }

  .account-list-item {
    flex-wrap: wrap;

    &-column {
      flex: unset !important;
      width: 30%;
    }
  }
}

.card-button {
  color: rgba(0, 0, 0, 0.8);
  font-size: 20px;
  float: right;
  padding-left: 0;
  padding-right: 2px
}

.card-button:hover {
  color: rgba(125, 125, 127, 0.78);
}

.mt4, .mt5 {
  border: 1px #DCDFE6 solid;
  background-color: #FFFFFF;
  margin-left: 10px;
  height: 50px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
}

.mt5 {
  width: 110px;
}

.mt4 {
  width: 110px;
  margin: 0;
}

.mt5:hover {
  font-weight: 1000;
  //background-color: #ebecfb;
}

.mt4:hover {
  font-weight: 1000;
  //background-color: #ebedfb;
}

.mt5One {
  border: 1px #DCDFE6 solid;
  margin-left: 10px;
  height: 50px;
  width: 110px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
  font-weight: 1000;
  //background-color: #ebecfb;
  background-color: #03ff96;
}

.mt4One {
  border: 1px #DCDFE6 solid;
  //margin-left: 10px;
  height: 50px;
  width: 110px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
  font-weight: 1000;
  //background-color: #ebeefb;
  background-color: #03ff96;
}

.mobile-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

</style>
