<template>
  <div>
    <div v-loading="accountLoading" class="app-container" v-if="true">
      <div class="tab">
        <div
          v-viewpoint.click="{ message: `[Account Transactions] Click ${item.type}` }"
          class="tab-item"
          :class="{ 'tab-active': item.id === tabActive }"
          v-for="item in tabOption"
          :key="item.id"
          @click="tabChange(item.id)"
        >
          <span></span>
          <span></span>
          <span></span>
          <span></span>
          <img v-if="item.icon" width="20" :src="item.id === tabActive ? item.iconSelect : item.icon" class="tab-icon">
          {{ $t(item.name) }}
        </div>
      </div>

      <b-row class="content" v-if="!accountLoading">
        <b-col lg="9" xl="9" class="content-main" v-if="tabActive < 4">
          <deposit ref="deposit" v-if="tabActive === 1" @selectEnter="selectEnter"></deposit>
          <!--          <Whitdrawals v-if="tabActive === 2"/>-->
          <Withdrawal ref="withdrawal" v-if="tabActive === 2" @japanEnter="japanEnter"/>
          <internal-fund-transfer ref="internalTransfer" v-if="tabActive === 3"></internal-fund-transfer>

        </b-col>
        <b-col lg="3" xl="3" v-if="tabActive < 4">
          <HintContent
            v-if="tabActive === 1"
            :contentArray="DepositJPTIP()"
          />
          <HintContent
            v-if="tabActive === 2"
            withDraw
            :withEnter="japanEnterName"
            :contentArray="JPTip()"
          />
          <HintContent
            v-if="tabActive === 3"
            :contentArray="[
              {
                title: $t('transaction.transaction.tips.test1'),
                html: true
              },
              $t('transaction.transaction.tips.test3'),
              $t('transaction.transaction.tips.test4'),
            ]"
          />
        </b-col>

        <b-col v-if="tabActive === 4">
          <!--          <div class="tab">
                      <div
                        class="tab-item"
                        :class="{ 'tab-active': item.id === tableActive }"
                        v-for="item in tableOption"
                        :key="item.id"
                        v-viewpoint.click="{ message: `[Transaction History] Click [${item.en}] button.` }"
                        @click="tableChange(item.id)"
                      >
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                        {{ $t(item.name) }}
                      </div>
                    </div>-->

          <!-- TableData -->
          <div class="table-title">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                {{ $t(tableMap[tableActive]) }} <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in tableOption"
                  :key="item.id"
                  :command="item.id"
                  v-viewpoint.click="{ message: `[Transaction History] Click [${item.en}] button.` }"
                  @click="tableChange(item.id)">{{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-input v-model="tableKeyword" placeholder="Search" size="small" @input="tableKeywordSearch"/>
          </div>
          <el-table
            :data="tableDataSlice"
            border
            stripe
            style="width: 100%"
            v-loading="tableLoading"
            @sort-change="sortChange"
          >
            <el-table-column
              :min-width="item.width || 120"
              v-for="item in getTableColumns()"
              :key="item.key"
              :prop="item.key"
              :label="item.label"
              :sortable="item.sort ? 'custom' : false"
            >
              <template slot="default" slot-scope="{row}">
                <div v-if="item.key === 'transactionType'">{{ $t(row.transactionTypeKey) }}</div>
                <div v-else-if="item.key === 'processedTime'" style="word-break: auto-phrase">
                  {{ row.processedTime }}
                </div>
                <div v-else-if="item.key === 'createTime'" style="word-break: auto-phrase">
                  {{ row.createTime }}
                </div>
                <div v-else-if="item.key === 'amount'">
                  {{ row.amountValue }}
                </div>
                <div v-else-if="item.key === 'status'">
                  {{ $t(row.statusKey) }}
                  <span v-if="row.preWithdrawSuccess === 0 && row.status.toUpperCase() === 'PROCESSING'"
                        @click="cancelWithdrawalDepositRequest({item:row})"
                        style="color: #4a5eeb; text-decoration: underline; cursor: pointer">
                    {{ $t('sidebar.cancel') }}
                  </span>
                  <!--                  <b-button class="btn-sm" variant="outline-danger"
                                              v-if="row.preWithdrawSuccess === 0 && row.status.toUpperCase() === 'PROCESSING'"
                                              @click="cancelWithdrawalDepositRequest({item:row})">{{ $t('sidebar.cancel') }}
                                      &lt;!&ndash;                            <i style="padding: 0;margin-left: 5px" class="el-icon-loading"></i>&ndash;&gt;
                                    </b-button>-->
                </div>
                <template v-else-if="item.key === 'mt4Login'">
                  <div v-if="row.mt4LoginKey">{{ $t(row.mt4LoginKey) }}</div>
                  <div v-else>{{ row.mt4Login }}</div>
                </template>
                <div v-else>{{ row[item.key] }}</div>
              </template>
              <!--              <template slot="header">-->
              <!--                <div v-if="item.select" style="display: flex;align-items: center;" :key="item.key">-->
              <!--                  <div>{{ item.label }}</div>-->
              <!--                  <el-select-->
              <!--                    v-model="selectMap.depositType"-->
              <!--                    size="mini"-->
              <!--                    style="flex: 1;margin-left: 10px;"-->
              <!--                    clearable-->
              <!--                  >-->
              <!--                    <el-option-->
              <!--                      v-for="opt in depositType"-->
              <!--                      :key="opt"-->
              <!--                      :label="opt"-->
              <!--                      :value="opt"-->
              <!--                    ></el-option>-->
              <!--                  </el-select>-->
              <!--                </div>-->
              <!--              </template>-->
            </el-table-column>
          </el-table>

          <el-pagination
            style="margin-top: 10px"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="this.tableData.length"
            :current-page="offset"
            :page-size="limit"
            :page-sizes="[10, 25, 50, 100]"
            @size-change="sizeChange"
            @current-change="currentChange"
          >
          </el-pagination>

          <!--  deposits and withdrawal  -->
          <!--          <div class="table-up-bar">{{ $t(tableMap[tableActive]) }}</div>-->
          <!--          <div v-loading="depositWithdrawalHistoryLoading || submitLoading" v-if="tableActive < 3">-->

          <!--            <div style="overflow-y: auto;">-->
          <!--              <b-table style="position:relative;z-index: 2"-->
          <!--                       class="hidden-md-and-down bTable"-->
          <!--                       :items="depositWithdrawalHistoryPageParams.pageData"-->
          <!--                       :fields="!withdrawaActivet? depositWithdrawalHistoryColumns : withdrawalHistoryColumns">-->
          <!--                <template v-slot:cell(direction)="data">-->
          <!--                  <div>-->
          <!--                    {{ $t('transaction.deposit.table.tra_type.' + data.item.direction) }}-->
          <!--                  </div>-->
          <!--                </template>-->
          <!--                <template v-slot:cell(transactionType)="data">-->
          <!--                  <div>{{ $t(data.item.transactionTypeKey) }}</div>-->
          <!--                </template>-->
          <!--                <template v-slot:cell(status)="data">-->
          <!--                  <div v-if="data.item.status.toLowerCase() === 'deleted'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.CANCELLED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'not paid'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.PENDING') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'processing'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.PROCESSING') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'mampamm'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.MAMPAMM') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'processed'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.PROCESSED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'cancelled'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.CANCELLED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'failed'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.FAILED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else>-->
          <!--                    {{ data.item.status }}-->
          <!--                  </div>-->
          <!--                  <div>-->
          <!--                    <b-button class="btn-sm" variant="outline-danger"-->
          <!--                              v-if="data.item.preWithdrawSuccess === 0 && data.item.status.toUpperCase() === 'PROCESSING'"-->
          <!--                              @click="cancelWithdrawalDepositRequest(data)">{{ $t('sidebar.cancel') }}-->
          <!--                      &lt;!&ndash;                            <i style="padding: 0;margin-left: 5px" class="el-icon-loading"></i>&ndash;&gt;-->
          <!--                    </b-button>-->
          <!--                  </div>-->
          <!--                </template>-->
          <!--              </b-table>-->
          <!--              <MobileTable key="1" :show-first="2"-->
          <!--                           class="hidden-lg-and-up"-->
          <!--                           :fields="!withdrawaActivet? depositWithdrawalHistoryColumns : withdrawalHistoryColumns"-->
          <!--                           :items="depositWithdrawalHistoryPageParams.pageData"></MobileTable>-->
          <!--            </div>-->
          <!--            <el-pagination style="margin-top: 10px" popper-class='select_bottom'-->
          <!--                           class="msg-pagination-container hidden-md-and-down" :background="true"-->
          <!--                           layout="total, sizes, prev, pager, next, jumper"-->
          <!--                           :total="depositWithdrawalHistoryPageParams.total"-->
          <!--                           :current-page.sync="depositWithdrawalHistoryPageParams.page"-->
          <!--                           :page-count="depositWithdrawalHistoryPageParams.totalPage"-->
          <!--                           :page-size="depositWithdrawalHistoryPageParams.pageSize" :page-sizes="[5,10,30,50]"-->
          <!--                           @size-change="changeDepositWithdrawalHistoryPage"-->
          <!--                           @current-change="getDepositWithdrawalHistoryList">-->
          <!--            </el-pagination>-->
          <!--            <MobilePagination v-if="!depositWithdrawalHistoryLoading" key="1"-->
          <!--                              @current-change="getDepositWithdrawalHistoryList"-->
          <!--                              :current-page.sync="depositWithdrawalHistoryPageParams.page"-->
          <!--                              :total-amount="depositWithdrawalHistoryPageParams.total" class="hidden-lg-and-up">-->
          <!--            </MobilePagination>-->
          <!--          </div>-->

          <!-- transfer  -->
          <!--          <div v-loading="internalTransactionHistoryLoading" v-if="tableActive === 3">-->
          <!--            <div style="overflow-y: auto;">-->
          <!--              <b-table class="hidden-md-and-down bTable"-->
          <!--                       :items="internalTransactionHistoryPageParams.pageData"-->
          <!--                       :fields="internalTransactionHistoryColumns">-->
          <!--                <template v-slot:cell(status)="data">-->
          <!--                  <div v-if="data.item.status.toLowerCase() === 'deleted'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.CANCELLED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'not paid'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.PENDING') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'processing'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.PROCESSING') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'mampamm'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.MAMPAMM') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'processed'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.PROCESSED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else-if="data.item.status.toLowerCase() === 'cancelled'">-->
          <!--                    {{ $t('transaction.deposit.table.sta_type.CANCELLED') }}-->
          <!--                  </div>-->
          <!--                  <div v-else>-->
          <!--                    {{ data.item.status }}-->
          <!--                  </div>-->
          <!--                  <div>-->
          <!--                    <b-button class="btn-sm" variant="outline-danger"-->
          <!--                              v-if="data.item.preWithdrawSuccess === 0 && data.item.status.toUpperCase() === 'PROCESSING'"-->
          <!--                              @click="cancelWithdrawalDepositRequest(data)">{{ $t('sidebar.cancel') }}-->
          <!--                      &lt;!&ndash;                            <i style="padding: 0;margin-left: 5px" class="el-icon-loading"></i>&ndash;&gt;-->
          <!--                    </b-button>-->
          <!--                  </div>-->
          <!--                </template>-->
          <!--              </b-table>-->
          <!--              <MobileTable key="2" :show-first="2"-->
          <!--                           class="hidden-lg-and-up" :fields="internalTransactionHistoryColumns"-->
          <!--                           :items="internalTransactionHistoryPageParams.pageData"></MobileTable>-->
          <!--            </div>-->
          <!--            <el-pagination style="margin-top: 10px" popper-class='select_bottom'-->
          <!--                           class="msg-pagination-container hidden-md-and-down" :background="true"-->
          <!--                           :total="internalTransactionHistoryPageParams.total"-->
          <!--                           :current-page.sync="internalTransactionHistoryPageParams.page"-->
          <!--                           :page-count="internalTransactionHistoryPageParams.totalPage"-->
          <!--                           :page-size="internalTransactionHistoryPageParams.pageSize" :page-sizes="[5,10,30,50]"-->
          <!--                           @size-change="changeInternalTransactionHistoryPage"-->
          <!--                           @current-change="getInternalTransactionHistoryList"-->
          <!--                           layout="total, sizes, prev, pager, next, jumper">-->
          <!--            </el-pagination>-->
          <!--            <MobilePagination v-if="!internalTransactionHistoryLoading" key="2"-->
          <!--                              @current-change="getInternalTransactionHistoryList"-->
          <!--                              :current-page.sync="internalTransactionHistoryPageParams.page"-->
          <!--                              :total-amount="internalTransactionHistoryPageParams.total"-->
          <!--                              class="hidden-lg-and-up">-->
          <!--            </MobilePagination>-->
          <!--          </div>-->
        </b-col>
      </b-row>

      <!--   存档   -->
      <!--      <div v-if="tabActive === 5">-->
      <!--        <div style="display: flex; flex-wrap: wrap; align-items: center;gap: 10px;margin-top: 10px;">-->
      <!--          <el-select-->
      <!--            class="archive-select"-->
      <!--            v-model="archiveAccount"-->
      <!--            clearable-->
      <!--            :placeholder="$t('transaction.Archive.account')"-->
      <!--            size="small"-->
      <!--            @change="archiveAccountChange"-->
      <!--          >-->
      <!--            <el-option-->
      <!--              v-for="item in archiveAccountList"-->
      <!--              :key="item.id"-->
      <!--              :label="item.Label"-->
      <!--              :value="item.id"-->
      <!--            >-->
      <!--            </el-option>-->
      <!--          </el-select>-->
      <!--          &lt;!&ndash;          <div>Enter a date range:</div>&ndash;&gt;-->
      <!--          <el-date-picker-->
      <!--            class="archive-date-picker"-->
      <!--            size="small"-->
      <!--            v-model="archiveTime"-->
      <!--            type="daterange"-->
      <!--            value-format="yyyy-MM-DD"-->
      <!--            :start-placeholder="$t('transaction.Archive.startDate')"-->
      <!--            :end-placeholder="$t('transaction.Archive.endDate')"-->
      <!--          >-->
      <!--          </el-date-picker>-->
      <!--          <el-button style="background: #495EEB;border: 0" type="primary" size="small"-->
      <!--                     @click="getTradeHistoryMonthList">{{ $t('transaction.Archive.search') }}-->
      <!--          </el-button>-->
      <!--        </div>-->
      <!--        <el-table-->
      <!--          v-loading="archiveLoading"-->
      <!--          border-->
      <!--          :data="archiveTable"-->
      <!--          style="margin-top: 10px; width: 100%"-->
      <!--        >-->
      <!--          <el-table-column-->
      <!--            prop="date"-->
      <!--            :label="$t('transaction.Archive.date')"-->
      <!--            align="center"-->
      <!--          >-->
      <!--          </el-table-column>-->
      <!--          <el-table-column-->
      <!--            :label="$t('transaction.Archive.handle')"-->
      <!--            width="130px"-->
      <!--            align="center"-->
      <!--          >-->
      <!--            <template slot-scope="{row}">-->
      <!--              <el-button style="background: #495EEB;border: 0" type="primary" size="mini"-->
      <!--                         @click="downloadTradeHistoryCsv(row)">-->
      <!--                {{ $t('transaction.Archive.download') }}-->
      <!--              </el-button>-->
      <!--            </template>-->
      <!--          </el-table-column>-->
      <!--        </el-table>-->
      <!--      </div>-->
    </div>

    <b-container fluid v-if="false">
      <b-row style="position:relative;z-index: 2">
        <b-col cols="18" md="12">
          <iq-card style="padding: 0">
            <template v-slot:headerTitle>
              <div class="hidden-md-and-down">
                <b-col lg="3" style=" float: left; ">
                  <button :class="depositIndex  ? 'depositOne' : 'deposit' "
                          @click="depositClick">{{ $t('transaction.button_test1') }}
                  </button>
                </b-col>
                <b-col lg="4" style=" float: left; ">
                  <button :class="withdrawalIndex ?  'withdrawalOne' : 'withdrawal' "
                          @click="withdrawalClick">{{ $t('transaction.button_test2') }}
                  </button>
                </b-col>
                <b-col lg="5" style="float: left; ">
                  <button :class="transferIndex ? 'transferOne' : 'transfer'"
                          @click="transferClick">{{ $t('transaction.button_test3') }}
                  </button>
                </b-col>
              </div>
              <wd-radio-group v-model="value" style="text-align: center;" class="hidden-lg-and-up">
                <wd-radio value="deposits" shape="button" style="margin-top: 6px;">
                  <div @click="depositClick">{{ $t('transaction.button_test1') }}</div>
                </wd-radio>
                <wd-radio value="withdrawal" shape="button" style="margin-top: 6px;">
                  <div @click="withdrawalClick">{{ $t('transaction.button_test2') }}</div>
                </wd-radio>
                <wd-radio value="transfer" shape="button" style="margin-top: 6px;">
                  <div @click="transferClick">{{ $t('transaction.button_test3') }}</div>
                </wd-radio>
              </wd-radio-group>
            </template>

            <template v-slot:body>
              <div v-if="depositActivet">
                <deposit ref="deposit"></deposit>
              </div>
              <div v-if="transferActivet">
                <internal-fund-transfer ref="internalTransfer"></internal-fund-transfer>
              </div>
              <div v-if="withdrawaActivet">
                <withdrawal ref="withdrawal"></withdrawal>
              </div>
            </template>

          </iq-card>
        </b-col>

        <b-col>
          <iq-card>
            <template v-slot:headerTitle>
              <h4 class="card-title" v-if="depositActivet">{{ $t('transaction.deposit.title') }}</h4>
              <h4 class="card-title" v-if="withdrawaActivet">{{ $t('transaction.withdrawal.title') }}</h4>
              <h4 class="card-title" v-if="transferActivet">{{ $t('transaction.transaction.title') }}</h4>
            </template>

            <template v-slot:body>
              <div>
                <el-empty
                  v-if="depositWithdrawalHistoryPageParams.pageData.length === 0 && !depositWithdrawalHistoryLoading "></el-empty>
                <div v-else>
                  <div v-loading="depositWithdrawalHistoryLoading || submitLoading" v-if="!transferActivet">
                    <div style="overflow-y: auto;">
                      <b-table style="position:relative;z-index: 2"
                               class="hidden-md-and-down"
                               :items="depositWithdrawalHistoryPageParams.pageData"
                               :fields="!withdrawaActivet? depositWithdrawalHistoryColumns : withdrawalHistoryColumns">
                        <template v-slot:cell(status)="data">
                          <div v-if="data.item.status.toLowerCase() === 'deleted'">
                            CANCELLED
                          </div>
                          <div v-else-if="data.item.status.toLowerCase() === 'processed'">
                            COMPLETED
                          </div>
                          <div v-else-if="data.item.status.toLowerCase() === 'not paid'">
                            PENDING
                          </div>
                          <div v-else>
                            {{ data.item.status }}
                          </div>
                          <div>
                            <b-button class="btn-sm btn-common-shadow" variant="outline-danger"
                                      v-if="data.item.preWithdrawSuccess === 0 && data.item.status.toUpperCase() === 'PROCESSING'"
                                      @click="cancelWithdrawalDepositRequest(data)">{{ $t('sidebar.cancel') }}
                              <!--                            <i style="padding: 0;margin-left: 5px" class="el-icon-loading"></i>-->
                            </b-button>
                          </div>
                        </template>
                      </b-table>
                      <MobileTable key="1" :show-first="2"
                                   class="hidden-lg-and-up"
                                   :fields="!withdrawaActivet? depositWithdrawalHistoryColumns : withdrawalHistoryColumns"
                                   :items="depositWithdrawalHistoryPageParams.pageData"></MobileTable>
                    </div>
                    <el-pagination style="margin-top: 10px" popper-class='select_bottom'
                                   class="msg-pagination-container hidden-md-and-down" :background="true"
                                   layout="total, sizes, prev, pager, next, jumper"
                                   :total="depositWithdrawalHistoryPageParams.total"
                                   :current-page.sync="depositWithdrawalHistoryPageParams.page"
                                   :page-count="depositWithdrawalHistoryPageParams.totalPage"
                                   :page-size="depositWithdrawalHistoryPageParams.pageSize" :page-sizes="[5,10,30,50]"
                                   @size-change="changeDepositWithdrawalHistoryPage"
                                   @current-change="getDepositWithdrawalHistoryList">
                    </el-pagination>
                    <MobilePagination v-if="!depositWithdrawalHistoryLoading" key="1"
                                      @current-change="getDepositWithdrawalHistoryList"
                                      :current-page.sync="depositWithdrawalHistoryPageParams.page"
                                      :total-amount="depositWithdrawalHistoryPageParams.total" class="hidden-lg-and-up">
                    </MobilePagination>
                  </div>

                  <div v-loading="internalTransactionHistoryLoading" v-if="transferActivet">
                    <div style="overflow-y: auto;">
                      <b-table class="hidden-md-and-down"
                               :items="internalTransactionHistoryPageParams.pageData"
                               :fields="internalTransactionHistoryColumns">
                      </b-table>
                      <MobileTable key="2" :show-first="2"
                                   class="hidden-lg-and-up" :fields="internalTransactionHistoryColumns"
                                   :items="internalTransactionHistoryPageParams.pageData"></MobileTable>
                    </div>
                    <el-pagination style="margin-top: 10px" popper-class='select_bottom'
                                   class="msg-pagination-container hidden-md-and-down" :background="true"
                                   :total="internalTransactionHistoryPageParams.total"
                                   :current-page.sync="internalTransactionHistoryPageParams.page"
                                   :page-count="internalTransactionHistoryPageParams.totalPage"
                                   :page-size="internalTransactionHistoryPageParams.pageSize" :page-sizes="[5,10,30,50]"
                                   @size-change="changeInternalTransactionHistoryPage"
                                   @current-change="getInternalTransactionHistoryList"
                                   layout="total, sizes, prev, pager, next, jumper">
                    </el-pagination>
                    <MobilePagination v-if="!internalTransactionHistoryLoading" key="2"
                                      @current-change="getInternalTransactionHistoryList"
                                      :current-page.sync="internalTransactionHistoryPageParams.page"
                                      :total-amount="internalTransactionHistoryPageParams.total"
                                      class="hidden-lg-and-up">
                    </MobilePagination>
                  </div>
                </div>
              </div>

            </template>
          </iq-card>
        </b-col>
      </b-row>
    </b-container>
    <div style="height: 30px;"></div>
  </div>

</template>

<script>
import Deposit from './Deposit.vue'
import InternalFundTransfer from './InternalFundTransfer.vue'
import Withdrawal from './Withdrawal.vue'
import { getLocal } from '@/Utils/authLocalStorage'
import { Message } from 'element-ui'
import MobilePagination from '@/components/MyfxComponent/MobileComponent/MobilePagination'
import MobileTable from '@/components/MyfxComponent/MobileComponent/MobileTable.vue'

import Whitdrawals from './TransactionComponents/Withdrawal.vue'
import HintContent from './TransactionComponents/HintContent.vue'

const {
  TransactionHistory,
  Account
} = require('../../services/allExport.js')
export default {

  name: 'Transaction',
  components: {
    MobileTable,
    Deposit,
    InternalFundTransfer,
    MobilePagination,
    Withdrawal,
    // 2024/04/16
    Whitdrawals,
    HintContent
  },
  data () {
    return {

      japanEnterName: '',
      japanDepositEnterName: '',

      // 存档
      archiveAccount: null,
      currentArchiveAccount: null,
      archiveAccountList: [],
      archiveTime: [],
      archiveLoading: false,
      archiveTable: [],

      tabActive: 1,
      tabOption: [
        {
          id: 1,
          name: 'transaction.button_test1',
          icon: require('@/assets/images/account-icon-1.png'),
          iconSelect: require('@/assets/images/account-icon-1s.png'),
          type: 'Deposits'
        },
        {
          id: 2,
          name: 'transaction.button_test2',
          icon: require('@/assets/images/account-icon-2.png'),
          iconSelect: require('@/assets/images/account-icon-2s.png'),
          type: 'Withdrawal'
        },
        {
          id: 3,
          name: 'transaction.button_test3',
          icon: require('@/assets/images/account-icon-3.png'),
          iconSelect: require('@/assets/images/account-icon-3s.png'),
          type: 'Internal Fund Transfer'
        },
        {
          id: 4,
          name: 'transaction.button_test4',
          icon: require('@/assets/images/account-icon-4.png'),
          iconSelect: require('@/assets/images/account-icon-4s.png'),
          type: 'Transaction History'
        }
        // {
        //   id: 5,
        //   name: 'transaction.button_test5',
        //   icon: '',
        //   iconSelect: ''
        // }
      ],

      tableActive: 1,
      tableOption: [
        { id: 1, name: 'transaction.button_test1', en: 'Deposits' },
        { id: 2, name: 'transaction.button_test2', en: 'Withdrawal' },
        { id: 3, name: 'transaction.button_test3', en: 'Internal Fund Transfer' }
      ],
      tableMap: {
        1: 'transaction.deposit.title',
        2: 'transaction.withdrawal.title',
        3: 'transaction.transaction.title'
      },

      value: 'deposits',
      withdrawalIndex: true,
      depositIndex: true,
      transferIndex: true,
      depositActivet: false,
      transferActivet: false,
      withdrawaActivet: false,
      internalTransactionHistoryTemp: [],
      internalTransactionHistoryLoading: false,
      // internalTransactionHistoryColumns: [{
      //     label: 'PROCESSED DATE/TIME',
      //     key: 'createTime',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Source Account',
      //     key: 'srcLogin',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Target Account',
      //     key: 'targLogin',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Amount',
      //     key: 'amount',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Status',
      //     key: 'status',
      //     class: 'text-left'
      //   }
      // ],
      internalTransactionHistoryPageParams: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPage: 0,
        pageData: []
      },

      depositWithdrawalHistoryTemp: [],
      depositWithdrawalHistoryLoading: false,
      // depositWithdrawalHistoryColumns: [{
      //     label: 'Processed Date/Time',
      //     key: 'processedTime',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Transaction Type',
      //     key: 'direction',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Amount',
      //     key: 'amount',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Currency',
      //     key: 'currency',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Account Number',
      //     key: 'mt4Login',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Payment Type',
      //     key: 'transactionType',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Status',
      //     key: 'status',
      //     class: 'text-left'
      //   }
      // ],
      // withdrawalHistoryColumns: [{
      //     label: 'Processed Date/Time',
      //     key: 'processedTime',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Transaction Type',
      //     key: 'direction',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Amount',
      //     key: 'amount',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Currency',
      //     key: 'currency',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Account Number',
      //     key: 'mt4Login',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Withdrawal Type',
      //     key: 'transactionType',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Status',
      //     key: 'status',
      //     class: 'text-left'
      //   }
      // ],

      offset: 1,
      limit: 10,
      total: 0,
      depositType: ['Bank Wire', 'Credit Card', 'Crypto', 'Crypto Credit Card Deposit', 'JPY Bank Wire', 'bitwakket'],
      selectMap: {
        depositType: ''
      },
      tableOrigin: [],
      tableData: [],
      tableDataSlice: [],
      tableKeyword: '',
      tableLoading: false,

      depositWithdrawalHistoryPageParams: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPage: 0,
        pageData: []
      },
      fundingWayType: 'Deposit',
      transactionType: '',
      transactionTypeSelection: [
        '', 'Bank Wire', 'CNY Bank Wire',
        'Credit Card', 'Crypto', 'JPY Bank Wire', 'Multi Level Rebate Withdraw',
        'UnionPay', 'bitwallet'
      ],
      submitLoading: false,
      accountLoading: false
    }
  },
  computed: {
    content () {
      if (this.$i18n.locale === 'ja_JP') {
        return [
          this.$t('transaction.deposit.tips.test1'),
          this.$t('transaction.deposit.tips.test2'),
          this.$t('transaction.deposit.tips.test3'),
          this.$t('transaction.deposit.tips.test7')
        ]
      } else {
        return [
          this.$t('transaction.deposit.tips.test1'),
          this.$t('transaction.deposit.tips.test2'),
          this.$t('transaction.deposit.tips.test3')
        ]
      }
    },
    internalTransactionHistoryColumns () {
      return [
        {
          label: this.$t('transaction.transaction.table.processed_date_time'),
          key: 'createTime',
          class: 'text-left',
          index: 0,
          sort: true
        },
        {
          label: this.$t('transaction.transaction.table.source_account'),
          key: 'srcLogin',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.transaction.table.target_account'),
          key: 'targLogin',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.transaction.table.amount'),
          key: 'amount',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.transaction.table.status'),
          key: 'status',
          class: 'text-left',
          sort: true
        }
      ]
    },
    depositWithdrawalHistoryColumns () {
      return [
        {
          label: this.$t('transaction.deposit.table.processed_date_time'),
          key: 'processedTime',
          class: 'text-left',
          index: 0,
          sort: true,
          select: false,
          width: 140
        },
        {
          label: this.$t('transaction.deposit.table.transaction_type'),
          key: 'direction',
          class: 'text-left',
          sort: true,
          select: false
        },
        {
          label: this.$t('transaction.deposit.table.amount'),
          key: 'amount',
          class: 'text-left',
          sort: true,
          select: false
        },
        {
          label: this.$t('transaction.deposit.table.currency'),
          key: 'currency',
          class: 'text-left',
          sort: true,
          select: false
        },
        {
          label: this.$t('transaction.deposit.table.account_number'),
          key: 'mt4Login',
          class: 'text-left',
          sort: true,
          select: false
        },
        {
          label: this.$t('transaction.deposit.table.payment_type'),
          key: 'transactionType',
          class: 'text-left',
          sort: true,
          select: true
        },
        {
          label: this.$t('transaction.deposit.table.status'),
          key: 'status',
          class: 'text-left',
          sort: true,
          select: false
        }
      ]
    },
    withdrawalHistoryColumns () {
      return [
        {
          label: this.$t('transaction.deposit.table.processed_date_time'),
          key: 'processedTime',
          class: 'text-left',
          index: 0,
          sort: true
        },
        {
          label: this.$t('transaction.withdrawal.table.transaction_type'),
          key: 'direction',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.withdrawal.table.amount'),
          key: 'amount',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.withdrawal.table.currency'),
          key: 'currency',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.withdrawal.table.account_number'),
          key: 'mt4Login',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.withdrawal.table.withdrawal_type'),
          key: 'transactionType',
          class: 'text-left',
          sort: true
        },
        {
          label: this.$t('transaction.withdrawal.table.status'),
          key: 'status',
          class: 'text-left',
          sort: true
        }
      ]
    }
  },
  watch: {
    '$i18n.locale' (n) {
      //   更新table国际化
      this.tableOrigin.forEach(v => {
        if (v.transactionType) {
          v.transactionTypeVal = this.$t(v.transactionTypeKey)
        }
        if (v.status) {
          v.statusVal = this.$t(v.statusKey)
        }
      })
    }
    // 'value' (newVal, oldVal) {
    //   if (newVal == 'deposits') {
    //     this.depositClick()
    //   } else if (newVal == 'withdrawal') {
    //     this.withdrawalClick()
    //   } else if (newVal == 'transfer') {
    //     this.transferClick()
    //   }
    // }
  },
  async created () {
    // this.getArchivedMt4TradingAccount()
    this.tabActive = Number(this.$route.params.name) || 1

    await this.getTradingAccounts()

    this.$vp.add({ message: '[Account Transactions] Click Deposits' })
  },
  methods: {

    /* --- 归档 start --- */

    // async getArchivedMt4TradingAccount () {
    //   try {
    //     const myguid = JSON.parse(getLocal('user')).myguid
    //     const { code, data } = await TransactionHistory.getArchivedMt4TradingAccount({ myguid })
    //     if (code == 200) {
    //       this.archiveAccountList = data.map(item => {
    //         item.Label = `${item.accountType} - ${item.mt4accountNumber}`
    //         return item
    //       })
    //     }
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    //
    // archiveAccountChange () {
    //   this.currentArchiveAccount = this.archiveAccountList.find(v => v.id === this.archiveAccount)
    // },
    //
    // async getTradeHistoryMonthList () {
    //   try {
    //     if (!this.archiveAccount) return Message.warning(this.$t('transaction.Archive.hint_account'))
    //     if (!this.archiveTime || !this.archiveTime.length) return Message.warning(this.$t('transaction.Archive.hint_date'))
    //     this.archiveLoading = true
    //     const { code, data } = await TransactionHistory.getTradeHistoryMonthList({
    //       createTime: this.currentArchiveAccount?.createTime,
    //       startTime: this.archiveTime?.[0],
    //       endTime: this.archiveTime?.[1],
    //       mt4Myguid: this.currentArchiveAccount?.myguid
    //     })
    //     if (code == 200) {
    //       this.archiveTable = data.map(v => ({ date: v }))
    //     }
    //     this.archiveLoading = false
    //   } catch (err) {
    //     this.archiveLoading = false
    //     console.log(err)
    //   }
    // },
    //
    // async downloadTradeHistoryCsv (row) {
    //   try {
    //     if (!this.archiveAccount) return Message.warning(this.$t('transaction.Archive.hint_account'))
    //     if (!this.archiveTime || !this.archiveTime.length) return Message.warning(this.$t('transaction.Archive.hint_date'))
    //     const { data, headers } = await TransactionHistory.downloadTradeHistoryCsv({
    //       date: row.date,
    //       mt4Myguid: this.currentArchiveAccount?.myguid
    //     })
    //     if (data.code == 200) {
    //       Message.warning(this.$t('transaction.Archive.hint_no_data'))
    //     } else {
    //       const [, name] = headers['content-disposition'].match(/filename=(.*)/)
    //       const url = URL.createObjectURL(new Blob([data], { type: 'text/csv' }))
    //       let a = document.createElement('a')
    //       a.href = url
    //       a.download = name
    //       document.body.appendChild(a)
    //       a.click()
    //       document.body.removeChild(a)
    //       a = null
    //       URL.revokeObjectURL(url)
    //     }
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },

    /* --- 归档 end --- */

    /**
     * @description tab选项切换
     * <AUTHOR>
     */
    tabChange (id) {
      if (this.tabActive === id) return
      this.tabActive = id

      this.japanDepositEnterName = ''
    },

    /**
     * @description table选项切换
     * <AUTHOR>
     */
    tableChange (id) {
      if (this.tableActive === id) return
      this.tableActive = id

      this.tableOrigin = []
      this.tableData = []
      this.tableDataSlice = []

      const mapFunc = {
        1: this.depositClick,
        2: this.withdrawalClick,
        3: this.transferClick
      }
      mapFunc[id]()
    },

    /**
     * history 切换
     * @param command
     */
    handleCommand (command) {
      this.offset = 1
      // this.limit = 10
      this.tableChange(command)
    },

    japanEnter (ev) {
      this.japanEnterName = ev || ''
    },

    selectEnter (ev) {
      this.japanDepositEnterName = ev || ''
    },

    /**
     * @description 入金日文tip
     * <AUTHOR>
     */
    DepositJPTIP () {
      const MAP = {
        国際銀行送金: [
          this.$t('transaction.deposit.bank_wire.test1'),
          this.$t('transaction.deposit.bank_wire.test2'),
          this.$t('transaction.deposit.bank_wire.test3'),
          this.$t('transaction.deposit.bank_wire.test4')
        ],
        仮想通貨: [
          this.$t('transaction.deposit.tips.test9'),
          this.$t('transaction.deposit.tips.test10')
        ],
        Bitwallet: [
          this.$t('transaction.deposit.tips.test11'),
          this.$t('transaction.deposit.tips.test_12_01'),
          this.$t('transaction.deposit.tips.test_12_02'),
          this.$t('transaction.deposit.tips.test_12_03')
        ],
        Bitwallet_2: [
          this.$t('transaction.deposit.tips.test13')
        ],
        日本円国内銀行送金: [
          this.$t('transaction.deposit.tips.test14'),
          this.$t('transaction.deposit.tips.test15'),
          // this.$t('transaction.deposit.tips.test16'),
          this.$t('transaction.deposit.tips.test17'),
          this.$t('transaction.deposit.tips.test18')
        ]
      }
      if (MAP[this.japanDepositEnterName]) {
        return MAP[this.japanDepositEnterName]
      } else {
        if (this.japanDepositEnterName === 'International Bank Wire') {
          return [
            this.$t('transaction.deposit.tips.test19'),
            this.$t('transaction.deposit.tips.test20'),
            this.$t('transaction.deposit.tips.test21'),
            this.$t('transaction.deposit.tips.test22')
          ]
        } else {
          const arr = [
            this.$t('transaction.deposit.tips.test1'),
            this.$t('transaction.deposit.tips.test2'),
            this.$t('transaction.deposit.tips.test3'),
            this.$t('transaction.deposit.tips.test4'),
            {
              title: this.$t('transaction.deposit.tips.test5'),
              html: true
            },
            // this.$t('transaction.withdrawal.tips.test7'),
            // this.$t('transaction.withdrawal.tips.test8'),
            // this.$t('transaction.withdrawal.tips.test9'),
            // this.$t('transaction.withdrawal.tips.test10'),
            {
              title: this.$t('transaction.withdrawal.tips.test11'),
              url: 'https://help.myfxmarkets.com/hc/ja/categories/18892954866585-%E5%85%A5%E9%87%91-%E5%87%BA%E9%87%91'
            },
            {
              title: this.$t('transaction.withdrawal.tips.test12'),
              url: 'https://help.myfxmarkets.com/hc/ja/articles/**************-MAM-PAMM%E5%8F%A3%E5%BA%A7%E3%81%AE%E5%87%BA%E9%87%91%E7%94%B3%E8%AB%8B%E6%89%8B%E9%A0%86'
            }
          ]
          // 删除英文和中文链接
          if (this.$i18n.locale === 'en_US' || this.$i18n.locale === 'zh_CN') {
            arr.splice(4, arr.length)
            return arr
          } else {
            return arr
          }
        }
      }
    },

    /**
     * @description 日文情况tip内容
     * <AUTHOR>
     */
    JPTip () {
      const map = {
        日本円国内銀行送金: [
          {
            title: this.$t('transaction.withdrawal.tips.test13_01'),
            html: true
          },
          this.$t('transaction.withdrawal.tips.test13_02'),
          this.$t('transaction.withdrawal.tips.test13_03'),
          this.$t('transaction.withdrawal.tips.test13_04'),
          this.$t('transaction.withdrawal.tips.test13_05')
        ],
        国際銀行送金: [
          {
            title: this.$t('transaction.withdrawal.tips.test18'),
            html: true
          },
          this.$t('transaction.withdrawal.tips.test19'),
          this.$t('transaction.withdrawal.tips.test20'),
          this.$t('transaction.withdrawal.tips.test21'),
          this.$t('transaction.withdrawal.tips.test22')
        ],
        仮想通貨: [
          {
            title: this.$t('transaction.withdrawal.tips.test18'),
            html: true
          },
          this.$t('transaction.withdrawal.tips.test25'),
          this.$t('transaction.withdrawal.tips.test26')
        ],
        クレジットカード: [
          {
            title: this.$t('transaction.withdrawal.tips.test18'),
            html: true
          },
          this.$t('transaction.withdrawal.tips.test28'),
          this.$t('transaction.withdrawal.tips.test29'),
          this.$t('transaction.withdrawal.tips.test30'),
          // this.$t('transaction.withdrawal.tips.test30_01'),
          this.$t('transaction.withdrawal.tips.test23')
        ],
        BitwalletJP: [
          {
            title: this.$t('transaction.withdrawal.tips.test18'),
            html: true
          },
          this.$t('transaction.withdrawal.tips.test32')
        ],
        Bitwallet: [
          {
            title: this.$t('transaction.withdrawal.tips.test33'),
            html: true
          },
          this.$t('transaction.withdrawal.tips.test34')
        ]
      }
      if (map[this.japanEnterName]) {
        return map[this.japanEnterName]
      } else {
        const arr = [
          this.$t('transaction.withdrawal.tips.test1'),
          this.$t('transaction.withdrawal.tips.test2'),
          this.$t('transaction.withdrawal.tips.test3'),
          this.$t('transaction.withdrawal.tips.test4'),
          this.$t('transaction.withdrawal.tips.test5'),
          this.$t('transaction.withdrawal.tips.test6'),
          // this.$t('transaction.withdrawal.tips.test10'),
          {
            title: this.$t('transaction.deposit.tips.test5'),
            html: true
          },
          // this.$t('transaction.withdrawal.tips.test7'),
          // this.$t('transaction.withdrawal.tips.test8'),
          // this.$t('transaction.withdrawal.tips.test9'),
          {
            title: this.$t('transaction.withdrawal.tips.test11'),
            url: 'https://help.myfxmarkets.com/hc/ja/categories/18892954866585-%E5%85%A5%E9%87%91-%E5%87%BA%E9%87%91'
          },
          {
            title: this.$t('transaction.withdrawal.tips.test12'),
            url: 'https://help.myfxmarkets.com/hc/ja/articles/**************-MAM-PAMM%E5%8F%A3%E5%BA%A7%E3%81%AE%E5%87%BA%E9%87%91%E7%94%B3%E8%AB%8B%E6%89%8B%E9%A0%86'
          }
        ]
        if (this.$i18n.locale === 'en_US' || this.$i18n.locale === 'zh_CN') {
          arr.splice(6, arr.length)
          return arr
        } else {
          return arr
        }
      }
    },

    // 全局存储TradingAccount
    async getTradingAccounts () {
      this.accountLoading = true
      const email = JSON.parse(getLocal('user')).email
      const myGuid = JSON.parse(getLocal('user')).myguid
      await Account.fetchTransactionsTradingAccount(myGuid, email).then(res => {
        const tradingAccountList = res.data
        const tradingAccount = []
        tradingAccountList.forEach(item => {
          if (Number(item.status) >= 0) {
            tradingAccount.push(item)
          }
        })
        this.$store.commit('commonSet', { // 通用方法， 多个key就继续往后写,打比方
          keys: ['tradingAccountList'], //  ['test', 'test']   ['testArr', 0] 这样都行
          data: tradingAccount
        })
        // console.log("---------"+this.$store.getters.commonGet(['accountNameLoginList'])) 可以这样获取
      })
      await TransactionHistory.getEWalletAmount(myGuid).then(res => {
        const sumAccountList = res.data
        this.$store.commit('commonSet', { // 通用方法， 多个key就继续往后写,打比方
          keys: ['sumAccountList'], //  ['test', 'test']   ['testArr', 0] 这样都行
          data: sumAccountList
        })
      })
      this.accountLoading = false
      if (this.$route.params.name === 'withdrawal') {
        this.withdrawalClick()
      } else if (this.$route.params.name === 'transfer') {
        this.transferClick()
      } else {
        this.depositClick()
      }

      if (this.tabActive == 1) return this.$refs.deposit?.getTradingAccount()
      if (this.tabActive == 2) return this.$refs.withdrawal?.getTradingAccount()
      if (this.tabActive == 3) return this.$refs.internalTransfer?.getAccountList()
    },
    depositClick () {
      this.fundingWayType = 'Deposit'
      this.depositIndex = true
      this.depositActivet = true
      this.transferIndex = false
      this.withdrawalIndex = false
      this.transferActivet = false
      this.withdrawaActivet = false
      this.value = 'deposits'
      if (this.$refs.deposit) {
        this.$refs.deposit.active = 0
        this.$refs.deposit.selectedDepositType = {}
      }
      this.depositWithdrawalHistoryPageParams.totalPage = 0
      this.depositWithdrawalHistoryPageParams.total = 0
      this.depositWithdrawalHistoryPageParams.pageData = []
      this.getDepositWithdrawalHistoryList()
    },
    withdrawalClick () {
      this.fundingWayType = 'Withdraw'
      this.depositIndex = false
      this.depositActivet = false
      this.transferActivet = false
      this.transferIndex = false
      this.withdrawaActivet = true
      this.withdrawalIndex = true
      this.value = 'withdrawal'
      if (this.$refs.withdrawal) {
        this.$refs.withdrawal.clearActionStep()
      }
      this.depositWithdrawalHistoryPageParams.totalPage = 0
      this.depositWithdrawalHistoryPageParams.total = 0
      this.depositWithdrawalHistoryPageParams.pageData = []
      this.getDepositWithdrawalHistoryList()
    },
    transferClick () {
      this.depositIndex = false
      this.depositActivet = false
      this.transferIndex = true
      this.transferActivet = true
      this.withdrawaActivet = false
      this.withdrawalIndex = false
      this.value = 'transfer'
      this.internalTransactionHistoryPageParams.totalPage = 0
      this.internalTransactionHistoryPageParams.total = 0
      this.internalTransactionHistoryPageParams.pageData = []
      this.getInternalTransactionHistoryList()
    },

    // 表格关键字搜索
    tableKeywordSearch () {
      this.offset = 1
      this.tableData = this.tableOrigin.filter(f => Object.values(f).map(m => m ? String(m) : '').filter(ff => ff.includes(this.tableKeyword)).length)
      this.getTableData()
    },

    // 表格分页
    sizeChange (size) {
      this.limit = size
      this.getTableData()
    },
    currentChange (current) {
      this.offset = current
      this.getTableData()
    },

    // 表格列
    getTableColumns () {
      const MAP = {
        1: this.depositWithdrawalHistoryColumns,
        2: this.withdrawalHistoryColumns,
        3: this.internalTransactionHistoryColumns
      }
      return MAP[this.tableActive]
    },

    // 表格排序
    sortChange ({ column, prop, order }) {
      this.tableKeywordSearch()

      if (order) {
        this.tableData.sort((a, b) => {
          const k1 = a[prop] ? String(a[prop]) : ''
          const k2 = b[prop] ? String(b[prop]) : ''

          // 是否数字
          if (/^\d+$/.test(k1)) {
            return order === 'ascending' ? +k1 - +k2 : +k2 - +k1
          } else if (prop === 'amount') {
            const symbolA = k1.charAt(0)
            const symbolB = k2.charAt(0)
            if (symbolA !== symbolB) {
              if (order === 'ascending') {
                return symbolA < symbolB ? -1 : 1
              } else {
                return symbolA < symbolB ? 1 : -1
              }
            }
            const numA = +(k1.slice(k1.includes('A$') ? 2 : 1))
            const numB = +(k2.slice(k1.includes('A$') ? 2 : 1))
            if (order === 'ascending') {
              return numA - numB
            } else {
              return numB - numA
            }
          } else {
            if (order === 'ascending') {
              return k1.localeCompare(k2)
            } else {
              return k2.localeCompare(k1)
            }
          }
        })
      }

      this.getTableData()
    },

    // 获取表格数据
    getTableData () {
      const sliceStart = (this.offset - 1) * this.limit
      const sliceEnd = sliceStart + this.limit
      this.tableDataSlice = this.tableData.slice(sliceStart, sliceEnd)
    },

    // 内转
    getInternalTransactionHistoryList () {
      this.$vp.add({ message: `[Transaction] Query internal transaction history table data, page ${ this.internalTransactionHistoryPageParams.page }, size ${ this.internalTransactionHistoryPageParams.pageSize }` })
      this.tableLoading = true

      this.internalTransactionHistoryLoading = true
      const myguid = JSON.parse(getLocal('user')).myguid
      TransactionHistory.getInternalTransactionHistory({
        currentPage: this.internalTransactionHistoryPageParams.page,
        pageSize: this.internalTransactionHistoryPageParams.pageSize,
        myguid
      }).then(res => {
        // .pageData
        // .totalPages
        // .total
        const pageData = res.data
        this.internalTransactionHistoryPageParams.totalPage = 1
        this.internalTransactionHistoryPageParams.total = res.data.length
        // console.log(this.internalTransactionHistoryPageParams)
        pageData.forEach(item => {
          if (item.srcServer === '') {
            item.srcLogin = '[MYFX Wallet]'
          } else if (item.srcServer === 'pro_mt5' || item.srcServer === 'st_mt5') {
            item.srcLogin = '[MT5]' + ' ' + item.srcLogin
          } else {
            item.srcLogin = '[' + item.srcServer + ']' + ' ' + item.srcLogin
          }

          if (item.targServer === '') {
            item.targLogin = '[MYFX Wallet]'
          } else if (item.targServer === 'pro_mt5' || item.targServer === 'st_mt5') {
            item.targLogin = '[MT5]' + ' ' + item.targLogin
          } else {
            item.targLogin = '[' + item.targServer + ']' + ' ' + item.targLogin
          }

          // item.amount = item.amount + ' ' + item.theCurrency
        })
        this.internalTransactionHistoryPageParams.pageData = pageData

        this.tableOrigin = pageData.map(item => {
          if (item.status) {
            const statusMap = {
              deleted: 'CANCELLED',
              'not paid': 'PENDING',
              processing: 'PROCESSING',
              mampamm: 'MAMPAMM',
              processed: 'PROCESSED',
              cancelled: 'CANCELLED',
              failed: 'FAILED'
            }
            if (statusMap[item.status.toLowerCase()]) {
              item.statusKey = `transaction.deposit.table.sta_type.${ statusMap[item.status.toLowerCase()] }`
              item.statusVal = this.$t(item.statusKey)
            } else {
              item.statusKey = item.statusVal = ''
            }
            const currency = { jpy: '¥', usd: '$', aud: 'A$', eur: '€', gbp: '£', usc: '¢' }
            const amountVal = `${ currency[item.theCurrency?.toLowerCase() || ''] } ${ Number(item.amount).toFixed(2) }`
            item.amount = amountVal
            item.amountValue = amountVal
          }
          return item
        })
        this.tableData = JSON.parse(JSON.stringify(this.tableOrigin))
        this.total = res.data.length
        this.getTableData()

        this.internalTransactionHistoryLoading = false
        this.tableLoading = false
      }).catch(error => {
        this.tableLoading = false
        this.internalTransactionHistoryLoading = false
      })
    },
    changeInternalTransactionHistoryPage (val) {
      this.internalTransactionHistoryPageParams.pageSize = val
      this.getInternalTransactionHistoryList()
    },

    // 出金 / 入金
    getDepositWithdrawalHistoryList () {
      this.$vp.add({ message: `[Transaction] Query ${ this.fundingWayType } history table data, page ${ this.depositWithdrawalHistoryPageParams.page }, size ${ this.depositWithdrawalHistoryPageParams.pageSize }` })
      this.tableLoading = true

      this.depositWithdrawalHistoryLoading = true
      const myguid = JSON.parse(getLocal('user')).myguid
      TransactionHistory.getDepositWithdrawalHistory({
        // currentPage: this.depositWithdrawalHistoryPageParams.page,
        // pageSize: this.depositWithdrawalHistoryPageParams.pageSize,
        myguid,
        fundingWayType: this.fundingWayType,
        transactionType: this.transactionType
      }).then(res => {
        // .pageData
        // .totalPages
        // .total
        this.depositWithdrawalHistoryPageParams.totalPage = 1
        this.depositWithdrawalHistoryPageParams.total = res.data.length
        this.depositWithdrawalHistoryPageParams.pageData = res.data.map(item => {
          if (item.transactionType) {
            item.transactionTypeKey = `transaction.transaction.deposits.paymentType.${ item.transactionType.replace(/ /g, '') }`
            item.transactionTypeVal = this.$t(item.transactionTypeKey)
          }
          if (item.status) {
            const statusMap = {
              deleted: 'CANCELLED',
              'not paid': 'PENDING',
              processing: 'PROCESSING',
              mampamm: 'MAMPAMM',
              processed: 'PROCESSED',
              cancelled: 'CANCELLED',
              failed: 'FAILED'
            }
            if (statusMap[item.status.toLowerCase()]) {
              item.statusKey = `transaction.deposit.table.sta_type.${ statusMap[item.status.toLowerCase()] }`
              item.statusVal = this.$t(item.statusKey)
            } else {
              item.statusKey = item.statusVal = ''
            }
          }
          if (item.mt4Login) {
            if (item.mt4Login == -1) {
              item.mt4Login = this.$t('transaction.deposit.table.account_type_text')
              item.mt4LoginKey = 'transaction.deposit.table.account_type_text'
            } else {
              item.mt4LoginKey = ''
            }
          }

          const currency = { jpy: '¥', usd: '$', aud: 'A$', eur: '€', gbp: '£', usc: '¢' }
          const amountVal = `${ currency[item.currency?.toLowerCase() || ''] } ${ Number(item.amount).toFixed(2) }`
          item.amount = amountVal
          item.amountValue = amountVal

          return item
        })

        this.tableOrigin = this.depositWithdrawalHistoryPageParams.pageData
        this.tableData = JSON.parse(JSON.stringify(this.tableOrigin))
        this.total = res.data.length
        this.getTableData()

        this.depositWithdrawalHistoryLoading = false

        this.tableLoading = false
      }).catch(error => {
        console.log(error)
        this.tableLoading = false

        this.depositWithdrawalHistoryLoading = false
      })
    },
    changeDepositWithdrawalHistoryPage (val) {
      this.depositWithdrawalHistoryPageParams.pageSize = val
      this.getDepositWithdrawalHistoryList()
    },
    cancelWithdrawalDepositRequest (data) {
      this.submitLoading = true
      const myguid = data.item.withdrawMyguid
      TransactionHistory.cancelWithdrawalRequest(myguid).then(res => {
        Message.success(res.msg)
        this.submitLoading = false
        this.getDepositWithdrawalHistoryList()
      }).catch(error => {
        this.submitLoading = false
        this.getDepositWithdrawalHistoryList()
      })
    },
    search () {
      this.depositWithdrawalHistoryPageParams.page = 1
      this.getDepositWithdrawalHistoryList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "Transaction.scss";

.transfer,
.withdrawal,
.deposit {
  border: 1px #DCDFE6 solid;
  background-color: #FFFFFF;
  margin-left: 10px;
  height: 50px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
}

.transfer {
  width: 180px;
}

.withdrawal {
  width: 110px;
}

.deposit {
  width: 80px;
}

.transfer:hover {
  font-weight: 1000;
  background-color: #ebedfb;
}

.withdrawal:hover {
  font-weight: 1000;
  background-color: #ebecfb;
}

.deposit:hover {
  font-weight: 1000;
  background-color: #ebeefb;
}

.depositOne {
  border: 1px #DCDFE6 solid;
  margin-left: 10px;
  height: 50px;
  width: 80px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
  font-weight: 1000;
  background-color: #ebecfb;
}

.withdrawalOne {
  border: 1px #DCDFE6 solid;
  margin-left: 10px;
  height: 50px;
  width: 110px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
  font-weight: 1000;
  background-color: #ebebfb;
}

.transferOne {
  border: 1px #DCDFE6 solid;
  margin-left: 10px;
  height: 50px;
  width: 180px;
  border-top-left-radius: 0.8em;
  border-top-right-radius: 0.8em;
  border-bottom-left-radius: 0.8em;
  border-bottom-right-radius: 0.8em;
  transition: all .3s;
  font-weight: 1000;
  background-color: #ebebfb;
}

/*.msg-pagination-container.is-background .el-pager li {
  !*对页数的样式进行修改*!
  background-color: rgba(0, 0, 0, 0.22);
  color: #FFF;
  border-color: #e2e3e9;
}

.msg-pagination-container.is-background .btn-next {
  !*对下一页的按钮样式进行修改*!
  //background-color:  #FFF;
  color: #FFF;
}

.msg-pagination-container.is-background .btn-prev {
  !*对上一页的按钮样式进行修改*!
  //background-color:  #FFF;
  color: #FFF;
}

.msg-pagination-container.is-background .el-pager li:not(.disabled).active {
  !*当前选中页数的样式进行修改*!
  background-color: #d2bcfc;
  color: #FFF;
}

.select_bottom {
  border-color: #d2bcfc;

  .el-select-dropdown__item.selected {
    color: #7937f4;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: #d2bcfc;
    color: #fff;
  }
}*/

.table-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  font-size: 18px;
  color: #fff;
  background-color: #495DEC;
  border-radius: 5px 5px 0 0;

  .el-input {
    width: 200px;
  }
}

.el-table {
  ::v-deep th .cell {
    color: #0d1350;
    //white-space: nowrap;
    word-break: break-word
  }
}

.el-pagination {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

@media screen and (max-width: 768px) {
  .archive-select,
  .archive-date-picker {
    width: 100% !important;
  }

  .tab-icon {
    display: none;
  }
  .tab {
    display: flex;
    justify-content: center;
    //justify-content: space-between;
    .tab-item {
      font-size: 12px;
      //margin-right: 0;
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: #FFFFFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
