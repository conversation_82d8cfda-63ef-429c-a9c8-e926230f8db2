<template>
  <b-container fluid class="deposit-box">

    <b-row>
      <b-col sm="12" md="12" lg="12" style="padding: 0">
        <div style="margin: 0;">
          <div style="display: flex; align-items: center">
            <h4 style="padding: 20px;" v-if="selectedDepositType.depositMethods">
              <span>{{ selectedDepositType.depositMethods }}</span>
            </h4>
            <div v-if="
                    (selectedDepositType.dataBaseName === 'pcins') &&
                    selectedDepositType.dataBaseName === 'pcins'"
                 class="creditText">
              {{ $t('transaction.deposit.credit_card.remakes') }}
            </div>
            <div
              v-if="selectedDepositType.dataBaseName === 'grandPay' &&
              (this.$i18n.locale === 'ja_JP' || this.$i18n.locale === 'en_US')"
              class="card_type">
              (VISA / Mastercard)
            </div>
          </div>

          <div
            v-if="active === 1 && selectedDepositType.dataBaseName === 'grandPay' &&
              (this.$i18n.locale === 'ja_JP' || this.$i18n.locale === 'en_US')"
            class="card_type1">
            (VISA / Mastercard)
          </div>

          <div v-if="active === 1 &&
                    $i18n.locale === 'ja_JP' &&
                    (selectedDepositType.depositMethods === 'Apple Pay / Google Pay' || selectedDepositType.depositMethods === 'Neteller / Skrill' ) &&
                    selectedDepositType.dataBaseName === 'xCoins'"
               style="color: #495eeb; margin-left: 15px;">
            <div>※ご利用方法は<a href="https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/%E5%85%A5%E9%87%91%E6%96%B9%E6%B3%95-JP-2024-10-04.pdf" target="_blank" style="text-decoration: underline">こちら</a>をご参照ください。</div>
            <div>ご入金いただいた資金は国内銀行送金または国際銀行送金での出金となります。</div>
<!--            <div>出金申請の際は、出金方法で「仮想通貨」を選択して、「USDT」で出金申請してください。</div>-->
          </div>
          <div v-if="active === 1 && $i18n.locale === 'en_US' &&
                  (selectedDepositType.depositMethods === 'Apple Pay / Google Pay' || selectedDepositType.depositMethods === 'Neteller / Skrill' ) &&
                    selectedDepositType.dataBaseName === 'xCoins'"
               style="color: #495eeb; margin-left: 15px;">
            <div>The deposited funds will be withdrawn via JPY Local Bank or International Bank Wire.</div>
<!--            <br> When submitting a withdrawal request, please select "Crypto Currency" as the withdrawal method and select "USDT" as the currency.-->
          </div>

          <div v-if="active === 1 && $i18n.locale === 'en_US' && selectedDepositType.depositMethods === 'Credit Card' &&
                    selectedDepositType.dataBaseName === 'xCoins'"
               style="color: #495eeb; margin-left: 15px;">
            <div>The deposited funds will be withdrawn via JPY Local Bank or International Bank Wire.</div>
<!--            <div>The deposited funds will be withdrawn via USDT.<br> When submitting a withdrawal request, please select "Crypto Currency" as the withdrawal method and select "USDT" as the currency.</div>-->
          </div>

          <div class="step">
            <transition-group name="deposit">
              <div :key="1">
                <transition-group name="steps">
                  <div key="step" v-if="selectedDepositType.dataBaseName !== 'none'">
                    <el-steps :key="1" style="margin-top: 30px;margin-bottom: 20px"
                              :active="active" finish-status="success"
                              align-center>
                      <el-step :description="$t('transaction.deposit.description1')"></el-step>
                      <el-step :description="$t('transaction.deposit.description2')"></el-step>
                      <el-step :description="$t('transaction.deposit.description3')"></el-step>
                    </el-steps>

<!--                    <div style="padding: 0 30px 10px; color: red; font-size: 12px" v-if="active == 0" v-html="$t('transaction.deposit.newYearTips')" />-->
                  </div>
                </transition-group>

                <transition-group name="form">
                  <div v-loading="depositWayLoading" :key="1" v-if="active===0"
                       style="position:relative;z-index: 2">
                    <b-table
                      style="margin: 0;"
                      class="hidden-md-and-down bTable"
                      :fields="depositWayColumns"
                      :items="depositWay"
                      @row-clicked="tableRowClick"
                    >
                      <template v-slot:cell(depositMethods)="data">
                        <b-row :style="{
                          filter: (data.item.maintenance || !data.item.usable) ? `grayscale(1)` : ''
                        }">
                          <b-col class="table-icon" md="3">
                            <i :class="setIconClass(data)"></i>
                          </b-col>
                          <b-col>
                            <span>{{ data.item.depositMethods }}</span>
                          </b-col>
                        </b-row>
                      </template>
                      <template v-slot:cell(status)="data">
                        <span v-if="!data.item.maintenance" style="color: green">{{ data.item.status }}</span>
                        <span v-else style="color: #a70606">{{ data.item.status }}</span>
                        <el-tooltip placement="bottom" effect="light">
                          <div style="width: 200px;" slot="content">
                            {{ creditCardToolTip }}
                          </div>

                          <i style="margin: 3px;color: #384bfa" class="far fa-question-circle"
                             v-if="data.item.dataBaseName === 'pcins' || data.item.dataBaseName === 'grandPay'"></i>

                        </el-tooltip>
                      </template>
                      <template v-slot:cell(fee)="data">
                        <span>{{ data.item.fee }}</span>
                        <el-tooltip placement="bottom" effect="light">
                          <div style="width: 200px;" slot="content">
                            {{ bitWalletTooltip }}
                          </div>

                          <i style="margin: 3px;color: #384bfa" class="far fa-question-circle"
                             v-if="data.item.depositMethods === 'Bitwallet' "></i>

                        </el-tooltip>

                      </template>
                      <!--                      <template v-slot:cell(operation)="data">-->
                      <!--                        <b-button-->
                      <!--                          :disabled="data.item.maintenance || !data.item.usable"-->
                      <!--                          class="table-button"-->
                      <!--                          variant="primary"-->
                      <!--                          style="white-space: nowrap"-->
                      <!--                          @click="selectDepositType(data)"-->
                      <!--                          v-viewpoint.click="{ message: `[Deposits] Click ${data.item.depositMethods}` }"-->
                      <!--                        >-->
                      <!--                          {{ $t('sidebar.select') }}-->
                      <!--                          <i v-if="btnLoading ? !data.item.usable : false"-->
                      <!--                             style="padding: 0;margin-left: 5px"-->
                      <!--                             class="el-icon-loading"-->
                      <!--                          ></i>-->
                      <!--                        </b-button>-->
                      <!--                      </template>-->
                    </b-table>
                    <div class="hidden-lg-and-up">
                      <DepositSelectors @next="mobileNext" :funding-way="depositWay"></DepositSelectors>
                    </div>
                  </div>
                  <div :key="2" v-if="active===1" style="padding: 30px">
                    <div v-if="selectedDepositType.dataBaseName === 'jpbank'">
                      <JPDomesticBankWireDepositForm :trading-account-list="tradingAccountList"
                                                     :account-name-login-list="accountNameLoginList"
                                                     @doDeposit="getDepositResult"
                                                     :back-method="back"
                                                     :next-method="next"></JPDomesticBankWireDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'mybitwallet'">
                      <BitWalletDepositForm ref="BitWalletDepositForm" :trading-account-list="tradingAccountList"
                                            :next-method="next" :back-method="back"
                                            :account-name-login-list="accountNameLoginList"
                                            @doDeposit="getDepositResult"></BitWalletDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'none'">
                      <InternationalBankWireDepositForm :back-method="back"></InternationalBankWireDepositForm>
                    </div>
                    <div v-if="selectedDepositType.depositMethods === 'Crypto'||
                    selectedDepositType.depositMethods=='加密货币' ||
                    selectedDepositType.depositMethods=='仮想通貨' ||
                    selectedDepositType.depositMethods=='虛擬貨幣' ||
                    selectedDepositType.depositMethods=='สกุลเงินเสมือน' ||
                    selectedDepositType.depositMethods=='Tiền ảo'">
                      <CryptoFundingDepositForm :trading-account-list="tradingAccountList" :next-method="next"
                                                :back-method="back" :account-name-login-list="accountNameLoginList"
                                                @doDeposit="getDepositResult"></CryptoFundingDepositForm>
                    </div>
                    <div v-if="
                    (selectedDepositType.dataBaseName === 'pcins') &&
                    selectedDepositType.dataBaseName === 'pcins'">
                      <CreditCardDepositForm :trading-account-list="tradingAccountList" :next-method="next"
                                             :back-method="back" :account-name-login-list="accountNameLoginList"
                                             @doDeposit="getDepositResult"></CreditCardDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName.startsWith('awepay')">
                      <AwePayDepositForm :trading-account-list="tradingAccountList" :next-method="next"
                                         :back-method="back" :account-name-login-list="accountNameLoginList"
                                         @doDeposit="getDepositResult"
                                         :depositMethods="selectedDepositType.dataBaseName"></AwePayDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'crypto356'">
                      <RMBFundingDepositForm :trading-account-list="tradingAccountList" :next-method="next"
                                             :back-method="back" :account-name-login-list="accountNameLoginList"
                                             @doDeposit="getDepositResult"></RMBFundingDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName.startsWith('myPay')">
                      <RMB2FundingDepositForm :trading-account-list="tradingAccountList" :next-method="next"
                                              :back-method="back" :account-name-login-list="accountNameLoginList"
                                              @doDeposit="getDepositResult"
                                              :depositMethods="selectedDepositType.dataBaseName"></RMB2FundingDepositForm>
                    </div>
                    <!-- Card/Mobile/Instant Payment -->
                    <div
                      v-if="
                      (selectedDepositType.depositMethods === 'Credit Card' ||
                      selectedDepositType.depositMethods === 'Apple Pay / Google Pay' ||
                      selectedDepositType.depositMethods === 'Neteller / Skrill') &&
                      selectedDepositType.dataBaseName === 'xCoins'"
                    >
                      <XCoinsDepositForm
                        :trading-account-list="tradingAccountList"
                        :account-name-login-list="accountNameLoginList"
                        :next-method="next"
                        :back-method="back"
                        :XCoinsType="XCoinsType"
                        :depositMethods="selectedDepositType.depositMethods"
                        @doDeposit="getDepositResult"
                      ></XCoinsDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'mybanq_deposit'">
                      <MybanQDepositForm :trading-account-list="tradingAccountList"
                                         :account-name-login-list="accountNameLoginList"
                                         :next-method="next"
                                         :back-method="back"
                                         @doDeposit="getDepositResult"></MybanQDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'grandPay'">
                      <GrandPayDepositForm :trading-account-list="tradingAccountList"
                                           :account-name-login-list="accountNameLoginList"
                                           :next-method="next"
                                           :back-method="back"
                                           @doDeposit="getDepositResult"></GrandPayDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'monetix'">
                      <MonetixDepositForm :trading-account-list="tradingAccountList"
                                           :account-name-login-list="accountNameLoginList"
                                           :next-method="next"
                                           :back-method="back"
                                           @doDeposit="getDepositResult"></MonetixDepositForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'monetix-MYR'">
                      <MonetixMYRDepositForm :trading-account-list="tradingAccountList"
                                          :account-name-login-list="accountNameLoginList"
                                          :next-method="next"
                                          :back-method="back"
                                          @doDeposit="getDepositResult"></MonetixMYRDepositForm>
                    </div>
                  </div>

                  <div :key="3" v-if="active===2" style="overflow: auto;">
                    <div v-if="selectedDepositType.depositMethods === 'Bitwallet'">
                      <BitWalletConfirmForm :back-method="back" :result="result"></BitWalletConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'jpbank'">
                      <JPDepositBankWireConfirmForm :back-method="back" :next-method="next" :result="result"
                                                    @getResult="getDepositResult"></JPDepositBankWireConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.depositMethods === 'Crypto'||
                    selectedDepositType.depositMethods=='加密货币' ||
                    selectedDepositType.depositMethods=='仮想通貨' ||
                    selectedDepositType.depositMethods=='虛擬貨幣' ||
                    selectedDepositType.depositMethods=='สกุลเงินเสมือน' ||
                    selectedDepositType.depositMethods=='Tiền ảo'">
                      <CryptoFundingConfirmForm :back-method="back" :next-method="next" :result="result"
                                                @doConfirm="getDepositResult">
                      </CryptoFundingConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName.startsWith('awepay')">
                      <AwepayConfirmForm :back-method="back" :next-method="next" :result="result"
                                         @doConfirm="getDepositResult">
                      </AwepayConfirmForm>
                    </div>
                    <div v-if="
                    (selectedDepositType.dataBaseName === 'pcins') &&
                    selectedDepositType.dataBaseName === 'pcins'">
                      <CreditCardConfirmForm :back-method="back" :next-method="next" :result="result"
                                             @doConfirm="getDepositResult">
                      </CreditCardConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'crypto356'">
                      <RMBFundingConfirmForm :back-method="back" :next-method="next" :result="result"
                                             @doConfirm="getDepositResult">
                      </RMBFundingConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName.startsWith('myPay')">
                      <RMB2FundingConfirmForm :back-method="back" :next-method="next" :result="result"
                                              @doConfirm="getDepositResult"
                                              :depositMethods="selectedDepositType.dataBaseName">
                      </RMB2FundingConfirmForm>
                    </div>
                    <div v-if="
                      (selectedDepositType.depositMethods === 'Credit Card' ||
                      selectedDepositType.depositMethods === 'Apple Pay / Google Pay' ||
                      selectedDepositType.depositMethods === 'Neteller / Skrill') &&
                      selectedDepositType.dataBaseName === 'xCoins'">
                      <XCoinsConfirmForm :back-method="back"
                                         :next-method="next"
                                         :result="result"
                                         @doConfirm="getDepositResult"
                                         :depositMethods="selectedDepositType.depositMethods"
                                         :dataBaseName="selectedDepositType.dataBaseName">
                      </XCoinsConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'mybanq_deposit'">
                      <MybanQConfirmForm :back-method="back"
                                         :next-method="next"
                                         :result="result"
                                         @doConfirm="getDepositResult">
                      </MybanQConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'grandPay'">
                      <GrandPayConfirmForm :back-method="back"
                                           :next-method="next"
                                           :result="result"
                                           @doConfirm="getDepositResult">
                      </GrandPayConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'monetix'">
                      <MonetixConfirmForm :back-method="back"
                                           :next-method="next"
                                           :result="result"
                                           @doConfirm="getDepositResult">
                      </MonetixConfirmForm>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'monetix-MYR'">
                      <MonetixMYRConfirmForm :back-method="back"
                                          :next-method="next"
                                          :result="result"
                                          @doConfirm="getDepositResult">
                      </MonetixMYRConfirmForm>
                    </div>
                  </div>

                  <div :key="4" v-if="active===3" style="overflow-x: auto;">
                    <div v-if="selectedDepositType.dataBaseName === 'jpbank'">
                      <JPDepositBankResult :result="result"></JPDepositBankResult>
                    </div>
                    <div v-if="selectedDepositType.depositMethods === 'Crypto'||
                    selectedDepositType.depositMethods=='加密货币' ||
                    selectedDepositType.depositMethods=='仮想通貨' ||
                    selectedDepositType.depositMethods=='虛擬貨幣' ||
                    selectedDepositType.depositMethods=='สกุลเงินเสมือน' ||
                    selectedDepositType.depositMethods=='Tiền ảo'">
                      <CryptoFundingDepositResult :result="result"></CryptoFundingDepositResult>
                    </div>
                    <div v-if="
                    (selectedDepositType.dataBaseName === 'pcins') &&
                    selectedDepositType.dataBaseName === 'pcins'">
                      <CreditCardDepositResult :result="result"></CreditCardDepositResult>
                    </div>
                    <div v-if="selectedDepositType.dataBaseName === 'mybanq_deposit'">
                      <MybanQDepositResult :result="result"></MybanQDepositResult>
                    </div>
                  </div>
                </transition-group>
              </div>
            </transition-group>
          </div>
        </div>
      </b-col>

      <div v-if="false">
        <b-col sm="12" md="12" lg="4" class="right-tip" v-if="selectedDepositType.depositMethods !=='国際銀行送金' ">
          <div style="padding-bottom: 20px;margin: 0">
            {{ $t('transaction.deposit.tips.title') }}

            <div v-if="selectedDepositType.depositMethods !== 'bitwallet' &&
            (selectedDepositType.depositMethods !== 'JPY Local Bank'&&
                    selectedDepositType.depositMethods !=='日本银行转账' &&
                    selectedDepositType.depositMethods !=='日本円国内銀行送金') &&
            (selectedDepositType.depositMethods !== 'Crypto'&&
                    selectedDepositType.depositMethods !=='加密货币' &&
                    selectedDepositType.depositMethods !=='仮想通貨') ">
              <b-col class="tips">
                {{ $t('transaction.deposit.tips.test1') }}
              </b-col>
              <b-col class="tips">
                {{ $t('transaction.deposit.tips.test2') }}
              </b-col>
              <b-col class="tips">
                {{ $t('transaction.deposit.tips.test3') }}
              </b-col>
              <b-col class="tips">
                {{ $t('transaction.deposit.tips.test4') }}
              </b-col>
              <b-col class="tips">
                {{ $t('transaction.deposit.tips.test5') }}
              </b-col>
              <b-col class="tips">
                <a
                  href="https://help.myfxmarkets.com/hc/ja/categories/18892954866585-%E5%85%A5%E9%87%91-%E5%87%BA%E9%87%91">{{
                    $t('transaction.deposit.tips.test6')
                  }}</a>
              </b-col>
              <b-col class="tips">
                {{ $t('transaction.deposit.tips.test7') }}
              </b-col>
              <b-col class="tips">
                <a
                  href="https://help.myfxmarkets.com/hc/ja/articles/19017306334233-MAM-PAMM%E5%8F%A3%E5%BA%A7%E3%81%AE%E5%87%BA%E9%87%91%E7%94%B3%E8%AB%8B%E6%89%8B%E9%A0%86">{{
                    $t('transaction.deposit.tips.test8')
                  }}</a>
              </b-col>

            </div>

            <div style="height: 545px;overflow: auto;"
                 v-if="selectedDepositType.depositMethods === 'bitwallet' && active=== 1">
              <b-col class="tips">
                <span>{{ $t('transaction.deposit.bitwallet.title') }}</span>
              </b-col>
              <b-col class="tips">
                <span>{{ $t('transaction.deposit.bitwallet.test5') }}</span>
              </b-col>
              <b-col class="tips">
                <span>{{ $t('transaction.deposit.bitwallet.test6') }}</span>
              </b-col>
              <b-col class="tips">
                <span>{{ $t('transaction.deposit.bitwallet.test7') }}</span>
              </b-col>
            </div>
            <div style="height: 545px;overflow: auto;"
                 v-if="selectedDepositType.depositMethods === 'bitwallet' && active=== 2">
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.bitwallet.test8') }} </span>
              </b-col>
            </div>
            <div style="height: 545px;overflow: auto;" v-if="selectedDepositType.depositMethods === 'JPY Local Bank'||
                    selectedDepositType.depositMethods=='日本银行转账' ||
                    selectedDepositType.depositMethods=='日本円国内銀行送金' ||
                    selectedDepositType.depositMethods=='日本銀行轉帳' ||
                    selectedDepositType.depositMethods=='การโอนเงินผ่านธนาคารในประเทศเยน' ||
                    selectedDepositType.depositMethods=='Chuyển khoản ngân hàng Nhật Bản' &&
                    active === 1">
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.jpbank.title2') }} </span>
              </b-col>
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.jpbank.test8') }} </span>
              </b-col>
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.jpbank.test9') }} </span>
              </b-col>
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.jpbank.test10') }} </span>
              </b-col>
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.jpbank.test11') }} </span>
              </b-col>
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.jpbank.test12') }} </span>
              </b-col>
            </div>
            <div style="height: 545px;overflow: auto;" v-if="selectedDepositType.depositMethods === 'Crypto'||
                    selectedDepositType.depositMethods =='加密货币' ||
                    selectedDepositType.depositMethods =='虛擬貨幣' ||
                    selectedDepositType.depositMethods =='สกุลเงินเสมือน' ||
                    selectedDepositType.depositMethods =='Chuyển khoản ngân hàng Nhật Bản' ||
                    selectedDepositType.depositMethods =='仮想通貨' &&
                    active === 1">
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.crpto.test8') }} </span>
              </b-col>
              <b-col class="tips">
                <span> {{ $t('transaction.deposit.crpto.test9') }} </span>
              </b-col>
            </div>
          </div>
        </b-col>
      </div>
    </b-row>
  </b-container>
</template>

<script>
import JPDomesticBankWireDepositForm
  from '@/views/AccountTransactions/component/DepositFormComponent/JPDomesticBankWireDepositForm'
import BitWalletDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/BitWalletDepositForm'
import InternationalBankWireDepositForm
  from '@/views/AccountTransactions/component/DepositFormComponent/InternationalBankWireDepositForm'
import { crypto365Validate, getDepositIsAvailable, getDepositWayList } from '@/services/deposit'
import BitWalletConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/BitWalletConfirmForm'
import CryptoFundingDepositForm
  from '@/views/AccountTransactions/component/DepositFormComponent/CryptoFundingDepositForm'
import CreditCardDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/CreditCardDepositForm'
import AwePayDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/AwePayDepositForm'
import RMBFundingDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/RMBFundingDepositForm'
import RMB2FundingDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/RMB2FundingDepositForm'
import { getCoEnabledDepositWayList } from '@/Utils/DepositUtil'
import JPDepositBankWireConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/JPDepositBankWireConfirmForm'
import JPDepositBankResult from '@/views/AccountTransactions/component/DepositResultFormComponent/JPDepositBankResult'
import CryptoFundingConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/CryptoFundingConfirmForm'
import CryptoFundingDepositResult
  from '@/views/AccountTransactions/component/DepositResultFormComponent/CryptoFundingDepositResult'
import AwepayConfirmForm from '@/views/AccountTransactions/component/DepositConfirmFormComponent/AwepayConfirmForm'
import CreditCardConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/CreditCardConfirmForm'
import CreditCardDepositResult
  from '@/views/AccountTransactions/component/DepositResultFormComponent/CreditCardDepositResult'
import RMBFundingConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/RMBFundingConfirmForm'
import RMB2FundingConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/RMB2FundingConfirmForm'
import DepositSelectors from '@/views/AccountTransactions/component/MobileComponent/DepoistSelectors'
import XCoinsDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/XCoinsDepositForm.vue'
import XCoinsConfirmForm from '@/views/AccountTransactions/component/DepositConfirmFormComponent/XCoinsConfirmForm.vue'
import MybanQDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/MybanQDepositForm.vue'
import MybanQConfirmForm from '@/views/AccountTransactions/component/DepositConfirmFormComponent/MybanQConfirmForm.vue'
import MybanQDepositResult
  from '@/views/AccountTransactions/component/DepositResultFormComponent/MybanQDepositResult.vue'
import GrandPayDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/GrandPayDepositForm.vue'
import GrandPayConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/GrandPayConfirmForm.vue'
import { getLocal } from '@/Utils/authLocalStorage'

import RequestTableList from '@/views/AccountTransactions/component/RequestTableList.vue'
import MonetixDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/MonetixDepositForm.vue'
import MonetixConfirmForm from '@/views/AccountTransactions/component/DepositConfirmFormComponent/MonetixConfirmForm.vue'
import MonetixMYRDepositForm from '@/views/AccountTransactions/component/DepositFormComponent/MonetixMYRDepositForm.vue'
import MonetixMYRConfirmForm
  from '@/views/AccountTransactions/component/DepositConfirmFormComponent/MonetixMYRConfirmForm.vue'

export default {
  name: 'deposit',
  components: {
    RequestTableList,
    DepositSelectors,
    RMBFundingConfirmForm,
    RMB2FundingConfirmForm,
    CreditCardDepositResult,
    CreditCardConfirmForm,
    AwepayConfirmForm,
    CryptoFundingDepositResult,
    CryptoFundingConfirmForm,
    JPDepositBankResult,
    JPDepositBankWireConfirmForm,
    RMBFundingDepositForm,
    RMB2FundingDepositForm,
    AwePayDepositForm,
    CreditCardDepositForm,
    CryptoFundingDepositForm,
    BitWalletConfirmForm,
    InternationalBankWireDepositForm,
    BitWalletDepositForm,
    JPDomesticBankWireDepositForm,
    XCoinsDepositForm,
    XCoinsConfirmForm,
    MybanQDepositForm,
    MybanQConfirmForm,
    MybanQDepositResult,
    GrandPayDepositForm,
    GrandPayConfirmForm,
    MonetixDepositForm,
    MonetixConfirmForm,
    MonetixMYRDepositForm,
    MonetixMYRConfirmForm
  },
  data () {
    return {
      currentType: '',
      accountNameLoginList: [],
      tradingAccountList: [],
      selectedAccount: '',
      selectedDepositType: {},
      active: 0,
      tradingAccountListLoading: false,
      // depositWayColumns: [{
      //     label: 'Deposit Methods',
      //     key: 'depositMethods',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'status',
      //     key: 'status',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Time To Fund',
      //     key: 'timeToFund',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'Fee',
      //     key: 'fee',
      //     class: 'text-left'
      //   },
      //   {
      //     label: 'operation',
      //     key: 'operation',
      //     class: 'text-left'
      //   }
      // ],
      depositWay: [],
      result: null,
      depositWayLoading: false,
      XCoinsType: ''
    }
  },
  watch: {
    active (n, o) {
      if (this.currentType === 'Bitwallet') {
        if (n === 2) {
          this.$emit('selectEnter', 'Bitwallet_2')
        } else {
          this.$emit('selectEnter', 'Bitwallet')
        }
      } else {
      }

      if (n === 0) {
        this.$emit('selectEnter')
      }
    }
  },
  computed: {
    bitWalletTooltip () {
      return this.$t('transaction.deposit.bitwallet.bitWalletTooltip')
    },
    creditCardToolTip () {
      return this.$t('transaction.deposit.credit_card.creditCardToolTip')
    },
    depositWayColumns () {
      return [
        {
          label: this.$t('transaction.deposit.depositWayColumns.label1'),
          key: 'depositMethods',
          class: 'text-left'
        },
        {
          label: this.$t('transaction.deposit.depositWayColumns.label2'),
          key: 'status',
          class: 'text-left'
        },
        {
          label: this.$t('transaction.deposit.depositWayColumns.label3'),
          key: 'timeToFund',
          class: 'text-left'
        },
        {
          label: this.$t('transaction.deposit.depositWayColumns.label4'),
          key: 'fee',
          class: 'text-left'
        }
        // {
        //   label: this.$t('transaction.deposit.depositWayColumns.label5'),
        //   key: 'operation',
        //   class: 'text-left'
        // }
      ]
    }
  },
  created () {
    this.depositWayLoading = true
    this.getTradingAccount()
    this.getDepositWay()
    // 避免接口过慢，获取不到账户
    // setTimeout(() => {

    // }, 1000)
  },
  methods: {
    getLocal,

    tableRowClick (data, idx, ev) {
      if (data.maintenance || !data.usable) return
      this.$vp.add({ message: `[Deposits] Click ${ data.depositMethods }` })
      this.selectDepositType({ item: data })
    },

    getTradingAccount () {
      this.tradingAccountList = this.$store.getters.commonGet(['tradingAccountList'])
      this.tradingAccountList.forEach(item => {

        item.accountType = item.accountType || ''
        if (!item.mt4group?.toLowerCase()?.includes('rebate') &&
          !item.mt4group?.toLowerCase()?.includes('-pf-') &&
          !item.accountType?.toLowerCase()?.includes('ctrader') &&
          !item.mt4group?.toLowerCase()?.includes('disabled') &&
          !item.mt4group?.toLowerCase()?.includes('-pf') &&
          !item.mt4group?.toLowerCase()?.includes('removed') /* &&
          !item.accountType.toLowerCase().includes('master') */) {
          if ((item.mt4group?.includes('MAM') || item.mt4group?.includes('PAMM'))) {
            if (item.mt4group?.toUpperCase()?.includes('-SUB-') ||
              item.mt4group?.toUpperCase()?.includes('-CLT') ||
              item.mt4group?.toUpperCase()?.includes('-CLIENT')) {
              // for testing purpose.)
              const accountNameLogin = item.login
              const accountName = item.accountName
              this.accountNameLoginList.push(accountNameLogin + '-' + accountName)
            }
          } else {
            const accountNameLogin = item.login
            const accountName = item.accountName
            this.accountNameLoginList.push(accountNameLogin + '-' + accountName)
          }
        }
      })
    },
    getDepositWay () {

      getDepositWayList().then(res => {
        const user = JSON.parse(getLocal('user'))
        const data = res.data

        // 通道是否可用
        getDepositIsAvailable(user.myguid).then(res => {

          // 从上层回调中移到本层，避免所有通道直接渲染
          this.depositWay = getCoEnabledDepositWayList(data).map(way => {
            // way.usable = null
            way.usable = true
            return way
          })

          this.depositWayLoading = false

          // -----------------------------------------

          this.depositWay.forEach(item => {
            //International Bank Wire默认开启
            if (item.dataBaseName === 'none') {
              item.usable = true
            } else {
              item.usable = res.data[item.dataBaseName] ?? false
            }
          })



          // --------------------------------------------------------
          const accountEmail = JSON.parse(getLocal('user')).email

          // 指定用户仅可见 MyPay
          const assignAccountMyPay = ['<EMAIL>']
          // 判断是否在 assignAccountMyPay 名单内
          if (assignAccountMyPay.includes(accountEmail)) {
            this.depositWay = this.depositWay.filter(f => f.dataBaseName === 'myPay')
          }

          // 指定用户不可见 XCoin 和 Credit
          const assignAccountNotXCoinAndCredit = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
          if (assignAccountNotXCoinAndCredit.includes(accountEmail)) {
            this.depositWay = this.depositWay.filter(f => f.dataBaseName !== 'xCoins' && f.dataBaseName !== 'grandPay')
          }

        }).catch(error => {
          console.log(error)
        })
      })

    },
    getDepositResult (data) {
      this.result = data
    },
    setIconClass (data) {
      let className
      if (data.item.depositMethods.replace(/\s*/g, '') == 'RMB2Funding') {
        className = 'RMB2Funding'
      } else {
        className = data.item.depositMethods.replace(/\s*/g, '')
        if (data.item.dataBaseName === 'pcins' || data.item.dataBaseName === 'grandPay') {
          className = 'CreditCard'
        }
        if (data.item.dataBaseName === 'none') {
          className = 'InternationalBankWire'
        }
        if (data.item.dataBaseName === 'usdt') {
          className = 'CryptoCurrency'
        }
        if (data.item.dataBaseName === 'jpbank') {
          className = 'JPDomesticBankWire'
        }
        if (data.item.dataBaseName === 'crypto356') {
          className = 'RMBFunding'
        }
        if (data.item.dataBaseName.startsWith('myPay')) {
          // className = 'RMB2Funding'
          className = data.item.dataBaseName
        }
        if (className == 'CreditCard' && data.item.dataBaseName === 'xCoins') {
          className = 'xCoins'
        }
        if (className == 'ApplePay/GooglePay' && data.item.dataBaseName === 'xCoins') {
          className = 'xCoins1'
        }
        if (className == 'Neteller/Skrill' && data.item.dataBaseName === 'xCoins') {
          className = 'xCoins2'
        }
        if (data.item.dataBaseName === 'monetix' && JSON.parse(getLocal('user')).country === 'Thailand') {
          className = 'monetix'
        }
        if (data.item.dataBaseName === 'monetix' && JSON.parse(getLocal('user')).country === 'Philippines') {
          className = 'monetix'
        }
        if (data.item.dataBaseName === 'monetix-MYR') {
          className = 'monetix-MYR'
        }
        if (data.item.dataBaseName.startsWith('awepay')) {
          className = 'Awepay'
        }
      }
      return 'el-icon-' + className
    },
    async selectDepositType (data) {
      data.item.type && (this.XCoinsType = data.item.type)
      this.currentType = data.item.depositMethods
      this.$emit('selectEnter', data.item.depositMethods)

      if (data.item === 'RMB Funding') {
        const myguid = JSON.parse(getLocal('user')).myguid
        await crypto365Validate(myguid).then(res => {
          this.selectedDepositType = data.item
          this.next()
        }).catch(error => {
          const panel = 'Identification'
          this.$router.push({
            name: '/myAccount/myDocument',
            params: {
              panel
            }
          })
        })
      } else {
        this.selectedDepositType = data.item
        console.log(this.selectedDepositType)
        this.next()
      }
    },
    next () {
      if (this.active++ > 3) this.active = 0
    },
    mobileNext (val) {
      const data = {
        item: val
      }
      this.selectDepositType(data)
    },
    back () {
      this.active--
      if (this.active === 0) {
        this.selectedDepositType = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.deposit-box {
  border: 1px solid #DBDCFC;
}

@media screen and (max-width: 992px) {
  .deposit-box {
    border: 0;
  }
}

.step {
  ::v-deep .el-steps {
    .is-process {
      color: #495EEB;

      .el-step__icon {
        border-color: #495EEB;
      }
    }
  }
}

.el-icon-JapaneseYenBankTransfer {
  background: url(~@/assets/images/paymentway/icon_1.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-JapaneseYenBankTransfer:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-CNYWithdrawal {
  background: url(~@/assets/images/paymentway/icon_6.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-CNYWithdrawal:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CreditCard {
  background: url(~@/assets/images/paymentway/icon_2.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CreditCard:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-InternationalBankWire {
  background: url(~@/assets/images/paymentway/icon_3.png) center no-repeat;
  font-size: 40px;
  background-size: contain;
  width: 100%;
}

.el-icon-InternationalBankWire:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-bitwallet {
  background: url(~@/assets/images/paymentway/icon_4.png) center no-repeat;
  font-size: 40px;
  background-size: contain;
  width: 100%;
}

.el-icon-bitwallet:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-Bitwallet {
  background: url(~@/assets/images/paymentway/icon_4.png) center no-repeat;
  font-size: 40px;
  background-size: contain;
  width: 100%;
}

.el-icon-Bitwallet:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-CryptoFunding {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  font-size: 40px;
  background-size: contain;
  width: 100%;
}

.el-icon-CryptoFunding:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-Crypto {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  font-size: 40px;
  background-size: contain;
  width: 100%;
}

.el-icon-Crypto:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-Awepay {
  background: url(~@/assets/images/paymentway/awepay_logo.png) no-repeat center;
  font-size: 40px;
  width: 100%;
  background-size: contain;
}

.el-icon-Awepay:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-RMBFunding {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  font-size: 40px;
  background-size: contain;
}

.el-icon-RMBFunding:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-RMB2Funding {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  font-size: 40px;
  background-size: contain;
}

.el-icon-RMB2Funding:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-JPDomesticBankWire {
  background: url(~@/assets/images/paymentway/icon_1.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-JPDomesticBankWire:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CryptoCurrency {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CryptoCurrency:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-xCoins {
  background: url(~@/assets/images/paymentway/icon_7.svg) center no-repeat;
  width: 100%;
  background-size: 80% 80%;
  //background-size: contain;
}

.el-icon-xCoins:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-xCoins1 {
  background: url(~@/assets/images/paymentway/icon_8.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-xCoins1:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-xCoins2 {
  background: url(~@/assets/images/paymentway/icon_9.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-xCoins2:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-monetix {
  background: url(~@/assets/images/paymentway/icon_10.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-monetix:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-monetix-MYR {
  background: url(~@/assets/images/paymentway/monetix-MYR.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-monetix-MYR:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}


.tips {
  margin-top: 20px;
}

.deposit-enter,
.deposit-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.deposit-enter-to,
.deposit-leave {
  opacity: 1;

}

.deposit-enter-active {
  transition: all 0.7s;
  transition-delay: 0.8s;
}

.deposit-leave-active {
  transition: all 0.7s;
}

.form-enter,
.form-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.form-enter-to,
.form-leave {
  opacity: 1;

}

.form-enter-active {
  transition: all 0.7s;
  transition-delay: 0.8s;
}

.form-leave-active {
  transition: all 0.7s;
}

.steps-enter,
.steps-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.steps-enter-to,
.steps-leave {
  opacity: 1;

}

.steps-enter-active {
  transition: all 0.7s;
  transition-delay: 0.8s;
}

.steps-leave-active {
  transition: all 0.7s;
}

.bTable {
  ::v-deep th {
    border-bottom: 1px solid #EBEEF5;
    color: rgb(73, 94, 235);
  }

  ::v-deep tbody {

    tr {
      position: relative;
      //border: 1px solid transparent;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        pointer-events: none;
      }

      &:hover::after {
        border: 2px solid #495EEB;
      }
    }
  }
}

.creditText {
  color: red;
  font-size: 16px;
  font-weight: 600;
  margin-left: -5px;
}

.table-button {
  transition: all .3s;
  background-color: #667aec;

  &:hover {
    box-shadow: -4px 4px 2px 0px rgba(73, 94, 235, .6);
    transform: translate(4px, -4px);
    background-color: #495eeb;
  }
}

.card_type {
  display: block!important;
  color: #2E2138;
  font-size: 23.04px;
}

.card_type1 {
  display: none!important;
  color: #2E2138;
  font-size: 23.04px;
  margin: -15px 0 0 16px;
}

@media screen and (max-width: 720px) {
  .creditText {
    font-size: 12px;
  }

  .card_type {
    display: none!important;
  }

  .card_type1 {
    display: block!important;
  }
}

.table-icon {
  display: flex;
  align-items: center;

  i {
    width: fit-content !important;
    font-size: 32px;

    &::before {
      font-size: 32px;
    }
  }
}

</style>

<style>
.table th {
  text-transform: none !important;
}

.el-step__description {
  margin-top: 5px;
}
</style>
