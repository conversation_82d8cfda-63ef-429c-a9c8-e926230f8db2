<template>
  <div v-loading="checkLoading">
    <!--    <ValidationObserver ref="form" v-slot="{ handleSubmit }">-->
    <!--      <b-form @submit.stop.prevent="handleSubmit(next)">-->
    <!--        <ValidationProvider name="transaction.account" rules="selectionValidate" v-slot="{ errors }">-->
    <!--          <b-form-group-->
    <!--            label-cols-sm="2"-->
    <!--            :label="$t('transaction.deposit.crpto.label1')">-->
    <!--            <b-select :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')" :disabled="submitLoading"-->
    <!--                      :options="accountNameLoginList"-->
    <!--                      v-model="selectedAccount"-->
    <!--                      style="width: 100%;height: 45px;border-radius: 8px;border-color: #DBDCFC">-->
    <!--            </b-select>-->
    <!--            <div class="validation">-->
    <!--              <span>{{ errors[0] }}</span>-->
    <!--            </div>-->
    <!--          </b-form-group>-->
    <!--        </ValidationProvider>-->

    <!--        <ValidationProvider name="transaction.deposit.crpto.label2" :rules="amountValidationRules" v-slot="{ errors }">-->
    <!--          <b-form-group-->
    <!--            label-cols-sm="2"-->
    <!--            :label="$t('transaction.deposit.crpto.label2')">-->
    <!--            <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
    <!--                          :placeholder="amountInputPlaceHolder"-->
    <!--                          :disabled="selectedAccountMyguid.length===0 || submitLoading"-->
    <!--                          v-model="amount" style="width: 100%;float: left;border-color: #DBDCFC"></b-form-input>-->
    <!--            <span style="float: left;margin: 10px">{{ $t('transaction.deposit.crpto.test') }} {{ currency }}</span>-->
    <!--            <div class="validation">-->
    <!--              <span>{{ errors[0] }}</span>-->
    <!--            </div>-->
    <!--          </b-form-group>-->
    <!--        </ValidationProvider>-->

    <!--        <ValidationProvider name="transaction.deposit.crpto.label3" rules="selectionValidate" v-slot="{ errors }">-->
    <!--          <b-form-group-->
    <!--            label-cols-sm="2"-->
    <!--            :label="$t('transaction.deposit.crpto.label3')">-->
    <!--            <b-form-select :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
    <!--                           :disabled="submitLoading" v-model="selectedPaymentType" :options="paymentType"-->
    <!--                           style="width: 100%;border-color: #DBDCFC;height: 45px;border-radius: 8px"></b-form-select>-->
    <!--            <div class="validation">-->
    <!--              <span>{{ errors[0] }}</span>-->
    <!--            </div>-->
    <!--          </b-form-group>-->
    <!--        </ValidationProvider>-->

    <!--        <ValidationProvider name="transaction.deposit.crpto.label4" rules="required" v-slot="{ errors }">-->
    <!--          <b-form-group-->
    <!--            label-cols-sm="2"-->
    <!--            :label="$t('transaction.deposit.crpto.label4')">-->
    <!--            <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
    <!--                          :disabled="submitLoading" v-model="walletCompanyName"-->
    <!--                          style="width: 100%;border-color: #DBDCFC"></b-form-input>-->
    <!--            <div class="validation">-->
    <!--              <span>{{ errors[0] }}</span>-->
    <!--            </div>-->
    <!--          </b-form-group>-->
    <!--        </ValidationProvider>-->

    <!--        <ValidationProvider name="transaction.deposit.crpto.label5" rules="required" v-slot="{ errors }">-->
    <!--          <b-form-group-->
    <!--            label-cols-sm="2"-->
    <!--            :label="$t('transaction.deposit.crpto.label5')">-->
    <!--            <b-form-input :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
    <!--                          :disabled="submitLoading" v-model="cryptoAddress"-->
    <!--                          style="width: 100%;border-color: #DBDCFC"></b-form-input>-->
    <!--            <div class="validation">-->
    <!--              <span>{{ errors[0] }}</span>-->
    <!--            </div>-->
    <!--          </b-form-group>-->
    <!--        </ValidationProvider>-->

    <!--        <ValidationProvider name="transaction.deposit.crpto.label6"-->
    <!--                            rules="selectionValidate" v-slot="{ errors }">-->
    <!--          <b-form-group-->
    <!--            label-cols-sm="2"-->
    <!--            :label="$t('transaction.deposit.crpto.label6')">-->
    <!--            <b-form-select :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
    <!--                           :disabled="submitLoading" v-model="selectedChain" :options="chain"-->
    <!--                           style="width: 100%;border-color: #DBDCFC;border-radius: 8px;height: 45px"></b-form-select>-->
    <!--            <div class="validation">-->
    <!--              <span>{{ errors[0] }}</span>-->
    <!--            </div>-->
    <!--          </b-form-group>-->
    <!--        </ValidationProvider>-->

    <!--        <b-button-->
    <!--          :disabled="submitLoading"-->
    <!--          variant="primary"-->
    <!--          style="float:right;margin-top: 10px"-->
    <!--          type="submit"-->
    <!--          v-viewpoint.click="{ message: `[Crypto Funding Deposits] Click next to deposits form` }">-->
    <!--          {{ $t('sidebar.next') }}<i style="padding: 4px" v-if="submitLoading"-->
    <!--                                     class="el-icon-loading"></i>-->
    <!--        </b-button>-->
    <!--        <b-button-->
    <!--          :disabled="submitLoading"-->
    <!--          style="margin-top: 10px"-->
    <!--          variant="outline-primary"-->
    <!--          @click="back"-->
    <!--          v-viewpoint.click="{ message: `[Crypto Funding Deposits] Click back to deposits page` }">-->
    <!--          {{ $t('sidebar.back') }}-->
    <!--        </b-button>-->
    <!--      </b-form>-->
    <!--    </ValidationObserver>-->

    <el-form v-loading="submitLoading" ref="CryptoFundingForm" :model="form" :rules="formRules" label-position="top"
             label-width="100px">
      <el-form-item :label="$t('transaction.deposit.AllForm.account')" prop="selectedAccount">
        <el-select
          style="width: 100%"
          v-model="form.selectedAccount"
          :placeholder="$t('transaction.deposit.AllForm.accountPlaceholder')"
          clearable
          @change="updateData"
        >
          <el-option
            v-for="item in accountNameLoginList"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('transaction.deposit.AllForm.amount')" prop="fundingAmount">
        <el-input v-model="form.fundingAmount"
                  :placeholder="amountPlaceholder || $t('transaction.deposit.AllForm.amountPlaceholder')"/>
        <div class="amount-hit">
          <div style="font-size: 12px;line-height: 30px; white-space: nowrap">
            {{ $t('transaction.deposit.AllForm.currency') }}：{{ form.fundingCurrency || '--' }}
          </div>
          <div style="font-size: 12px; line-height: 1.1rem">{{ $t('transaction.deposit.AllForm.cryptoAmountHint') }}</div>
        </div>
      </el-form-item>
      <el-form-item :label="$t('transaction.deposit.AllForm.payType')" prop="paymentType">
        <el-select
          style="width: 100%"
          v-model="form.paymentType"
          :placeholder="$t('transaction.deposit.AllForm.payTypePlaceholder')"
          clearable
          @change="updateData"
        >
          <el-option
            v-for="item in paymentType"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('transaction.deposit.AllForm.cryptoCompanyName')" prop="companyName">
        <el-input v-model="form.companyName"
                  :placeholder="$t('transaction.deposit.AllForm.cryptoCompanyNamePlaceholder')"/>
      </el-form-item>
      <el-form-item :label="$t('transaction.deposit.AllForm.cryptoCompanyAddress')" prop="address">
        <el-input v-model="form.address"
                  :placeholder="$t('transaction.deposit.AllForm.cryptoCompanyAddressPlaceholder')"/>
      </el-form-item>
      <el-form-item :label="$t('transaction.deposit.AllForm.chain')" prop="chain"
                    v-if="form.paymentType === 'USDT' || form.paymentType === 'USDC'">
        <!--        <el-input v-model="form.chain" placeholder="请选择区块链"/>-->
        <el-select
          style="width: 100%"
          v-model="form.chain"
          :placeholder="$t('transaction.deposit.AllForm.chainPlaceholder')"
          clearable
        >
          <el-option
            v-for="item in chain"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px;display: flex;justify-content:space-between;align-items: center;">
      <b-button
        class="btn-common-shadow"
        :disabled="submitLoading"
        style="margin-top: 10px;"
        variant="outline-primary"
        @click="back"
        v-viewpoint.click="{ message: `[Crypto Funding Deposits] Click back to deposits page` }">
        {{ $t('sidebar.back') }}
      </b-button>
      <b-button
        class="btn-common-shadow"
        :disabled="submitLoading"
        variant="primary"
        @click="submitForm"
        v-viewpoint.click="{ message: `[Crypto Funding Deposits] Click next to deposits form` }">
        {{ $t('sidebar.next') }}<i style="padding: 4px" v-if="submitLoading"
                                   class="el-icon-loading"></i>
      </b-button>
    </div>
  </div>
</template>

<script>
import { cryptoFundingCheck, cryptoFundingDeposit } from '@/services/deposit'
import { getSignByCurrency } from '@/Utils/CurrencySignMappingUtil'
import { verifyDepositsAmountRange } from '@/Utils/customValidate'

export default {
  name: 'CryptoFundingDepositForm',
  props: {
    accountNameLoginList: Array,
    tradingAccountList: Array,
    nextMethod: Function,
    backMethod: Function
  },
  mounted () {
    this.check()
  },
  watch: {
    // 'form.selectedAccount': {
    //   handler (newVal, oldVal) {

    // if (newVal === null || newVal === undefined) {
    //   return
    // }
    // const login = newVal.split('-')[0]
    // this.tradingAccountList.forEach(item => {
    //   if (item.login.toString() === login.toString()) {
    //     this.currency = item.currency
    //     this.selectedAccountMyguid = item.myguid
    //   }
    // })
    //
    // if (this.selectedPaymentType.includes('USD')) {
    //   Object.keys(this.amountLimitList.cryptoFunding_usdx).forEach(item => {
    //     if (item.includes(this.currency)) {
    //       if (item.includes('MAX')) {
    //         this.amountMax = this.amountLimitList.cryptoFunding_usdx[item]
    //       }
    //       if (item.includes('MIN')) {
    //         this.amountMin = this.amountLimitList.cryptoFunding_usdx[item]
    //       }
    //     }
    //   })
    // } else {
    //   Object.keys(this.amountLimitList.cryptoFunding).forEach(item => {
    //     if (item.includes(this.currency)) {
    //       if (item.includes('MAX')) {
    //         this.amountMax = this.amountLimitList.cryptoFunding[item]
    //       }
    //       if (item.includes('MIN')) {
    //         this.amountMin = this.amountLimitList.cryptoFunding[item]
    //       }
    //     }
    //   })
    // }
    //
    // if (this.currency !== 'JPY') {
    //   const max = this.amountMax.toFixed(1)
    //   const min = this.amountMin.toFixed(1)
    //   this.amountInputPlaceHolder = getSignByCurrency(this.currency) + min + ' ~ ' + getSignByCurrency(this.currency) + max
    // } else {
    //   this.amountInputPlaceHolder = getSignByCurrency(this.currency) + this.amountMin + ' ~ ' + getSignByCurrency(this.currency) + this.amountMax
    // }
    //
    // this.setAndValidateAmount()
    //   }
    // },
    // selectedPaymentType: {
    //   immediate: true,
    //   handler (newVal, oldVal) {
    //     if (this.selectedAccountMyguid.length !== 0) {
    //       if (this.selectedPaymentType.includes('USD')) {
    //         Object.keys(this.amountLimitList.cryptoFunding_usdx).forEach(item => {
    //           if (item.includes(this.currency)) {
    //             if (item.includes('MAX')) {
    //               this.amountMax = this.amountLimitList.cryptoFunding_usdx[item]
    //             }
    //             if (item.includes('MIN')) {
    //               this.amountMin = this.amountLimitList.cryptoFunding_usdx[item]
    //             }
    //           }
    //         })
    //       } else {
    //         Object.keys(this.amountLimitList.cryptoFunding).forEach(item => {
    //           if (item.includes(this.currency)) {
    //             if (item.includes('MAX')) {
    //               this.amountMax = this.amountLimitList.cryptoFunding[item]
    //             }
    //             if (item.includes('MIN')) {
    //               this.amountMin = this.amountLimitList.cryptoFunding[item]
    //             }
    //           }
    //         })
    //       }
    //
    //       if (this.currency !== 'JPY') {
    //         const max = this.amountMax.toFixed(1)
    //         const min = this.amountMin.toFixed(1)
    //         this.amountInputPlaceHolder = getSignByCurrency(this.currency) + min + ' ~ ' + getSignByCurrency(this.currency) + max
    //       } else {
    //         this.amountInputPlaceHolder = getSignByCurrency(this.currency) + this.amountMin + ' ~ ' + getSignByCurrency(this.currency) + this.amountMax
    //       }
    //
    //       this.setAndValidateAmount()
    //     }
    //
    //     if (newVal === 'USDT' || newVal === 'USDC') {
    //       this.chainSelectionVisible = true
    //       return
    //     }
    //     this.chainSelectionVisible = false
    //     this.selectedChain = newVal
    //   }
    // }
  },

  data () {
    return {

      form: {
        selectedAccount: '',
        // selectedChain: '',
        fundingCurrency: '',
        fundingAmount: '',
        mt4AccountMyguid: '',
        paymentType: 'USDT',
        companyName: '',
        address: '',
        chain: ''
      },
      amountMin: 0,
      amountMax: 0,
      amountPlaceholder: '',

      // --------------------------------

      selectedAccount: '',
      selectedChain: '',
      selectedPaymentType: 'USDT',
      amount: null,
      walletCompanyName: '',
      cryptoAddress: '',
      currency: '',
      selectedAccountMyguid: '',
      paymentType: ['USDT', 'USDC', 'BTC', 'ETH'],
      // chain: ['ERC-20', 'TRC-20'],
      chainSelectionVisible: false,
      amountValidationRules: 'required|twoDecimalPlaces',
      checkLoading: false,
      submitLoading: false,
      amountLimitList: [],
      amountInputPlaceHolder: ''
      // amountMax: 0,
      // amountMin: 0
    }
  },

  computed: {
    chain () {
      return this.form.paymentType === 'USDC' ? ['ERC-20'] : ['ERC-20', 'TRC-20']
    },
    formRules () {
      const verifyFundingAmount = (r, v, c) => verifyDepositsAmountRange(r, v, c, this.amountMin, this.amountMax)

      return {
        selectedAccount: [{
          required: true,
          message: this.$t('transaction.deposit.AllForm.accountError_01'),
          trigger: 'change'
        }],
        fundingAmount: [
          {
            required: true,
            message: this.$t('transaction.withdrawal.CreditCard.form.label2_error1'),
            trigger: 'change'
          },
          { required: true, validator: verifyFundingAmount, trigger: 'change' }
        ],
        paymentType: [{
          required: true,
          message: this.$t('transaction.deposit.AllForm.payTypeError_01'),
          trigger: 'change'
        }],
        companyName: [{
          required: true,
          message: this.$t('transaction.deposit.AllForm.cryptoCompanyNameError_01'),
          trigger: 'change'
        }],
        address: [{
          required: true,
          message: this.$t('transaction.deposit.AllForm.cryptoCompanyAddressError_01'),
          trigger: 'change'
        }],
        chain: [{ required: true, message: this.$t('transaction.deposit.AllForm.chainError_01'), trigger: 'change' }]
      }
    }
  },

  methods: {

    updateData () {
      this.form.fundingAmount = ''

      if (!this.form.selectedAccount) {
        this.amountMin = 0
        this.amountMax = 0
        this.form.fundingCurrency = ''
        this.form.mt4AccountMyguid = ''
        this.amountPlaceholder = ''
        return
      }

      const login = this.form.selectedAccount.split('-')[0]
      const f = this.tradingAccountList.find(f => `${f.login}` === `${login}`)
      if (!f) return
      this.form.fundingCurrency = f.currency
      this.form.mt4AccountMyguid = f.myguid
      const currencyMap = { jpy: '¥', usd: '$', aud: 'A$', eur: '€', gbp: '£', usc: '¢' }
      const currencySymbol = currencyMap[f.currency.toLowerCase()]
      if (this.form.paymentType.includes('USD')) {
        this.amountMin = this.amountLimitList.cryptoFunding_usdx[`${f.currency}_MIN`]
        this.amountMax = this.amountLimitList.cryptoFunding_usdx[`${f.currency}_MAX`]
      } else {
        this.amountMin = this.amountLimitList.cryptoFunding[`${f.currency}_MIN`]
        this.amountMax = this.amountLimitList.cryptoFunding[`${f.currency}_MAX`]
      }
      this.amountPlaceholder = `${currencySymbol}${this.amountMin}~${currencySymbol}${this.amountMax}`
    },

    async submitForm () {
      try {
        await this.$refs.CryptoFundingForm.validate()
        this.submitLoading = true
        const res = await cryptoFundingDeposit({
          fundingAmount: this.form.fundingAmount,
          mt4AccountMyguid: this.form.mt4AccountMyguid,
          paymentType: this.form.paymentType,
          companyName: this.form.companyName,
          address: this.form.address,
          chain: this.form.chain?.replace('-', '') || this.form.paymentType
        })
        this.$emit('doDeposit', res.data)
        this.submitLoading = false
        this.nextMethod()
      } catch (err) {
        this.submitLoading = false
        console.log(err)
      }
    },

    // -----------------------------------

    check () {
      this.checkLoading = true
      cryptoFundingCheck().then(res => {
        this.amountLimitList = res.data
        this.amountInputPlaceHolder = ''
        this.checkLoading = false
        const selectedAccount = this.$store.getters.commonGet(['defaultDepositTarget'])
        if (selectedAccount !== null && selectedAccount !== undefined && selectedAccount.length !== 0) {
          this.selectedAccount = selectedAccount.login + '-' + selectedAccount.accountName
          this.form.selectedAccount = selectedAccount.login + '-' + selectedAccount.accountName
        }
      }).catch(res => {

      })
    },
    // next () {
    //   this.submitLoading = true
    //   const data = {
    //     fundingAmount: this.amount,
    //     mt4AccountMyguid: this.selectedAccountMyguid,
    //     paymentType: this.selectedPaymentType,
    //     companyName: this.walletCompanyName,
    //     address: this.cryptoAddress,
    //     chain: this.selectedChain
    //   }
    //   data.chain = data.chain.replace('-', '')
    //   cryptoFundingDeposit(data).then(res => {
    //     const result = res.data
    //     this.$emit('doDeposit', result)
    //     this.submitLoading = false
    //     this.nextMethod()
    //     window.scrollTo({
    //       top: 0,
    //       left: 0
    //     })
    //   }).catch(error => {
    //     this.submitLoading = false
    //   })
    // },
    back () {
      this.backMethod()
    }
    // setAndValidateAmount () {
    //   this.amountValidationRules = 'required|twoDecimalPlaces|depositAmount:' + this.amountMin + ',' + this.amountMax + ',' + getSignByCurrency(this.currency)
    //   console.log(this.amountValidationRules)
    //   // this.$refs.form.validate();
    // }
  }
}
</script>

<style scoped>
.amount-hit {
  display: flex;
  gap: 20px;
  align-items: center;
  line-height: 1.6rem;
}

@media screen and (max-width: 768px) {
  .amount-hit {
    flex-wrap: wrap;
    gap: 0;
  }
}
</style>
