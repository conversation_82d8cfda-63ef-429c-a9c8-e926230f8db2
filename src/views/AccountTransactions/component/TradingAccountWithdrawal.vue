<template>
  <div ref="tradingAccountWithdrawal">
    <h4 style="margin-bottom: 30px;padding: 30px 30px 0 30px;">{{ selectedPaymentType.paymentMethods }}</h4>
    <!--    <span style="color: red">Before you finish the form, please read the attention tips on the right first !</span>-->
    <el-steps style="margin-bottom: 20px" :active="active" finish-status="success" align-center>
      <el-step :description="$t('transaction.withdrawal.description1')"></el-step>
      <el-step :description="$t('transaction.withdrawal.description2')"></el-step>
      <el-step :description="$t('transaction.withdrawal.description3')"></el-step>
    </el-steps>

<!--    <div style="padding: 0 20px 10px; color: red; font-size: 12px" v-if="active == 0" v-html="$t('transaction.withdrawal.newYearTips')" />-->

    <transition-group name="withdrawalWay">
      <div v-loading="countryLoading" style="overflow-x: auto;" :key="1"
           v-if="active===0">
        <div style="padding: 10px 20px;">
          <div>{{ $t('transaction.withdrawal.hint_01') }}</div>
          <div>{{ $t('transaction.withdrawal.hint_02') }}</div>
        </div>
        <b-table
          style="margin: 0;"
          class="hidden-md-and-down bTable"
          :fields="withdrawalWayColumns"
          :items="withdrawalWay"
          @row-clicked="tableRowClick"
        >
          <template v-slot:cell(paymentMethods)="data">
            <b-row style="display: flex;align-items: center">
              <b-col class="table-icon" md="3" style="padding: 0;max-width: 18%">
                <i :class="setIconClass(data)"></i>
              </b-col>
              <b-col style="padding: 0;">
                {{ data.item.paymentMethods }}
              </b-col>
            </b-row>
          </template>
          <template v-slot:cell(timeToWithdraw)="data">
            <div>
              {{ data.value }}
              <el-tooltip v-if="data.item.paymentMethods === $t('transaction.withdrawal.withdrawalWay.credit_card')"
                          effect="light" placement="top">
                <template slot="content">
                  <div style="width: 200px;">
                    <div>
                      {{ $t('transaction.withdrawal.hint_03') }}
                    </div>
                    <div style="margin-top: 6px;">
                      {{ $t('transaction.withdrawal.hint_04') }}
                    </div>
                  </div>
                </template>
                <i
                  class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </template>
          <template v-slot:cell(operation)="data">
            <b-button class="table-button " variant="primary" @click="selectPaymentType(data.item)">
              {{ $t('sidebar.select') }}
            </b-button>
          </template>
        </b-table>
        <div class="hidden-lg-and-up">
          <WithdrawalSelectors @next="getFundingWay" :funding-way="withdrawalWay"></WithdrawalSelectors>
        </div>
      </div>

    </transition-group>

    <transition-group name="security">
      <div style="margin-top: 20px;padding: 30px;" v-loading="loading !== true " v-if="active===1" :key="1">
        <b-form @keydown.enter.prevent @keyup.enter.prevent>
          <div class="question-bar">
            {{ $t('reset_password.label2') }}
          </div>
          <div class="question-title">{{ securityQuestion.question }}</div>
          <DialogSecurity></DialogSecurity>
          <div class="question-form">
            <input v-model="answer"
                   class="question-form-ipt"
                   type="text"/>
            <div class="question-form-btn">
              <div class="question-form-btn-back btn-common-shadow"
                   v-viewpoint.click="{ message: `[Transaction Withdrawal] Step2 [${selectedPaymentType.paymentMethods}] security question back.` }"
                   @click="back">{{ $t('sidebar.back') }}
              </div>
              <div class="question-form-btn-next btn-common-shadow" @click="verifySecurityQuestion">
                {{ $t('sidebar.next') }}
              </div>
            </div>
          </div>
          <!--          <b-form-group label-cols-sm="2" :label="$t('reset_password.label2')">-->
          <!--            <b-col style="font-weight: 1000;padding: 0">{{ securityQuestion.question }}</b-col>-->
          <!--          </b-form-group>-->
          <!--          <b-form-group label-cols-sm="2" :label="$t('reset_password.label3')">-->
          <!--            <b-form-input style="border-color: #DBDCFC" v-model="answer"-->
          <!--                          @keyup.enter="verifySecurityQuestion"></b-form-input>-->
          <!--            <DialogSecurity></DialogSecurity>-->
          <!--          </b-form-group>-->
          <!--          &lt;!&ndash;          <b-form-group>&ndash;&gt;-->
          <!--          <b-button :disabled="active===0 || active===3" style="margin-top: 12px; " variant="outline-primary"-->
          <!--                    @click="back">-->
          <!--            {{ $t('sidebar.back') }}-->
          <!--          </b-button>-->
          <!--          <b-button variant="primary" style=" margin-top: 12px;float: right" v-if="loading === true "-->
          <!--                    @click="verifySecurityQuestion">-->
          <!--            {{ $t('sidebar.next') }}-->
          <!--          </b-button>-->
          <!--          </b-form-group>-->
        </b-form>
      </div>

      <div v-loading="channelLoading !== true" v-if="active===2" :key="2">
        <div v-if="selectedPaymentType.paymentMethods=== 'JP Domestic Bank Wire' ||
                    selectedPaymentType.paymentMethods === '日本银行转账' ||
                    selectedPaymentType.paymentMethods === '日本円国内銀行送金' ||
                    selectedPaymentType.paymentMethods === '日本銀行轉帳' ||
                    selectedPaymentType.paymentMethods === 'เยน โอนเงินผ่านธนาคารในประเทศ' ||
                    selectedPaymentType.paymentMethods === 'Chuyển khoản ngân hàng Nhật Bản'">
          <JPDomesticBankWireForm :questionToken="questionToken" @flag="getFlag" ref="JPDomesticBankWireForm"
                                  :submit-method="submitWithdrawal"
                                  @submitMsg="getMsg"></JPDomesticBankWireForm>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === 'Credit Card' ||
                    selectedPaymentType.paymentMethods === '信用卡' ||
                    selectedPaymentType.paymentMethods === 'クレジットカード' ||
                    selectedPaymentType.paymentMethods === 'บัตรเครดิต' ||
                    selectedPaymentType.paymentMethods === 'Thẻ tín dụng'">
          <CreditCardForm :questionToken="questionToken" @flag="getFlag" ref="creditCardFromSubmit"
                          :submit-method="submitWithdrawal"
                          @submitMsg="getMsg"></CreditCardForm>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === 'International Bank Wire' ||
                    selectedPaymentType.paymentMethods === '国际银行汇款' ||
                    selectedPaymentType.paymentMethods === '国際銀行送金' ||
                    selectedPaymentType.paymentMethods === '國際銀行匯款' ||
                    selectedPaymentType.paymentMethods === 'การโอนเงินผ่านธนาคารระหว่างประเทศ' ||
                    selectedPaymentType.paymentMethods === 'Chuyển tiền ngân hàng quốc tế'">
          <BankWireForm :questionToken="questionToken" @flag="getFlag" ref="bankWireFromSubmit"
                        :submit-method="submitWithdrawal"
                        @submitMsg="getMsg"></BankWireForm>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === $t('transaction.withdrawal.withdrawalWay.bitwallet')">
          <BitWallet :questionToken="questionToken" @flag="getFlag" ref="bitWalletFromSubmit"
                     :submit-method="submitWithdrawal"
                     @submitMsg="getMsg"></BitWallet>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === 'Crypto Currency' ||
                    selectedPaymentType.paymentMethods === '加密货币' ||
                    selectedPaymentType.paymentMethods === '仮想通貨' ||
                    selectedPaymentType.paymentMethods === '虛擬貨幣' ||
                    selectedPaymentType.paymentMethods === 'สกุลเงินเสมือน' ||
                    selectedPaymentType.paymentMethods === 'Tiền ảo'">
          <CryptoCurrencyForm :questionToken="questionToken" @flag="getFlag" ref="cryptoCurrencyFromSubmit"
                              :submit-method="submitWithdrawal"
                              @submitMsg="getMsg"></CryptoCurrencyForm>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === $t('transaction.withdrawal.withdrawalWay.awe_pay')">
          <AWEPayForm :questionToken="questionToken" @flag="getFlag" ref="AWEPayFromSubmit"
                      :submit-method="submitWithdrawal"
                      @submitMsg="getMsg"></AWEPayForm>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === 'CNY Withdrawal' ||
                    selectedPaymentType.paymentMethods === '人民币出金' ||
                    selectedPaymentType.paymentMethods === '人民元の出金' ||
                    selectedPaymentType.paymentMethods === '人民幣出金' ||
                    selectedPaymentType.paymentMethods === 'การใช้จ่าย RMB' ||
                    selectedPaymentType.paymentMethods === 'Rút tiền CNY'">
          <CNYWithdrawalForm :questionToken="questionToken" @flag="getFlag" ref="CNYFromSubmit"
                             :submit-method="submitWithdrawal"
                             @submitMsg="getMsg"></CNYWithdrawalForm>
        </div>
        <div v-if="selectedPaymentType.paymentMethods === 'CNY Direct Transfer' ||
                    selectedPaymentType.paymentMethods === '人民币转账' ||
                    selectedPaymentType.paymentMethods === '中国元口座へ出金' ||
                    selectedPaymentType.paymentMethods === 'CNYDirectTransfer' ||
                    selectedPaymentType.paymentMethods === '人民幣轉帳' ||
                    selectedPaymentType.paymentMethods === 'ถอนเงินไปยังบัญชี CNY' ||
                    selectedPaymentType.paymentMethods === 'Chuyển tiền CNY'">
          <CNYDirectTransferForm :questionToken="questionToken" @flag="getFlag" ref="CNYDirectTransferFromSubmit"
                                 :submit-method="submitWithdrawal" @submitMsg="getMsg"></CNYDirectTransferForm>
        </div>

        <div v-if="['THB QR Payment', 'THB QR', 'THB Withdrawal', 'THB出金', 'การถอนเงินบาท', 'Rút THB', '泰铢出金', '泰銖出金'].includes(selectedPaymentType.paymentMethods)">
          <MonetixForm
            ref="MonetixFormSubmit"
            :questionToken="questionToken"
            @flag="getFlag"
            :submit-method="submitWithdrawal"
            @submitMsg="getMsg"
          />
        </div>

        <div style="padding: 0 20px 20px;">
          <b-button
            class="btn-common-shadow"
            :disabled="active===0 || active===3"
            style="margin-top: 12px; "
            variant="outline-primary"
            v-viewpoint.click="{ message: `[Transaction Withdrawal] Step3 [${selectedPaymentType.paymentMethods}] form back.` }"
            @click="back"
          >
            {{ $t('sidebar.back') }}
          </b-button>
          <b-button class="btn-common-shadow" variant="primary" style=" margin-top: 12px;float: right"
                    @click="submitWithdrawal">
            {{ $t('sidebar.submit') }}
          </b-button>
        </div>

      </div>

      <div v-if="active===3" :key="4">
        <el-result icon="success" :title="$t('DepositsSuccess')" subTitle="">
          <template slot="extra">
            <div class="table-success" v-if="list.length !== 0">
              <div class="table-content" v-for="item in list">
                <div class="label">{{ item.label }}</div>
                <div class="value" style="padding-left: 4px;">
                  <span v-if="item.value">{{ item.value }}</span>
                  <span v-else>--</span>
                </div>
              </div>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 30px">
              <b-button class="btn-common-shadow" variant="primary" @click="active=0" size="medium">
                {{ $t('sidebar.return') }}
              </b-button>
            </div>
          </template>
        </el-result>
      </div>
    </transition-group>
  </div>
</template>

<script>
import JPDomesticBankWireForm
  from '@/views/AccountTransactions/component/WithdrawalFormComponent/JPDomesticBankWireForm'
import CreditCardForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/CreditCardForm'
import BankWireForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/BankWireForm'
import BitWallet from '@/views/AccountTransactions/component/WithdrawalFormComponent/BitWallet'
import CryptoCurrencyForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/CryptoCurrencyForm'
import AWEPayForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/AWEPayForm'
import CNYWithdrawalForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/CNYWithdrawalForm'
import CNYDirectTransferForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/CNYDirectTransferForm'
import MonetixForm from '@/views/AccountTransactions/component/WithdrawalFormComponent/MonetixForm.vue'
import MyCard from '../../Card/MyCard'
import WithdrawalSelectors from '@/views/AccountTransactions/component/MobileComponent/WithdrawalSelectors'
import DialogSecurity from '@/components/MyfxComponent/DialogSecurity'
import { getLocal } from '@/Utils/authLocalStorage'

const {
  SecurityQuestionContent
} = require('../../../services/allExport.js')
export default {
  name: 'TradingAccountWithdrawal',
  components: {
    CryptoCurrencyForm,
    BitWallet,
    BankWireForm,
    CreditCardForm,
    JPDomesticBankWireForm,
    AWEPayForm,
    CNYWithdrawalForm,
    CNYDirectTransferForm,
    MyCard,
    WithdrawalSelectors,
    DialogSecurity,
    MonetixForm
  },
  data () {
    return {
      active: 0,
      answer: '',
      loading: false,
      bitWalletFlag: 0,
      securityQuestion: {},
      selectedPaymentType: {},
      tradingAccountList: {},
      channelLoading: true,
      countryLoading: false,
      country: '',
      nationality: '',
      fundingWay: {},
      list: [
        /* {
           label: 'mt4Login',
           value: '111',
         },
         {
           label: 'server',
           value: '111',
         },
         {
           label: 'theCurreny',
           value: '111',
         },
         {
           label: 'amount',
           value: '111',
         },
         {
           label: 'mt4Login',
           value: '111',
         },
         {
           label: 'mt4Login',
           value: '111',
         },
         {
           label: 'createTime',
           value: '111',
         }, */
      ],

      questionToken: ''
    }
  },
  computed: {
    withdrawalWayColumns () {
      return [
        {
          label: this.$t('transaction.withdrawal.withdrawal_way_columns.payment_methods'),
          key: 'paymentMethods',
          class: 'text-left'
        },
        {
          label: this.$t('transaction.withdrawal.withdrawal_way_columns.time_to_withdraw'),
          key: 'timeToWithdraw',
          class: 'text-left'
        },
        {
          label: this.$t('transaction.withdrawal.withdrawal_way_columns.fee'),
          key: 'fee',
          class: 'text-left'
        }
        // {
        //   label: this.$t('transaction.withdrawal.withdrawal_way_columns.operation'),
        //   key: 'operation',
        //   class: 'text-left'
        // }
      ]
    },
    withdrawalWay () {
      return [
        {
          key: 'bitwallet',
          paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.bitwallet'),
          timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.1_2Days'),
          fee: this.$t('transaction.withdrawal.withdrawalWay.fee2')
        },
        {
          key: 'crypto_currency',
          paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.crypto_currency'),
          timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.1_2Days'),
          fee: '3.5 ~ 30 USDT'
        },
        {
          key: 'international_bank_wire',
          paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.international_bank_wire'),
          timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.1_3Days'),
          fee: this.$t('transaction.withdrawal.withdrawalWay.fee1')
        }
      ]
    }
  },
  watch: {
    'withdrawalWayColumns' (newVal, oldVal) {
      if (newVal != oldVal) {
        this.getCountry()
      }
    }
  },
  created () {
    this.getCountry()
  },
  methods: {
    tableRowClick (data, idx, ev) {
      // this.$vp.add({ message: `[Deposits] Click ${ data.depositMethods }` })
      this.$vp.add({ message: `[Transaction Withdrawal Channel] Click [${ data.paymentMethods }] button.` })
      this.selectPaymentType(data)
    },

    getCountry () {
      this.countryLoading = true
      this.country = JSON.parse(getLocal('user')).country
      this.nationality = JSON.parse(getLocal('user')).nationality
      const jpBank = {
        key: 'jp_domestic_bank_wire',
        paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.jp_domestic_bank_wire'),
        timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.1_3Days'),
        fee: this.$t('transaction.withdrawal.withdrawalWay.fee1')
      }
      const creditCard = {
        key: 'credit_card',
        paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.credit_card'),
        timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.10days'),
        fee: this.$t('transaction.withdrawal.withdrawalWay.fee1')
      }
      const cnWithdraw = {
        key: 'cny_Withdrawal',
        paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.cny_Withdrawal'),
        timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.instant'),
        fee: this.$t('transaction.withdrawal.withdrawalWay.fee1')
      }
      const cnDirectTransfer = {
        key: 'cny_direct_transfer',
        paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.cny_direct_transfer'),
        timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.instant'),
        fee: this.$t('transaction.withdrawal.withdrawalWay.fee1')
      }
      const awePay = {
        key: 'awe_pay',
        paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.awe_pay'),
        timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.1_3Days'),
        fee: this.$t('transaction.withdrawal.withdrawalWay.fee1')
      }

      // 指定用户不可见 Credit
      const accountEmail = JSON.parse(getLocal('user')).email
      const assignAccountNotXCoinAndCredit = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
      if (!assignAccountNotXCoinAndCredit.includes(accountEmail)) {
        if (this.country.toLowerCase() === 'japan' || this.nationality.toLowerCase() === 'japan') {
          this.withdrawalWay.push(creditCard)
        }
      }


      // ---------------- THB QR Payment ----------------

      const THB_QR_Payment = {
        key: 'thb_qr_payment',
        paymentMethods: this.$t('transaction.withdrawal.withdrawalWay.monetix'),
        timeToWithdraw: this.$t('transaction.withdrawal.withdrawalWay.1Day'),
        fee: this.$t('transaction.withdrawal.withdrawalWay.fee2'),
      }

      if (this.country.toLowerCase() === 'thailand') {
        this.withdrawalWay.push(THB_QR_Payment)
      }

      // ---------------- THB QR Payment ----------------




      if (this.country.toLowerCase() == 'Japan'.toLowerCase()) {
        this.withdrawalWay.unshift(jpBank)
      } else if (this.country?.toLowerCase() == 'China'.toLowerCase()) {
        //暂时隐藏
        //this.withdrawalWay.push(cnWithdraw)
        this.withdrawalWay.push(cnDirectTransfer)
      } else if ((this.country?.toLowerCase() == 'Malaysia'.toLowerCase()) ||
        (this.country?.toLowerCase() == 'Thai'.toLowerCase()) ||
        (this.country?.toLowerCase() == 'Vietnam'.toLowerCase())) {
        this.withdrawalWay.push(awePay)
      }

      const query = [
        'jp_domestic_bank_wire', 'international_bank_wire', 'cny_Withdrawal', 'cny_direct_transfer', 'crypto_currency', 'credit_card', 'bitwallet', 'awe_pay'
      ]
      this.withdrawalWay.sort((a, b) => {
        return query.indexOf(a.key) - query.indexOf(b.key)
      })

      // 测试用
      // this.withdrawalWay.push(jpBank, cnWithdraw, cnDirectTransfer, awePay)
      this.countryLoading = false
    },
    getFlag (data) {
      this.bitWalletFlag = data
    },
    getMsg (data) {
      console.log('msg', data)
      this.list = data
    },
    getFundingWay (data) {
      this.selectPaymentType(data)
    },
    next () {
      if (this.active++ > 3) {
        this.active = 0
      }
    },
    back () {
      this.active--
      if (this.active === 0) {
        this.selectedPaymentType = {}
        this.$emit('paymentType', this.selectedPaymentType)
        this.answer = ''
      }
    },
    setIconClass (data) {
      let className = data.item.paymentMethods.replace(/\s*/g, '')
      if (className == '信用卡' || className == 'クレジットカード' || className == 'บัตรเครดิต' || className == 'Thẻ tín dụng'.replace(/\s*/g, '')) {
        className = 'CreditCard'
      }
      if (className == '国际银行汇款' || className == '国際銀行送金' || className == '國際銀行匯款' || className == 'การโอนเงินผ่านธนาคารระหว่างประเทศ' || className == 'Chuyển tiền ngân hàng quốc tế'.replace(/\s*/g, '')) {
        className = 'InternationalBankWire'
      }
      if (className == '加密货币' || className == '仮想通貨' || className == '虛擬貨幣' || className == 'สกุลเงินเสมือน' || className == 'Tiền ảo'.replace(/\s*/g, '')) {
        className = 'CryptoCurrency'
      }
      if (className == '日本银行转账' || className == '日本円国内銀行送金' || className == '日本銀行轉帳' || className == 'เยน โอนเงินผ่านธนาคารในประเทศ'.replace(/\s*/g, '') || className == 'Chuyển khoản ngân hàng Nhật Bản'.replace(/\s*/g, '')) {
        className = 'JPDomesticBankWire'
      }
      if (className == '人民币出金' || className == '人民元の出金' || className == '人民幣出金' || className == 'การใช้จ่าย RMB'.replace(/\s*/g, '') || className == 'Rút tiền CNY'.replace(/\s*/g, '')) {
        className = 'CNYWithdrawal'
      }
      if (className == '人民币转账' || className == '中国元口座へ出金' || className == 'CNYDirectTransfer' || className == '人民幣轉帳' || className == 'ถอนเงินไปยังบัญชี CNY'.replace(/\s*/g, '') || className == 'Chuyển tiền CNY'.replace(/\s*/g, '')) {
        className = 'CNYWithdrawal'
      }
      if (['THBQRPayment', 'THBQR', 'THBWithdrawal', 'THB出金', 'การถอนเงินบาท', 'RútTHB', '泰铢出金', '泰銖出金'].includes(className)) {
        className = 'monetix'
      }
      return 'el-icon-' + className
    },

    async selectPaymentType (data) {
      this.$vp.add({ message: `[Transaction Withdrawal Channel] Click [${ data.paymentMethods }] button.` })

      this.next()
      this.selectedPaymentType = data
      // console.log(this.selectedPaymentType)
      this.$emit('paymentType', this.selectedPaymentType)
      const myGuid = JSON.parse(getLocal('user')).myguid
      await SecurityQuestionContent.getQueryAll(myGuid).then(res => {
        this.securityQuestion = res.data
        this.loading = true
      })
    },
    checkloading () {
      if (this.bitWalletFlag == 200) {
        this.channelLoading = true
        this.bitWalletFlag = 0
        this.next()
      } else if (this.bitWalletFlag == 600) {
        this.$message({
          showClose: true,
          message: this.$t('transaction.withdrawal.balance_message'),
          type: 'warning'
        })
        this.channelLoading = true
        this.bitWalletFlag = 0
      } else if (this.bitWalletFlag == 500) {
        this.$message({
          showClose: true,
          message: this.$t('transaction.withdrawal.agreement'),
          type: 'warning'
        })
        this.channelLoading = true
        this.bitWalletFlag = 0
      } else {
        this.channelLoading = true
        this.bitWalletFlag = 0
      }
    },
    async verifySecurityQuestion () {
      this.$vp.add({ message: `[Transaction Withdrawal] Verify [${ this.selectedPaymentType.paymentMethods }] security question.` })

      const myGuid = JSON.parse(getLocal('user')).myguid
      this.loading = false
      await SecurityQuestionContent.questionVerification(myGuid, this.answer).then(async res => {
        this.questionToken = res.token
        this.answer = ''
        this.loading = true
        this.next()
      }).catch(error => {
        console.log(error)
        this.securityQuestion = error.data
        this.loading = true
      })
    },

    async submitWithdrawal () {
      this.$vp.add({ message: `[Transaction Withdrawal] Step3 [${ this.selectedPaymentType.paymentMethods }] form submit.` })

      this.channelLoading = false
      if (this.selectedPaymentType.paymentMethods === 'Bitwallet') {
        await this.$refs.bitWalletFromSubmit.submitFrom()
      } else if (this.selectedPaymentType.paymentMethods === 'International Bank Wire' ||
        this.selectedPaymentType.paymentMethods === '国际银行汇款' ||
        this.selectedPaymentType.paymentMethods === '国際銀行送金' ||
        this.selectedPaymentType.paymentMethods === '國際銀行匯款' ||
        this.selectedPaymentType.paymentMethods === 'การโอนเงินผ่านธนาคารระหว่างประเทศ' ||
        this.selectedPaymentType.paymentMethods === 'Chuyển tiền ngân hàng quốc tế') {
        // await this.$refs.bankWireFromSubmit.sumbitFrom()
        await this.$refs.bankWireFromSubmit.submitForm()
      } else if (this.selectedPaymentType.paymentMethods === 'Crypto Currency' ||
        this.selectedPaymentType.paymentMethods === '加密货币' ||
        this.selectedPaymentType.paymentMethods === '仮想通貨' ||
        this.selectedPaymentType.paymentMethods === '虛擬貨幣' ||
        this.selectedPaymentType.paymentMethods === 'สกุลเงินเสมือน' ||
        this.selectedPaymentType.paymentMethods === 'Tiền ảo') {
        await this.$refs.cryptoCurrencyFromSubmit.sumbitFrom()
      } else if (this.selectedPaymentType.paymentMethods === 'Credit Card' ||
        this.selectedPaymentType.paymentMethods === '信用卡' ||
        this.selectedPaymentType.paymentMethods === 'クレジットカード' ||
        this.selectedPaymentType.paymentMethods === 'บัตรเครดิต' ||
        this.selectedPaymentType.paymentMethods === 'Thẻ tín dụng') {
        // await this.$refs.creditCardFromSubmit.sumbitFrom()
        await this.$refs.creditCardFromSubmit.submitForm()
      } else if (this.selectedPaymentType.paymentMethods === 'JP Domestic Bank Wire' ||
        this.selectedPaymentType.paymentMethods === '日本银行转账' ||
        this.selectedPaymentType.paymentMethods === '日本円国内銀行送金' ||
        this.selectedPaymentType.paymentMethods === '日本銀行轉帳' ||
        this.selectedPaymentType.paymentMethods === 'เยน โอนเงินผ่านธนาคารในประเทศ' ||
        this.selectedPaymentType.paymentMethods === 'Chuyển khoản ngân hàng Nhật Bản') {
        // await this.$refs.JPDomesticBankWireForm.sumbitFrom()
        await this.$refs.JPDomesticBankWireForm.submitForm()
      } else if (this.selectedPaymentType.paymentMethods === 'AWE Pay') {
        await this.$refs.AWEPayFromSubmit.sumbitFrom()
      } else if (this.selectedPaymentType.paymentMethods === 'CNY Withdrawal' ||
        this.selectedPaymentType.paymentMethods === '人民币出金' ||
        this.selectedPaymentType.paymentMethods === '人民元の出金' ||
        this.selectedPaymentType.paymentMethods === '人民幣出金' ||
        this.selectedPaymentType.paymentMethods === 'การใช้จ่าย RMB' ||
        this.selectedPaymentType.paymentMethods === 'Rút tiền CNY') {
        // await this.$refs.CNYFromSubmit.sumbitFrom()
        await this.$refs.CNYFromSubmit.submitForm()
      } else if (this.selectedPaymentType.paymentMethods === 'CNY Direct Transfer' ||
        this.selectedPaymentType.paymentMethods === '人民币转账' ||
        this.selectedPaymentType.paymentMethods === '中国元口座へ出金' ||
        this.selectedPaymentType.paymentMethods === 'CNYDirectTransfer' ||
        this.selectedPaymentType.paymentMethods === '人民幣轉帳' ||
        this.selectedPaymentType.paymentMethods === 'ถอนเงินไปยังบัญชี CNY' ||
        this.selectedPaymentType.paymentMethods === 'Chuyển tiền CNY') {
        // await this.$refs.CNYDirectTransferFromSubmit.sumbitFrom()
        await this.$refs.CNYDirectTransferFromSubmit.submitForm()


      } else if (['THB QR Payment', 'THB QR', 'THB Withdrawal', 'THB出金', 'การถอนเงินบาท', 'Rút THB', '泰铢出金', '泰銖出金'].includes(this.selectedPaymentType.paymentMethods)) {
        await this.$refs.MonetixFormSubmit.submitForm()
      }
      this.checkloading()
    }

  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-step {

  .is-process {
    color: #495EEB;

    .el-step__icon {
      border-color: #495EEB;
    }
  }
}

.bTable {
  ::v-deep th {
    border-bottom: 1px solid #EBEEF5;
    color: rgb(73, 94, 235);
  }

  ::v-deep tbody {

    tr {
      position: relative;
      //border: 1px solid transparent;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        pointer-events: none;
      }

      &:hover::after {
        border: 2px solid #495EEB;
      }
    }
  }
}

.question {
  overflow: hidden;

  &-bar {
    padding: 0 30px;
    color: #495EEB;
    line-height: 50px;
    background-color: #EDF2FF;
  }

  &-title {
    margin-top: 30px;
    margin-left: 30px;
  }

  &-reset {
    margin-top: 10px;
    margin-left: 30px;
    cursor: pointer;
    color: #F88407;
    text-decoration: underline;
  }

  &-form {
    margin: 20px 30px 0;
    //width: 400px;

    &-ipt {
      padding: 0 10px;
      width: 100%;
      height: 40px;
      border-radius: 4px;
      border: 1px solid #DBDCFC;
    }

    &-btn {
      margin-top: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-back {
        cursor: pointer;
        padding: 4px 18px;
        color: #495EEB;
        border-radius: 8px;
        border: 1px solid #495EEB;
      }

      &-next {
        cursor: pointer;
        padding: 5px 18px;
        border-radius: 8px;
        color: #fff;
        background-color: #495EEB;
      }
    }
  }
}

.el-icon-JPDomesticBankWire {
  background: url(~@/assets/images/paymentway/icon_1.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-JPDomesticBankWire:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CNYWithdrawal {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-CNYWithdrawal:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-AWEPay {
  background: url(~@/assets/images/paymentway/awepay_logo.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-AWEPay:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CreditCard {
  background: url(~@/assets/images/paymentway/icon_2.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CreditCard:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-BankWire {
  background: url(~@/assets/images/paymentway/icon_3.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-BankWire:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-Bitwallet {
  background: url(~@/assets/images/paymentway/icon_4.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-Bitwallet:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CryptoCurrency {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CryptoCurrency:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-monetix {
  background: url(~@/assets/images/paymentway/icon_10.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-monetix:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.security-enter,
.security-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.security-enter-to,
.security-leave {
  opacity: 1;
}

.security-enter-active {
  transition: all 0.7s;
  transition-delay: 0.8s;
}

.security-leave-active {
  transition: all 0.7s;
}

.withdrawalWay-enter,
.withdrawalWay-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.withdrawalWay-enter-to,
.withdrawalWay-leave {
  opacity: 1;

}

.withdrawalWay-enter-active,
.withdrawalWay-leave-active {
  transition: all 0.8s;
}

.table-success {
  width: 400px;
  border: 1px solid #dddddd;
  background-color: #FFFFFF;

  .table-content {
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dddddd;
    text-align: left;

    .label {
      width: 140px;
      background-color: #495eeb;
      border-right: 1px solid #dddddd;
      color: #FFFFFF;
      padding: 6px 0 6px 5px;

      .value {
        flex: 1;
        padding: 6px 0 6px 5px;
        text-align: center;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

}

.table-button {
  transition: all .3s;
  background-color: #667aec;

  &:hover {
    box-shadow: -4px 4px 2px 0px rgba(73, 94, 235, .6);
    transform: translate(4px, -4px);
    background-color: #495eeb;
  }
}

@media screen and (max-width: 720px) {
  .table-success {
    width: 300px;
  }
}

.table-icon {
  display: flex;
  align-items: center;

  i {
    width: fit-content !important;
    font-size: 32px;

    &::before {
      font-size: 32px;
    }
  }
}

</style>
