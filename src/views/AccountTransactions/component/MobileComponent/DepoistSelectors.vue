<template>
  <div class="mobile-deposit">
    <!--    <div-->
    <!--      style="padding: 7px;background-color: rgba(193,208,240,0.67);border-radius: 10px;margin-bottom: 10px;width:100%;display:table;">-->
    <!--      <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
    <!--        <span>{{ $t('transaction.deposit.depositWayColumns.label1') }}</span>-->
    <!--      </div>-->
    <!--      <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
    <!--        <span>{{ $t('transaction.deposit.depositWayColumns.label3') }}</span>-->
    <!--      </div>-->
    <!--      <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
    <!--        <span>{{ $t('transaction.deposit.depositWayColumns.label4') }}</span>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div>-->
    <div
      v-for="(item, idx) in fundingWay"
      :key="idx"
      class="mobile-deposit-item"
      :style="item.maintenance ? 'pointer-events: none;cursor: not-allowed;background: #DCDCDC': ''"
      v-viewpoint.click="{ message: `[Deposits] Click ${item.depositMethods}` }"
      @click="next(item)"
    >
      <!--      <div class="deposit-card-container">-->
      <i
        :class="setIconClass(item)"
        :style="{ filter: (item.maintenance || !item.usable) ? `grayscale(1)` : '' }"
      ></i>

      <div style="flex: 1">
        <div style="color: #495DEC; font-size: 12px;">{{ $t('transaction.deposit.depositWayColumns.label2') }}:
          {{ item.status }}
          <el-tooltip
            placement="bottom" effect="light"
            v-if="item.dataBaseName === 'pcins' || item.dataBaseName === 'grandPay'"
          >
            <div style="width: 200px;" slot="content">
              {{ $t('transaction.deposit.credit_card.creditCardToolTip') }}
            </div>
            <i style="width: auto; height: auto; font-size: 16px;" class="far fa-question-circle" @click.stop></i>
          </el-tooltip>
        </div>
        <div
          style="margin-top: 2px;line-height: 1.5;display: flex; align-items: center;gap: 5px;width: 100%;text-align: center">
          <div style="flex: 1;flex-shrink: 0">{{ item.depositMethods }}</div>
          <div>|</div>
          <div style="flex: 1;flex-shrink: 0">{{ item.timeToFund }}</div>
          <div>|</div>
          <div style="flex: 1;flex-shrink: 0">{{ item.fee }}</div>
        </div>
      </div>

      <!--          <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
      <!--            <i :class="setIconClass(item)"></i>-->
      <!--            <span style="display: inline;">{{ item.depositMethods }}</span>-->
      <!--          </div>-->

      <!--          <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
      <!--            <span style="display: inline">{{ item.timeToFund }}</span>-->

      <!--            <el-tooltip placement="bottom" effect="light">-->
      <!--              <div style="width: 200px;" slot="content">-->
      <!--                {{ bitWalletTooltip }}-->
      <!--              </div>-->

      <!--              <i style="margin: 3px;color: #384bfa" class="far fa-question-circle"-->
      <!--                 v-if="item.depositMethods === 'bitwallet'"></i>-->
      <!--            </el-tooltip>-->

      <!--          </div>-->

      <!--          <div style="width:34%;display:table-cell;text-align: center;vertical-align: middle">-->
      <!--            <span style="display: inline">{{ item.fee }}</span>-->
      <!--          </div>-->

      <!--      </div>-->
    </div>
    <!--    </div>-->

  </div>

</template>

<script>
export default {
  name: 'DepositSelectors',
  props: {
    icon: String,
    fundingWay: Array,
    fundingWayColumn: Array
  },
  data () {
    return {
      selectedFundingWay: null
    }
  },
  computed: {
    bitWalletTooltip () {
      return this.$t('transaction.deposit.bitwallet.bitWalletTooltip')
    }
  },
  methods: {
    next (data) {
      if (data.maintenance || !data.usable) return
      this.$emit('next', data)
    },
    setIconClass (data) {
      let className
      if (data.depositMethods.replace(/\s*/g, '') == 'RMB2Funding') {
        className = 'RMB2Funding'
      } else {
        className = data.depositMethods.replace(/\s*/g, '')
        if (data.dataBaseName === 'pcins' || data.dataBaseName === 'grandPay') {
          className = 'CreditCard'
        }
        if (data.dataBaseName === 'none') {
          className = 'InternationalBankWire'
        }
        if (data.dataBaseName === 'usdt') {
          className = 'CryptoCurrency'
        }
        if (data.dataBaseName === 'jpbank') {
          className = 'JPYLocalBank'
        }
        if (data.dataBaseName === 'crypto356') {
          className = 'RMBFunding'
        }
        if (data.dataBaseName.startsWith('myPay')) {
          className = 'RMB2Funding'
        }
        if (className === 'CreditCard' && data.dataBaseName === 'xCoins') {
          className = 'xCoins'
        }
        if (className === 'ApplePay/GooglePay' && data.dataBaseName === 'xCoins') {
          className = 'xCoins1'
        }
        if (className === 'Neteller/Skrill' && data.dataBaseName === 'xCoins') {
          className = 'xCoins2'
        }
        if (data.dataBaseName === 'monetix') {
          className = 'monetix'
        }
        if (data.dataBaseName === 'monetix-MYR') {
          className = 'monetix-MYR'
        }
        if (data.dataBaseName.startsWith('awepay')) {
          className = 'Awepay'
        }
      }
      return 'el-icon-' + className
    }
  }
}
</script>

<style lang="scss">

//@media screen and (max-width: 1280px) {
//  .mobile-deposit-selector {
//    padding-right: 15px;
//    padding-left: 15px;
//  }
//}
//
//@media screen and (max-width: 980px) {
//  .mobile-deposit-selector {
//    padding-left: 10px;
//    padding-right: 15px;
//  }
//}
//
//@media screen and (max-width: 765px) {
//  .mobile-deposit-selector {
//    padding-left: 0;
//    padding-right: 5px;
//  }
//}

.mobile-deposit {
  display: flex;
  flex-direction: column;
  gap: 10px;

  &-item {
    gap: 10px;
    display: flex;
    align-items: center;
    padding: 4px 16px;
    min-height: 70px;
    cursor: pointer;
    transition: 0.3s;
    border-radius: 12px;
    border: 1px solid transparent;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

    &:hover {
      border: 1px solid #495EEB;
    }

    i {
      width: 30px;
      height: 30px;
    }

    &:hover {
      background-color: rgba(182, 198, 240, 0.67);
    }
  }
}

/* Add some padding inside the card container */
//.deposit-card-container {
//  padding: 2px 16px;
//  min-height: 70px;
//  width: 100%;
//  display: table;
//}

.el-icon-JPYLocalBank {
  background: url(~@/assets/images/paymentway/icon_1.png) center no-repeat; /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-JPYLocalBank:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-JPDomesticBankWire {
  background: url(~@/assets/images/paymentway/icon_1.png) center no-repeat;
  /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-JPDomesticBankWire:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CreditCard {
  background: url(~@/assets/images/paymentway/icon_2.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CreditCard:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-InternationalBankWire {
  background: url(~@/assets/images/paymentway/icon_3.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-InternationalBankWire:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-Bitwallet {
  background: url(~@/assets/images/paymentway/icon_4.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-Bitwallet:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-クレジットカード {
  background: url(~@/assets/images/paymentway/icon_2.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-クレジットカード:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CryptoCurrency {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CryptoCurrency:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CryptoFunding {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CryptoFunding:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-Crypto {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-Crypto:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-Awepay {
  background: url(~@/assets/images/paymentway/awepay_logo.png) no-repeat center;
  width: 100%;
  background-size: contain;
}

.el-icon-Awepay:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-RMBFunding {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-RMBFunding:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-人民币入金 {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-人民币入金:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-人民元の入金 {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-人民元の入金:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-RMB2Funding {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  font-size: 40px;
  background-size: contain;
}

.el-icon-RMB2Funding:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-人民币入金2 {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  font-size: 40px;
  background-size: contain;
}

.el-icon-人民币入金2:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-人民元の入金2 {
  background: url(~@/assets/images/paymentway/rmb.png) center no-repeat;
  width: 100%;
  font-size: 40px;
  background-size: contain;
}

.el-icon-人民元の入金2:before {
  content: "dog";
  visibility: hidden;
}

.el-icon-xCoins {
  background: url(~@/assets/images/paymentway/icon_7.svg) center no-repeat;
  width: 100%;
  background-size: 80% 80%;
  //background-size: contain;
}

.el-icon-xCoins:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-xCoins1 {
  background: url(~@/assets/images/paymentway/icon_8.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-xCoins1:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-xCoins2 {
  background: url(~@/assets/images/paymentway/icon_9.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-xCoins2:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-monetix {
  background: url(~@/assets/images/paymentway/icon_10.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-monetix:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-monetix-MYR {
  background: url(~@/assets/images/paymentway/monetix-MYR.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-monetix-MYR:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}
</style>
