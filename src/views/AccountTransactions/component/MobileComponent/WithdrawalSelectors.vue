<template>
  <div class="mobile-deposit">
    <!--    <div-->
    <!--      style="padding: 7px;background-color: rgba(193,208,240,0.67);border-radius: 10px;margin-bottom: 10px;width:100%;display:table;">-->
    <!--      <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
    <!--        <span>{{ $t('transaction.withdrawal.withdrawal_way_columns.payment_methods') }}</span>-->
    <!--      </div>-->
    <!--      <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
    <!--        <span>{{ $t('transaction.withdrawal.withdrawal_way_columns.time_to_withdraw') }}</span>-->
    <!--      </div>-->
    <!--      <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
    <!--        <span>{{ $t('transaction.withdrawal.withdrawal_way_columns.fee') }}</span>-->
    <!--      </div>-->
    <!--    </div>-->

    <div
      v-for="(item, idx) in fundingWay"
      :key="idx"
      class="mobile-deposit-item"
      @click="next(item)"
    >
      <i :class="setIconClass(item.paymentMethods)"></i>
      <div style="flex: 1">
        <div
          style="margin-top: 2px;line-height: 1.5;display: flex; align-items: center;gap: 5px;width: 100%;text-align: center">
          <div style="flex: 1;flex-shrink: 0">{{ item.paymentMethods }}</div>
          <div>|</div>
          <div style="flex: 1;flex-shrink: 0">{{ item.timeToWithdraw }}</div>
          <div>|</div>
          <div style="flex: 1;flex-shrink: 0">{{ item.fee }}</div>
        </div>
      </div>

      <!--        <div class="deposit-card-container">-->

      <!--          <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
      <!--            <i :class="setIconClass(item.paymentMethods)"></i>-->
      <!--            <span style="display: inline;">{{ item.paymentMethods }}</span>-->
      <!--          </div>-->

      <!--          <div style="width:33%;display:table-cell;text-align: center;vertical-align: middle">-->
      <!--            <span style="display: inline">{{ item.timeToWithdraw }}</span>-->
      <!--          </div>-->

      <!--          <div style="width:34%;display:table-cell;text-align: center;vertical-align: middle">-->
      <!--            <span style="display: inline">{{ item.fee }}</span>-->
      <!--          </div>-->

      <!--        </div>-->
    </div>
    <div style="height: 10px"></div>
  </div>

</template>

<script>
export default {
  name: 'WithdrawalSelectors',
  props: {
    icon: String,
    fundingWay: Array,
    fundingWayColumn: Array
  },


  methods: {
    next (data) {
      console.log(data)
      this.$emit('next', data)
    },
    setIconClass (data) {
      let className = data.replace(/\s*/g, '')
      if (className == '信用卡' || className == 'クレジットカ-ド' || className == 'บัตรเครดิต' || className == 'Thẻ tín dụng'.replace(/\s*/g, '')) {
        className = 'CreditCard'
      }
      if (className == '国际银行汇款' || className == '国際銀行送金' || className == '國際銀行匯款' || className == 'การโอนเงินผ่านธนาคารระหว่างประเทศ' || className == 'Chuyển tiền ngân hàng quốc tế'.replace(/\s*/g, '')) {
        className = 'InternationalBankWire'
      }
      if (className == '加密货币' || className == '仮想通貨' || className == '虛擬貨幣' || className == 'สกุลเงินเสมือน' || className == 'Tiền ảo'.replace(/\s*/g, '')) {
        className = 'CryptoCurrency'
      }
      if (className == '日本银行转账' || className == '日本円国内銀行送金' || className == '日本銀行轉帳' || className == 'เยน โอนเงินผ่านธนาคารในประเทศ'.replace(/\s*/g, '') || className == 'Chuyển khoản ngân hàng Nhật Bản'.replace(/\s*/g, '')) {
        className = 'JPDomesticBankWire'
      }
      if (className == '人民币出金' || className == '人民元の出金' || className == '人民幣出金' || className == 'การใช้จ่าย RMB'.replace(/\s*/g, '') || className == 'Rút tiền CNY'.replace(/\s*/g, '')) {
        className = 'CNYWithdrawal'
      }
      if (className == '人民币转账' || className == '中国元口座へ出金' || className == 'CNYDirectTransfer' || className == '人民幣轉帳' || className == 'ถอนเงินไปยังบัญชี CNY'.replace(/\s*/g, '') || className == 'Chuyển tiền CNY'.replace(/\s*/g, '')) {
        className = 'CNYWithdrawal'
      }
      if (['THBQRPayment', 'THBQR', 'THBWithdrawal', 'THB出金', 'การถอนเงินบาท', 'RútTHB', '泰铢出金', '泰銖出金'].includes(className)) {
        className = 'monetix'
      }
      return 'el-icon-' + className
    }
  }
}
</script>

<style lang="scss">

.mobile-deposit {
  margin: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  &-item {
    gap: 10px;
    display: flex;
    align-items: center;
    padding: 4px 16px;
    min-height: 70px;
    cursor: pointer;
    transition: 0.3s;
    border-radius: 12px;
    border: 1px solid transparent;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

    &:hover {
      border: 1px solid #495EEB;
    }

    i {
      width: 30px;
      height: 30px;
    }

    &:hover {
      background-color: rgba(182, 198, 240, 0.67);
    }
  }
}

//@media screen and (max-width: 1280px) {
//  .mobile-deposit-selector {
//    padding-right: 15px;
//    padding-left: 15px;
//  }
//}
//
//@media screen and (max-width: 980px) {
//  .mobile-deposit-selector {
//    padding-left: 10px;
//    padding-right: 15px;
//  }
//}
//
//@media screen and (max-width: 765px) {
//  .mobile-deposit-selector {
//    padding-left: 0;
//    padding-right: 5px;
//  }
//}
//
//.card-mobile {
//  /* Add shadows to create the "card" effect */
//  transition: 0.3s;
//  border-radius: 12px;
//  margin: 7px 10px;
//  cursor: pointer;
//  border: 1px solid transparent;
//  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
//
//  &:hover {
//    border: 1px solid #495EEB;
//  }
//}
//
///* On mouse-over, add a deeper shadow */
//.card-mobile:hover {
//  background-color: rgba(182, 198, 240, 0.67);
//}
//
///* Add some padding inside the card container */
//.deposit-card-container {
//  padding: 2px 16px;
//  min-height: 70px;
//  width: 100%;
//  display: table;
//}

.el-icon-JapaneseYenBankTransfer {
  background: url(~@/assets/images/paymentway/icon_1.png) center no-repeat; /*使用自己的图片来替换*/
  width: 100%;
  background-size: contain;
}

.el-icon-JapaneseYenBankTransfer:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CreditCard {
  background: url(~@/assets/images/paymentway/icon_2.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CreditCard:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-InternationalBankWire {
  background: url(~@/assets/images/paymentway/icon_3.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-InternationalBankWire:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-bitwallet {
  background: url(~@/assets/images/paymentway/icon_4.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-bitwallet:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-CryptoFunding {
  background: url(~@/assets/images/paymentway/icon_5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-CryptoFunding:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-monetix {
  background: url(~@/assets/images/paymentway/icon_10.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-monetix:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-AWEPay {
  background: url(~@/assets/images/paymentway/awepay_logo.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-AWEPay:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-RMBFunding {
  background: url(~@/assets/images/paymentway/USDT5.png) center no-repeat;
  width: 100%;
  background-size: contain;
}

.el-icon-RMBFunding:before {
  content: "dog";
  font-size: 40px;
  visibility: hidden;
}

.el-icon-RMB2Funding {
  background: url(~@/assets/images/paymentway/USDT5.png) center no-repeat;
  width: 100%;
  font-size: 40px;
  background-size: contain;
}

.el-icon-RMB2Funding:before {
  content: "dog";
  visibility: hidden;
}

</style>
