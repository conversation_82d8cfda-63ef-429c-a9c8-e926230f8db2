<template>
  <b-container fluid>
    <b-row>
      <b-col sm="12" md="12" lg="12" style="padding: 0">
        <div style="border: 1px solid #DBDCFC; padding: 30px">
          <div v-if="!isTransferSuccess">
            <ValidationObserver ref="form" v-slot="{ handleSubmit }">
              <b-form @submit.stop.prevent="handleSubmit(submitInternalTransactionRequest)">
                <div class="hidden-md-and-down">
                  <b-form-group
                    label-cols-sm="2"
                    :label="$t('transaction.transaction.label1')">
                    <b-form-row>
                      <el-col :span="11" style="flex: 1">
                        <ValidationProvider :name="$t('transaction.InternalFundTransfer.sourceAccount')"
                                            rules="selectionValidate" v-slot="{ errors }">
                          <b-select
                            style="border-color: #DBDCFC;height: 100%"
                            :disabled="submitLoading || marginLevelLoading || balanceLevelLoading"
                            ref="originSelection"
                            :options="originAccountList"
                            v-model="originSelection"
                            @change="originChange"
                          ></b-select>
                          <div class="validation">
                            <span>{{ errors[0] }}</span>
                          </div>
                        </ValidationProvider>
                      </el-col>
                      <i style="font-size: 30px; display: flex;align-items: center;" class="las la-caret-right"></i>
                      <el-col :span="11" style="flex: 1">
                        <ValidationProvider :name="$t('transaction.InternalFundTransfer.targetAccount')"
                                            rules="selectionValidate" v-slot="{ errors }">
                          <b-select
                            style="border-color: #DBDCFC;height: 100%"
                            :disabled="submitLoading"
                            ref="targetSelection"
                            :options="targetAccountList"
                            v-model="targetSelection"
                          ></b-select>
                          <div class="validation">
                            <span>{{ errors[0] }}</span>
                          </div>
                        </ValidationProvider>
                      </el-col>
                    </b-form-row>
                  </b-form-group>
                  <el-row style="margin-bottom: 20px">
                    <el-col :span="7">{{ $t('transaction.transaction.label2') }} {{ marginLevel }}
                      <i class="el-icon-loading" v-show="marginLevelLoading"></i>
                    </el-col>

                    <el-col :span="7">{{ $t('transaction.transaction.label3') }} {{ balance }} {{ originCurrency }}
                      <i class="el-icon-loading" v-show="balanceLevelLoading"></i>
                    </el-col>
                  </el-row>
                </div>

                <div class="hidden-lg-and-up">
                  <div class="form-group">
                    <label>{{ $t('transaction.transaction.label1') }}</label>
                    <b-row>
                      <b-col cols="12">
                        <ValidationProvider :name="$t('transaction.transaction.label1')" rules="selectionValidate"
                                            v-slot="{ errors }">
                          <b-select :disabled="submitLoading || marginLevelLoading || balanceLevelLoading"
                                    ref="originSelection"
                                    :options="originAccountList"
                                    v-model="originSelection"
                                    @change="originChange"
                          ></b-select>
                          <div class="validation">
                            <span>{{ errors[0] }}</span>
                          </div>
                        </ValidationProvider>
                      </b-col>
                      <b-col cols="12">
                        <div style=" display: flex;justify-content: center;align-items: center;">
                          <i style="font-size: 30px;" class="las la-caret-down"></i>
                        </div>
                      </b-col>
                      <b-col cols="12">
                        <ValidationProvider name="Target Account" rules="selectionValidate" v-slot="{ errors }">
                          <b-select :disabled="submitLoading" ref="targetSelection" :options="targetAccountList"
                                    v-model="targetSelection"></b-select>
                          <div class="validation">
                            <span>{{ errors[0] }}</span>
                          </div>
                        </ValidationProvider>
                      </b-col>
                    </b-row>
                    <b-row style="margin-bottom: 20px">
                      <b-col>{{ $t('transaction.transaction.label2') }} {{ marginLevel }}
                        <i class="el-icon-loading" v-show="marginLevelLoading"></i>
                      </b-col>

                      <b-col>{{ $t('transaction.transaction.label3') }} {{ balance }} {{ originCurrency }}
                        <i class="el-icon-loading" v-show="balanceLevelLoading"></i>
                      </b-col>
                    </b-row>
                  </div>
                </div>

                <ValidationProvider :name="$t('transaction.transaction.table.amount')" rules="required|twoDecimalPlaces"
                                    v-slot="{ errors }">
                  <b-form-group
                    label-cols-sm="2"
                    :label="$t('transaction.transaction.label4')">
                    <b-form-row style="margin: 0;">
                      <div style="position: relative; width: 100%; display: flex; gap: 10px; align-items: center;">
                        <div style="position: absolute; left: 10px">{{ originCurrency }}</div>
                        <b-input style="flex: 1; border: 1px solid #DBDCFC; padding-left: 45px"
                                 :disabled="submitLoading || balanceLevelLoading || !originAccount || balance<=0"
                                 v-model="amount">
                        </b-input>
                      </div>
                    </b-form-row>
                    <div class="validation">
                      <span>{{ errors[0] }}</span>
                    </div>
                    <div class="validation">
                      <span>{{ amountValidate }}</span>
                    </div>
                  </b-form-group>
                </ValidationProvider>
                <b-form-group
                  label-cols-sm="2"
                  :label="$t('transaction.transaction.label5')">
                  <b-form-textarea style="border: 1px solid #DBDCFC" :disabled="submitLoading" v-model="comment"
                                   rows="5"
                                   type="textarea"></b-form-textarea>
                </b-form-group>
                <div style="text-align: center">
                  <b-button
                    class="btn-common-shadow"
                    type="submit"
                    variant="primary"
                    :disabled="submitLoading"
                    v-viewpoint.click="{ message: `[Internal Fund Transfer] Click apply transfer` }">
                    {{ $t('transaction.InternalFundTransfer.apply') }}
                    <i
                      style="padding: 4px"
                      v-if="submitLoading"
                      class="el-icon-loading"
                    ></i>
                  </b-button>
                </div>
              </b-form>
            </ValidationObserver>
          </div>
          <div v-if="isTransferSuccess">
            <el-result v-if="isAutoTransfer===1" icon="success" :title="$t('transaction.transaction.title1')">
            </el-result>
            <el-result v-else icon="success"
                       :title="$t('transaction.transaction.test')">
            </el-result>
            <div style="text-align: center">
              <b-button
                variant="primary"
                size="medium"
                @click="isTransferSuccess=false"
                v-viewpoint.click="{ message: `[Internal Fund Transfer] Click back to form` }">{{ $t('sidebar.back') }}
              </b-button>
            </div>
          </div>
        </div>
      </b-col>
      <b-col v-if="false" sm="12" md="12" lg="3" class="right-tip">
        <iq-card>
          <template v-slot:headerTitle>
            {{ $t('transaction.transaction.tips.title') }}
          </template>
          <template v-slot:body>
            <div>
              <b-col>
                {{ $t('transaction.transaction.tips.test1') }}<span style="color: red;">250%. </span>
                {{ $t('transaction.transaction.tips.test2') }}
              </b-col>
              <b-col>
                {{ $t('transaction.transaction.tips.test3') }}
              </b-col>
              <b-col>
                {{ $t('transaction.transaction.tips.test4') }}
              </b-col>
            </div>
          </template>
        </iq-card>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>

import { getLocal } from '@/Utils/authLocalStorage'
import { fetchTradingAccountBalance, fetchTradingAccountMarginLevel } from '@/services/account'
import { Message } from 'element-ui'
import { submitInternalTransactionRequest } from '@/services/transactionHistory'

export default {
  name: 'InternalFundTransfer',
  data () {
    return {
      originAccount: '',
      originSelection: 0,
      originAccountList: [
        { value: 0, disabled: true, text: this.$t('transaction.InternalFundTransfer.select_01') }
      ],
      targetAccount: '',
      targetSelection: 0,
      targetAccountList: [
        { value: 0, disabled: true, text: this.$t('transaction.InternalFundTransfer.select_02') }
      ],
      defaultAccountList: [],
      walletList: [],
      balance: 0.00,
      marginLevel: '--',
      marginLevelLoading: false,
      balanceLevelLoading: false,
      amountValidate: '',
      amount: null,
      comment: '',
      submitLoading: false,
      resultDialogVisible: false,
      isAutoTransfer: 0,
      isTransferSuccess: false,
      originCurrency: ''
    }
  },
  watch: {
    originSelection (newVal, oldVal) {
      this.originAccount = this.setSelectedAccountNumber(newVal)
      let myguid = ''

      const originLogin = this.originAccount
      if (!originLogin.includes('MYFX Wallet')) {
        this.originCurrency = newVal.match(/\[(.*?)\]/)[1]
      }
      this.defaultAccountList.forEach(item => {
        if (item.login !== null) {
          if (item.login.toString() === originLogin) {
            myguid = item.myguid
            this.getAccountMarginLevel(myguid)
            this.getAccountBalance(myguid)
          }
          if (originLogin.includes('MYFX Wallet')) {
            this.walletList.forEach(item => {
              if (originLogin.includes(item.currency)) {
                this.balance = item.amount
                this.marginLevel = '--'
                this.originCurrency = item.currency
              }
            })
          }
        }
      })
    },
    targetSelection (newVal, oldVal) {
      this.targetAccount = this.setSelectedAccountNumber(newVal)
    },
    amount (newVal, oldVal) {
      if (newVal === '0') {
        this.amountValidate = this.$t('transaction.InternalFundTransfer.checkAmount')
      } else if (newVal > this.balance) {
        this.amountValidate = this.$t('transaction.InternalFundTransfer.checkBalance')
      } else {
        this.amountValidate = ''
      }
    }
  },
  created () {
    this.getAccountList()
  },
  methods: {

    /**
     * @description 禁用已选择的选项
     * <AUTHOR>
     */
    originChange (ev) {
      // 切换时清空marginLevel
      this.marginLevel = '--'

      if (ev === this.targetSelection) {
        // 选择和右侧账户一样时，清除右侧账户
        this.targetSelection = 0
      }

      this.targetAccountList.forEach(v => {
        if (v.value !== 0) {
          v.disabled = false
        }
      })
      const target = this.targetAccountList.find(v => v.value === ev)
      target && (target.disabled = true)
    },

    getAccountList () {
      let actType = ''
      let dActType = ''
      const data = this.$store.getters.commonGet(['tradingAccountList'])
      this.defaultAccountList = data
      data.forEach(item => {
        item.accountType = item.accountType || ''
        item.mt4group = item.mt4group || ''
        if (!(
          item.mt4group.toLowerCase().includes('disable') ||
          item.mt4group.includes('-PF') ||
          item.mt4group.toLowerCase().includes('rebate') ||
          item.accountType.toLowerCase().includes('ctrader'))) {
          actType = 'MT4-'
          dActType = 'mt4'
          if (item?.server?.toLowerCase()?.includes('mt5')) {
            actType = 'MT5-'
            dActType = 'mt5'
          }
          let data = ''
          if (!item.accountName.includes('MYFX Wallet')) {
            data = actType + item.login /* + '-'  + item.mt4group  */ + '-[' + item.currency + ']'
            //子户可以进行收账，子户不能转出
            if (!item.mt4group.toLowerCase().includes('-sub') && !item.mt4group.toLowerCase().includes('-client') && !item.mt4group.toLowerCase().includes('-clt')) {
              this.originAccountList.push({
                value: data,
                disabled: false,
                text: `${ data } - ${ item.accountName }`
              })
            }
            this.targetAccountList.push({ value: data, disabled: false, text: `${ data } - ${ item.accountName }` })
          } else {
            data = item.accountName + ' ' + item.currency
            this.originAccountList.push({ value: data, disabled: false, text: `${ data } - ${ item.accountName }` })
          }
        }
      })
      this.walletList = this.$store.getters.commonGet(['sumAccountList'])
      this.walletList.forEach(item => {
        this.originAccountList.push({
          value: 'MYFX Wallet' + ' ' + item.currency,
          disabled: false,
          text: this.$t('transaction.InternalFundTransfer.wallet') + ' ' + item.currency
        })
      })
    },
    setSelectedAccountNumber (selection) {
      if (selection.toLowerCase().includes('MYFX Wallet'.toLowerCase())) {
        return selection
      } else {
        const split = selection.split('-')
        return split[1]
      }
    },

    getAccountMarginLevel (accountMyguid) {
      this.marginLevelLoading = true
      fetchTradingAccountMarginLevel(accountMyguid).then(res => {
        let login = ''
        let type = ''
        let data = res.data
        if (data === 0 || isNaN(data) || data === null) {
          this.defaultAccountList.forEach(item => {
            if (item.myguid === accountMyguid) {
              login = item.login
            }
          })
          this.originAccountList.forEach(item => {
            const selectedAccountNumber = this.setSelectedAccountNumber(item)
            if (selectedAccountNumber === login) {
              const split = item.split('-')
              type = split[0]
            }
          })

          if (type.toLowerCase() === 'mt5') {
            data = '0'
          } else {
            data = '--'
          }
        }
        this.marginLevel = data.toString()
        this.marginLevelLoading = false
      }).catch(error => {
        this.marginLevelLoading = false
      })
    },
    getAccountBalance (accountMyguid) {
      this.balanceLevelLoading = true
      fetchTradingAccountBalance(accountMyguid).then(res => {
        this.balance = res.data.balance
        this.balanceLevelLoading = false
      }).catch(error => {
        this.balanceLevelLoading = false
      })
    },
    submitInternalTransactionRequest () {
      const myguid = JSON.parse(getLocal('user')).myguid

      if (this.originAccount === '' || this.originAccount === null) {
        Message.error(this.$t('transaction.InternalFundTransfer.error.error1'))
        return
      }

      if (this.targetAccount === '' || this.targetAccount === null) {
        Message.error(this.$t('transaction.InternalFundTransfer.error.error2'))
        return
      }

      if (this.amountValidate !== '') {
        return
      }
      if (this.originAccount === this.targetAccount) {
        Message.error(this.$t('transaction.InternalFundTransfer.error.error3'))
        return
      }
      let targetMyguid = ''
      let originMyguid = ''

      this.defaultAccountList.forEach(item => {
        if (item.login.toString() === this.originAccount) {
          originMyguid = item.myguid
        }
        if (item.login.toString() === this.targetAccount) {
          targetMyguid = item.myguid
        }
      })

      if (this.originAccount.toLowerCase().includes('MYFX Wallet'.toLowerCase())) {
        this.walletList.forEach(item => {
          if (this.originAccount.includes(item.currency)) {
            originMyguid = 'e-Wallet ' + item.currency
          }
        })
      }

      this.submitLoading = true
      submitInternalTransactionRequest({
        originMyguid,
        targetMyguid,
        amount: this.amount,
        comment: this.comment,
        myguid: myguid
      }).then(res => {
        this.isAutoTransfer = res.type
        this.submitLoading = false
        this.isTransferSuccess = true
        this.balance = '--'
        this.marginLevel = '--'
        // TODO: these variable cannot clear, please solve problems
        this.targetSelection = 0
        this.originSelection = 0

        this.amount = ''
        this.comment = ''
      }).catch(error => {
        this.submitLoading = false
      })
    }
  }
}
</script>

<style lang="scss">
.validation {
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #dc3545
}
</style>
