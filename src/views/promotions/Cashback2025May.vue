<script>
import {
  checkCashback2025May,
  getCashback2025MayAccount,
  getCashback2025MayJoinedAccount,
  joinCashback2025May
} from '@/services/promotion'
import {Message} from 'element-ui'

export default {
  name: 'Cashback2025May',
  data () {
    return {
      screenWidth: 0,
      cashback2025MayShow: false,
      tradingAccountList: [],
      accountNameLoginList: [],
      conditionCheck: false,
      joinLoading: false,
      welcomeLoading: false,
      joinedAccountList: [],
    }
  },
  async mounted () {
    this.screenWidth = window.innerWidth
    window.addEventListener('resize', () => {
      this.screenWidth = window.innerWidth
    })
    await this.getCashback2025MayJoinedAccount()
    await this.getTradingAccount()
  },
  methods: {
    showDialog () {
      if (new Date() < new Date('2025-06-30 23:59:59')) {
        this.cashback2025MayShow = true
        this.conditionCheck = false
      }
    },
    /**
     * @description 获取参加过cashback2025May的交易账号
     */
    async getCashback2025MayJoinedAccount () {
      try {
        this.welcomeLoading = true
        const { data } = await getCashback2025MayJoinedAccount()
        this.joinedAccountList = data
        this.welcomeLoading = false
      } catch (e) {
        console.log(e)
        this.welcomeLoading = false
      }
    },
    /**
     * @description 获取账户列表
     */
    async getTradingAccount () {
      this.accountNameLoginList = []
      const { data } = await getCashback2025MayAccount()
      data.forEach(item => {
        this.accountNameLoginList.push({
          label: item.login + '-' + item.accountName,
          value: item.myguid
        })
      })
    },
    /**
     *@description 参加活动
     */
    async joinNow () {
      try {
        this.joinLoading = true
        await joinCashback2025May({
          mt4AccountGuids: this.accountNameLoginList.map(item => item.value)
        })
        Message.success(this.$t('promotion.up600.button_ok'))
        await this.getCashback2025MayJoinedAccount()
        this.joinLoading = false
        this.cashback2025MayShow = false
        await this.getTradingAccount()
      } catch (e) {
        console.log(e)
        this.joinLoading = false
      }
    },
  }
}
</script>

<template>
  <div class="app-container" v-loading="welcomeLoading">
    <div class="left">
      <div class="left-cover">
        <div class="left-cover-img">
          <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/cashback2025May-ja_JP.png" alt="cashback2025May">
          <img v-else src="/img/promotion/cashback2025May-en_US.png"  alt="cashback2025May"/>
        </div>
      </div>
      <div class="left-info">
        <div style="font-weight: bold; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[0]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[1]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[2]') }}</div>
        <br/>
        <div style="font-weight: bold; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[3]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[4]') }}</div>
        <div v-html="$t('promotion.cashback2025May.info.desc[5]')"></div>
        <br/>
        <div style="font-weight: bold; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[6]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[7]') }}</div>
        <div v-html="$t('promotion.cashback2025May.info.desc[8]')"></div>
        <br/>
        <div style="font-weight: bold; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[9]') }}</div>
        <div v-html="$t('promotion.cashback2025May.info.desc[10]') "></div>
        <div>{{ $t('promotion.cashback2025May.info.desc[11]') }}</div>
        <br/>
        <div style="font-weight: bold; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[12]') }}</div>
        <ul style="list-style-type: disc; padding-left: 20px;">
          <li>{{ $t('promotion.cashback2025May.info.desc[13]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[14]') }}</li>
        </ul>
      </div>
    </div>
    <div class="right">
      <div class="right-notice" style="border-radius: 14px; overflow-y: auto">
        <div style="font-weight: bold; margin-top: 10px; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[15]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[16]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[17]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[18]') }}</div>
        <div>{{ $t('promotion.cashback2025May.info.desc[19]') }}</div>
      </div>

      <div class="right-info">
        <el-button class="right-button"
             :style="joinedAccountList.length > 0 ? {} : { background: '#01FF96', color: '#0d1350' } "
             @click="showDialog">
          {{ joinedAccountList.length > 0 ? $t('promotion.up600.button_ok') : $t('promotion.up600.button') }}
        </el-button>
        <div style="font-weight: bold; font-size: 16px;">{{ $t('promotion.cashback2025May.info.desc[20]') }}</div>
        <ul style="list-style-type: disc; padding-left: 20px;">
          <li>{{ $t('promotion.cashback2025May.info.desc[21]') }}</li>
        </ul>
        <ul style="list-style-type: circle; padding-left: 50px; margin: 0 0 10px">
          <li>{{ $t('promotion.cashback2025May.info.desc[22]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[23]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[24]') }}</li>
        </ul>
        <ul style="list-style-type: disc; padding-left: 20px;">
          <li>{{ $t('promotion.cashback2025May.info.desc[25]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[26]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[27]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[28]') }}</li>
          <li>{{ $t('promotion.cashback2025May.info.desc[29]') }}</li>
          <li v-html="$t('promotion.cashback2025May.info.desc[30]')"></li>
        </ul>
        <div v-html="$t('promotion.cashback2025May.info.desc[31]')"></div>

        <div v-if="joinedAccountList.length > 0">
          <div class="right-account">
            <div style="font-weight: bold">{{ $t('promotion.cashback2025May.info.account') }}</div>
            <div v-for="item in joinedAccountList">{{ item }}</div>
          </div>
          <div style="color: red;">{{ $t('promotion.cashback2025May.info.hint2') }}</div>
          <div style="color: red;">{{ $t('promotion.cashback2025May.info.hint3') }}</div>
        </div>
      </div>
    </div>

    <el-dialog :width="screenWidth > 720 ? '40%' : '85%'" top="15vh" :visible.sync="cashback2025MayShow">
      <div style="display: flex; flex-direction: column; gap: 30px;">
        <div v-if="accountNameLoginList.length > 0">
          <div style="margin-bottom: 10px">{{ $t('promotion.cashback2025May.selectLabel') }}</div>
          <ul style="list-style-type: disc; padding-left: 20px;">
            <li v-for="item in accountNameLoginList">{{ item.label }}</li>
          </ul>

          <el-checkbox v-model="conditionCheck" style="margin: 20px 0">
            <div v-html="$t('promotion.cashback2025May.info.agreeContent')"></div>
          </el-checkbox>
        </div>
        <el-empty :description="$t('promotion.cashback2025May.accountEmptyDesc')" v-else></el-empty>

        <div style="width: 95%; color: red; line-height: 1.5rem; word-break: break-word">{{ $t('promotion.cashback2025May.info.hint1') }}</div>
      </div>

      <div style="display: flex;justify-content: flex-end" slot="footer">
        <el-button
            size="small"
            type="primary"
            :disabled="!conditionCheck"
            :loading="joinLoading"
            @click="joinNow">
          {{ this.$t('promotion.up600.hint.confirm') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style scoped lang="scss">
.app-container {
  display: flex;
  gap: 30px;
  //align-items: flex-start;
  padding: 30px;
  margin: 0 14px 30px;
  min-height: 100%;
  color: black;
  background-color: #E9EBF8;

  .left,
  .right {
    flex: 1;
    flex-shrink: 0;
  }

  .left {
    display: flex;
    flex-direction: column;

    &-cover {

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 10px;
        aspect-ratio: 2 / 1;
      }
    }

    &-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-top: 30px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;

    &-notice {
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      min-height: 200px;
      //aspect-ratio: 2 / 1;
      //padding: 20px;
      //background: white;

      ul {
        margin-bottom: 0;
      }
    }

    &-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-top: 40px;

      .right-button {
        width: fit-content;
        color: #fff;
        background: #495EEB;
        padding: 16px 70px;
        margin: 0px auto 20px;
        border-radius: 30px;
        font-size: 16px;
      }

      .right-account {
        min-width: 230px;
        width: fit-content;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #495eeb;
        color: white;
        padding: 10px;
        margin: 20px auto;
        border-radius: 20px;
      }
    }

  }
}

.account-check-box {
  max-height: 600px;
  overflow-y: auto
}

@media screen and (max-width: 997px) {
  .app-container {
    flex-direction: column;

    .left,
    .right {
      flex: unset;
    }

    .right-notice {
      gap: 20px;
      aspect-ratio: unset !important;
      min-height: unset !important;
    }
  }

  .account-check-box {
    max-height: 220px;
    overflow-y: auto
  }
}
</style>
