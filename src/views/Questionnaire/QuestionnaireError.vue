<template>
  <div class="questionnaire">
    <div class="header">
      <img class="logo" src="@/assets/images/logo-full.png" alt="">
    </div>
    <div class="content" v-if="code">
      <div class="title" v-if="code == 5004 || code == 5005">
        System Message: Incomplete Form
      </div>
      <div class="title" v-if="code == 5006">
        System Message: Duplicate Registration Detected
      </div>
      <div class="title" v-if="code == 5007">
        System Message: Submission Error
      </div>
      <div class="img">
        <img src="@/assets/images/email/error.png" alt="">
      </div>
      <div class="content-body" v-if="code == 5004">
        <p> {{msg}} </p>
        <p>Please review the form and ensure all required fields are filled out.</p>
        <p>
          <el-button @click="$router.go(-1)" style="background-color: #495eeb; color: #FFFFFF" size="mini">Go back to form</el-button>
        </p>
      </div>
      <div class="content-body" v-if="code == 5005">
        <p>You do not have access in Client Office(CO).</p>
        <p style="margin-bottom: 0">Please create ourCO account from below link.</p>
        <p>
          <a href="https://myfxmarkets.com/open-live-forex-account" target="_blank">
            Open new CO Account from here
          </a>
        </p>
      </div>
      <div class="content-body" v-if="code == 5006">
        <p>Thank you for your interest in our platform!</p>
        <p>We noticed that you have already registered, and we're currently processing your application.</p>
        <p>
          If you have any questions about your registration status or need to make changes, please contact our support team at
          <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>
      </div>
      <div class="content-body" v-if="code == 5007">
        <p>We apologize, but there was an issue saving your information to our database.</p>
        <p>
          Please try again later. If the problem persists, contact our support team at
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
      </div>
      <div v-if="code == 5004 || code == 5005">
        <p style="padding: 0 20px">
          If you need assistance, feel free to contact our support team at
          <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>
      </div>
    </div>
    <div class="bg"></div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      code: this.$route.query.code,
      msg: this.$route.query.content //code=5004
    }
  },
  created() {
    this.getMsg()
  },
  methods: {
    getMsg() {
      console.log(this.$route.query)
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  .bg {
    height: 120px;
    width: 100%;
    position: absolute;
    background: linear-gradient(to bottom, #c6c6c6, transparent);
    top: 0;
  }
  .header {
    height: 120px;
    line-height: 160px;
    z-index: 2;
    .logo {
      width: 200px;
      height: 65px;
    }
  }
  .content {
    width: 480px;
    border: 1px solid #c3c3c3;
    border-radius: 7px;
    margin-top: 80px;
    padding-bottom: 30px;
    text-align: center;
    .title {
      border-bottom: 2px solid #c3c3c3;
      padding: 10px 20px;
      font-size: 20px;
      font-weight: 500;
    }
    .img {
      margin: 16px 0;
    }
    .content-body {
      padding: 0 20px;
      color: #494848;
    }
  }

}
@media screen and (max-width: 720px) {
  .questionnaire {
    .content {
      width: 340px;
      margin-top: 40px;
      .title {
        font-size: 16px;
      }
      .content-body {
        padding: 0 10px;
      }
    }
  }
}
</style>
