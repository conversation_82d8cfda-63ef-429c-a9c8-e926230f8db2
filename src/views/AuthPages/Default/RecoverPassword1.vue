<template>
  <div>
    <router-link to="/auth/sign-in1">
      <i style="font-size: 30px;margin-bottom: 20px" class="fas fa-arrow-circle-left"></i>
    </router-link>
    <h1>{{ $t('login.reset_password') }}</h1>
    <transition-group name="sendemail">
      <div v-if="active === 0" :key="1">
        <p>{{ $t('login.reset_password_text') }}</p>
        <ValidationObserver ref="form" v-slot="{ handleSubmit }">
          <form class="mt-4" novalidate @submit.prevent="handleSubmit(submit)">
            <ValidationProvider name="login.email" rules="required|email" v-slot="{ errors }">
              <div class="form-group">
                <label for="exampleInputEmail1">{{ $t('login.email') }}</label>
                <input type="email" v-model="email"
                       :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')" id="exampleInputEmail1"
                       aria-describedby="emailHelp" :placeholder="$t('login.enter_email')">
                <div class="validation">
                  <span>{{ errors[0] }}</span>
                </div>
              </div>
            </ValidationProvider>

            <div v-if="googleRecaptchaVisible" id="google-captcha">
              <GoogleRecaptcha ref="recaptcha" @getValidateCode="clickGoogleCaptcha"
                               :captchaType="captchaType"
                               key="6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_"></GoogleRecaptcha>
            </div>
            <ValidationProvider v-else vid="captcha" name="login.check_code" rules="required" v-slot="{ errors }">
              <div class="form-group">
                <label for="captchaInput">{{ $t('login.check_code') }}</label>
                <b-row>
                  <b-col>
                    <input v-bind:disabled="submitLoading" id="captchaInput"
                           :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                           style="width: 100%;float: left"
                           v-model="code"
                           :placeholder="$t('login.check_code') "
                           required>
                    <div class="validation">
                      <span>{{ errors[0] }}</span>
                    </div>
                  </b-col>
                  <b-col>
                    <el-image :fit="codePictureStyle"
                              :src="codePicture"
                              @click="getCheckCode"
                              v-loading="captchaLoading"
                              style="float: right"
                              element-loading-spinner="el-icon-loading"></el-image>

                  </b-col>

                </b-row>

              </div>
            </ValidationProvider>
            <div class="d-inline-block w-100">
              <button type="submit" :disabled="submitLoading" class="btn btn-primary float-left btn-common-shadow">
                {{ $t('login.reset_password_confirm') }}
              </button>
            </div>
          </form>
        </ValidationObserver>
      </div>
      <div v-if="active === 1" :key="2">
        <p>{{ $t('login.reset_password_text_code') }}</p>
        <ValidationObserver ref="form" v-slot="{ handleSubmit }">
          <form class="mt-4" novalidate @submit.prevent="handleSubmit(verify)">
            <ValidationProvider name="login.enter_check_code" rules="required" v-slot="{ errors }">
              <div class="form-group">
                <el-row>
                  <el-col>
                    <el-input v-model="checkCode" style="width: 70%;" size="big" id="checkCode"
                              :placeholder="$t('login.enter_check_code') ">
                      <el-button :loading="submitLoading" @click="reSubmitEmailRequest" :disabled="!sendButtonVisible"
                                 slot="append">
                        <!--                        -->
                        <span v-if="countdown !== 0">{{ countdown }}</span>
                        <span v-if="countdown === 0">{{
                            isFirst ? $t('login.reset_password_send') : $t('login.reset_password_resend')
                          }}</span>
                      </el-button>
                    </el-input>
                  </el-col>
                </el-row>

                <div class="validation">
                  <span>{{ errors[0] }}</span>
                </div>
              </div>
              <div class="d-inline-block w-100">
                <button type="submit" :disabled="submitVerifyCodeLoading" class="btn btn-primary float-left btn-common-shadow"><i
                  style="padding: 0;margin-left: 5px"
                  v-if="submitVerifyCodeLoading"
                  class="el-icon-loading"></i>{{
                    $t('login.reset_password_submit')
                  }}
                </button>
              </div>
            </ValidationProvider>
          </form>
        </ValidationObserver>
      </div>
      <div v-if="active === 2" :key="3">
        <p>{{ $t('login.Enter_the_new_password') }}</p>
        <ValidationObserver ref="form" v-slot="{ handleSubmit }">
          <form class="mt-4" novalidate @submit.prevent="handleSubmit(submitNewPassword)">
            <ValidationProvider :name="$t('login.New_Password')" rules="required||newPassword" v-slot="{ errors }">
              <b-form-group
                class="col-sm-7"
                label-cols-sm="0">
                <label>{{ $t('login.New_Password') }}</label>
                <b-form-input style="width: 100%" type="password" v-model="newPassword.newPassword"></b-form-input>
                <div class="validation">
                  <span>{{ errors[0] }}</span>
                </div>
              </b-form-group>
              <b-form-group
                class="col-sm-7"
                label=""
                label-cols-sm="0">
                <div>
                  <b-checkbox :disabled="true" v-for="item in validateSelection" v-model="item.checked"
                              :key="item.index">
                    {{ item.name }}
                  </b-checkbox>
                </div>
              </b-form-group>
            </ValidationProvider>
            <ValidationProvider :name="$t('login.New_Password_Confirm')" rules="required" v-slot="{ errors }">
              <b-form-group
                class="col-sm-7"
                label-cols-sm="0">
                <label>{{ $t('login.New_Password_Confirm') }}</label>
                <b-form-input style="width: 100%" type="password"
                              v-model="newPassword.newPasswordConfirm"></b-form-input>
                <div class="validation">
                  <span>{{ errors[0] }}</span>
                </div>
                <div v-if="inconsistency" class="validation">
                  <span>{{ $t('login.Password_inconsistency') }}</span>
                </div>
              </b-form-group>
            </ValidationProvider>
            <b-button class="btn-common-shadow" :disabled="submitPasswordLoading" variant="primary" type="submit" style="margin-top: 12px;">
              {{ $t('login.reset_password_active2.submit') }}
              <i v-if="submitPasswordLoading"
                 style="padding: 0;margin-left: 5px"
                 class="el-icon-loading"/>
            </b-button>
          </form>
        </ValidationObserver>
      </div>
    </transition-group>
  </div>
</template>
<script>
import auth from '@/services/auth'
import { Message } from 'element-ui'
import { mapGetters } from 'vuex'
import GoogleRecaptcha from '@/components/MyfxComponent/GoogleRecaptcha'
import Transaction from '@/views/AccountTransactions/Transaction'
import {
  reSendForgetPasswordEmailCode,
  sendForgetPasswordEmailCode,
  startForgetPassword,
  verifyForgetPasswordEmailCode
} from '@/services/account'
import { extend } from 'vee-validate'
import { passwordValid } from '@/Utils/NewPasswordValidater'
import AESCrypto from '@/Utils/AESCrypto'
import i18n from '@/i18n'


let npwd = ''
extend('newPassword', {
  validate (value) {
    npwd = value
    const validated = passwordValid(value)
    return validated.length === 5
  },
  message() {
    if (!/^[\w!@#.]+$/.test(npwd)) return i18n.t('accountRegister.checkbox_5_no_html')
    return i18n.t('login.Insufficient_password_complexity')
  }
})

export default {
  name: 'RecoverPassword1',
  components: {
    Transaction,
    GoogleRecaptcha
  },
  data () {
    return {
      email: '',
      googleRecaptchaVisible: false,
      code: '',
      codePictureStyle: 'contain',
      codePicture: '',
      active: 0,
      checkCode: '',
      captchaLoading: false,
      submitLoading: false,
      submitVerifyCodeLoading: false,
      sendButtonVisible: true,
      countdown: 0,
      // 是否首次发送
      isFirst: true,
      timer: null,
      inconsistency: false,
      submitPasswordLoading: false,
      captchaType: 'cloudflare', // 默认使用cloudflare
      newPassword: {
        newPassword: '',
        newPasswordConfirm: '',
        checkCode: ''
      },
      validateSelection: [
        {
          index: 0,
          name: this.$t('reset_password.eight_character'),
          checked: false
        },
        {
          index: 1,
          name: this.$t('reset_password.one_number'),
          checked: false
        },
        {
          index: 2,
          name: this.$t('reset_password.one_lowerCase'),
          checked: false
        },
        {
          index: 3,
          name: this.$t('reset_password.one_upperCase'),
          checked: false
        },
        // {
        //   index: 4,
        //   name: this.$t('accountRegister.checkbox_5'),
        //   checked: false
        // }
      ],

      // 邮箱验证 ticket
      ticket: ''
    }
  },
  watch: {
    'newPassword.newPassword': {
      handler (newVal, oldVal) {
        this.validateNewPassword = passwordValid(newVal)
        this.validateSelection.forEach(item => {
          item.checked = this.validateNewPassword.includes(item.name)
        })
      }
    },
    'newPassword.newPasswordConfirm': {
      handler (newVal, oldVal) {
        this.inconsistency = !(newVal === this.newPassword.newPassword && newVal !== null)
      }
    }
  },
  computed: {
    ...mapGetters({
      stateCaptcha: 'Setting/captchaState'
    })
  },
  created () {
    this.getCheckCode()
  },
  methods: {
    getCheckCode () {
      this.captchaLoading = true
      this.googleRecaptchaVisible = false
      this.$store.dispatch('Setting/authCaptchaAction', {
        codeImage: '',
        token: ''
      })
      auth.getCaptcha().then(res => {
        // 开发环境使用cloudflare
        if (process.env.NODE_ENV === 'development') {
          this.captchaType = 'cloudflare'
          this.googleRecaptchaVisible = true
          return
        }

        // 生产环境根据后端返回的type选择
        if (res.type === 'googleCaptcha') {
          this.captchaType = 'googleCaptcha'
          this.googleRecaptchaVisible = true
          return
        } else {
          this.captchaType = 'cloudflare'
          this.googleRecaptchaVisible = true
          return
        }

        // 兼容旧的图片验证码逻辑（如果后端没有返回type或者返回其他值）
        const codeData = res.data
        if (!codeData) {
          Message.error('Error: Get check code error!')
          return
        }
        this.$store.dispatch('Setting/authCaptchaAction', {
          codeImage: codeData.codeImage,
          token: codeData.token
        })
        this.codePicture = this.stateCaptcha.codeImage
        this.captchaLoading = false
      })
    },
    clickGoogleCaptcha (val) {
      this.$store.dispatch('Setting/authCaptchaAction', {
        codeImage: '',
        token: val
      })
    },
    async submitNewPassword () {
      if (this.inconsistency) {
        return
      }
      this.submitPasswordLoading = true
      const newPassword = AESCrypto.Encrypt(this.newPassword.newPassword)
      const email = this.email
      await startForgetPassword({
        newPassword: newPassword,
        email: email
      }, this.ticket)
        .then(res => {
          Message.success(this.$t('login.reset_password_active2.submitSuccess'))
          this.$router.push('/auth/sign-in1')
        }).catch(error => {
          this.ticket = ''
          this.active = 0
          this.submitPasswordLoading = false
        })
    },
    submit () {
      if (this.sendEmail()) {
        console.log(this.email)
      }
    },
    async sendEmail () {
      this.sendButtonVisible = true
      this.submitLoading = true
      const token = this.stateCaptcha.token
      if (!this.stateCaptcha.token) {
        Message.error('Please verify your identity')
        return
      }
      await sendForgetPasswordEmailCode(this.email, token, this.code).then(async res => {
        // Message.success(res.msg)
        this.startCountdown()
        this.sendButtonVisible = false
        this.submitLoading = false
        setTimeout(() => {
          this.next()
          this.isFirst = false
        }, 540)
        return true
      }).catch(async error => {
        this.getCheckCode()
        this.sendButtonVisible = false
        this.submitLoading = false
        return false
      })
    },
    reSubmitEmailRequest () {
      console.log('ssssssssssssssssss')
      this.sendButtonVisible = true
      this.submitLoading = true
      const token = this.stateCaptcha.token
      if (!this.stateCaptcha.token) {
        Message.error('Please verify your identity')
        return
      }
      reSendForgetPasswordEmailCode(this.email, token, this.code).then(async res => {
        Message.success(this.$t('login.resendCode'))
        this.startCountdown()
        this.sendButtonVisible = false
        this.submitLoading = false
        return true
      }).catch(async error => {
        this.startCountdown()
        this.sendButtonVisible = false
        this.submitLoading = false
        return false
      })
    },


    // 验证邮箱验证码
    async verify () {
      this.submitVerifyCodeLoading = true
      await verifyForgetPasswordEmailCode(this.email, this.checkCode).then(res => {
        Message.success(res.msg)

        this.ticket = res.data

        this.submitVerifyCodeLoading = false
        setTimeout(() => {
          this.next()
        }, 540)
      }).catch(error => {
        this.submitVerifyCodeLoading = false
      })
    },
    next () {
      const temp = this.active
      this.active = -1
      this.active = temp
      this.active++// 方法区
    },
    startCountdown () {
      this.countdown = 60 // 设置倒计时时间
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown === 0) {
          this.stopCountdown()
        }
      }, 1000) // 每秒更新倒计时
    },
    stopCountdown () {
      clearInterval(this.timer)
      this.countdown = 0
      this.sendButtonVisible = true
    }
  }
}
</script>

<style lang="scss">

.validation {
  width: 100%;
  float: right;
  margin-top: .25rem;
  font-size: 80%;
  color: #dc3545
}

.sendemail-enter,
.sendemail-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.sendemail-enter-to,
.sendemail-leave {
  opacity: 1;

}

.sendemail-enter-active {
  transition: all 0.7s;
  transition-delay: 0.8s;
}

.sendemail-leave-active {
  transition: all 0.7s;
}
</style>
