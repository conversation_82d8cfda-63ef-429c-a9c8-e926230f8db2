<template>
  <ValidationObserver ref="form" v-slot="{ handleSubmit }">
    <form novalidate @submit.prevent="handleSubmit(onSubmit)">
      <ValidationProvider vid="email" name="login.email" rules="required|email" v-slot="{ errors }">
        <div class="form-group">
          <label for="emailInput">{{ $t('login.email') }}</label>
          <input v-bind:disabled="loginLoading" type="email"
                 :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                 id="emailInput" aria-describedby="emailHelp"
                 v-model="user.username" required>
          <div class="invalid-feedback">
            <span>{{ errors[0] }}</span>
          </div>
        </div>
      </ValidationProvider>
      <ValidationProvider vid="password" name="login.password" rules="required" v-slot="{ errors }">
        <div class="form-group">
          <label for="passwordInput">{{ $t('login.password') }}</label>
          <router-link to="/auth/password-reset1" class="float-right">
            <span style="font-size: 12px;">{{ $t('login.forgot_password') }}</span>
          </router-link>
          <div class="pwd-box">
            <input v-bind:disabled="loginLoading" :type="pwdShow ? 'text' : 'password' "
                   :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                   id="passwordInput"
                   v-model="user.password" required />

            <i class="el-icon-view" @click="pwdShow = !pwdShow"></i>
          </div>
          <div class="invalid-feedback">
            <span>{{ errors[0] }}</span>
          </div>
        </div>
      </ValidationProvider>
      <div v-if="googleRecaptchaVisible" id="google-captcha">
        <GoogleRecaptcha ref="recaptcha" @getValidateCode="clickGoogleCaptcha"
                         :captchaType="captchaType"
                         key="6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_"></GoogleRecaptcha>
      </div>
      <ValidationProvider v-else vid="captcha" name="login.check_code" rules="required" v-slot="{ errors }">
        <div class="form-group">
          <label for="captchaInput">{{ $t('login.check_code') }}</label>
          <div>
            <b-row>
              <b-col>
                <input v-bind:disabled="loginLoading" id="captchaInput"
                       :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                       style="width: 100%;float: left"
                       v-model="user.code" required>
              </b-col>
              <b-col>
                <el-image :fit="codePictureStyle"
                          :src="codePicture"
                          @click="getCheckCode"
                          class="captchaPicture"
                          v-loading="captchaLoading"
                          element-loading-spinner="el-icon-loading"></el-image>
              </b-col>
            </b-row>
            <div class="invalid-feedback">
              <span>{{ errors[0] }}</span>
            </div>
          </div>
        </div>

      </ValidationProvider>

      <div class="btn2">
        <button type="submit" class="btn btn-primary">
          {{ $t('login.sigin') }}
          <i v-if="loginLoading"
             style="padding: 0;margin-left: 5px"
             class="el-icon-loading"
          ></i>
        </button>
      </div>

      <div class="w-100 btns">
        <div class="btn1">
          <!--          <div><a :href="`https://cco.myfxmarkets.com/${lang}`" target="_blank"-->
          <!--                  style="text-decoration: underline">{{ $t('loginBtn.btn1') }}</a></div>-->
          <div><a href="https://myfxmarkets.com/ja/open-live-forex-account" target="_blank"
                  style="text-decoration: underline">{{ $t('loginBtn.btn2') }}</a></div>
        </div>

        <!--        <div class="custom-control custom-checkbox d-inline-block mt-2 pt-1">
                  <input type="checkbox" class="custom-control-input" :id="formType">
                  <label class="custom-control-label" :for="formType">Remember Me</label>
                </div>-->


      </div>
      <!--      <div class="sign-info">
                <span class="dark-color d-inline-block line-height-2">
                  Don't have an account?
                              <router-link to="/auth/sign-up1" class="iq-waves-effect pr-4">
                                Sign up
                              </router-link>
                </span>
               <social-login-form></social-login-form>   TODO 暂时隐藏
            </div>-->

    </form>

  </ValidationObserver>
</template>

<script>
import auth from '../../../../services/auth'
import { mapGetters } from 'vuex'
import { setLocal, getLocal } from '@/Utils/authLocalStorage'
import GoogleRecaptcha from '@/components/MyfxComponent/GoogleRecaptcha'
import i18n from '@/i18n'
import { LPOACheck } from '@/services/LPOAUpdate'

export default {
  name: 'SignIn1Form',
  components: {
    GoogleRecaptcha
  },
  props: ['formType', 'username', 'password', 'code'],
  data: () => ({
    user: {
      username: '',
      password: '',
      code: '',
      token: ''
    },
    codePicture: '',
    codePictureStyle: 'contain',
    loginLoading: false,
    captchaLoading: false,
    googleRecaptchaVisible: false,
    dialogVisible: false,
    pwdShow: false
  }),
  mounted () {
    this.user.username = this.$props.username
    this.user.password = this.$props.password
    this.user.code = this.$props.code
  },
  created () {
    // location.reload()
    this.clickCodeImage()
  },
  computed: {
    ...mapGetters({
      stateUsers: 'Setting/usersState',
      stateCaptcha: 'Setting/captchaState'
    }),
    lang () {
      const language = i18n.locale
      switch (language) {
        case 'zh':
        case 'zh_CN':
          return 'zh'
        case 'ja':
        case 'ja_JP':
          return 'jp'
        default:
          return ''
      }
    }
  },
  methods: {
    // LPOA检测
    async LPOACheck () {
      try {
        if (!localStorage.user) return
        const { lpoaChecked } = await LPOACheck({ coguid: JSON.parse(getLocal('user')).myguid })
        if (lpoaChecked === 1) {
          // 暂不显示
          // this.$store.commit('Setting/setLPOAUpdateShow', true)
        }
      } catch (err) {
        console.log(err)
      }
    },

    onSubmit () {
      if (this.formType === 'passport') {
        this.passportLogin()
      } else if (this.formType === 'jwt') {
        this.login()
      }

      this.$versionPrompt()
    },

    login () {
      this.loginLoading = true
      this.user.token = this.stateCaptcha.token
      if (!this.stateCaptcha.token) {
        this.$message.error('Please verify your identity')
        this.loginLoading = false
        return
      }
      const user = this.user
      auth.login(user).then(res => {
        if (!(res.code === 200)) {
          this.$message.error(res.msg)
          this.getCheckCode()
          return
        }
        localStorage.HaveSecurityQuestion = res.data.haveSecurityQuestion
        const userInfo = res.data.accountEntity
        if (userInfo) {
          this.$store.dispatch('Setting/addUserAction', {
            user: {
              country: userInfo.country,
              firstname: userInfo.firstname,
              gender: userInfo.gender,
              city: userInfo.city,
              ibcode: userInfo.ibcode,
              accounttype: userInfo.accounttype,
              parentib: userInfo.parentib,
              annualincome: userInfo.annualincome,
              wheremyfx: userInfo.wheremyfx,
              employmentstatus: userInfo.employmentstatus,
              tradingdeskpassword: userInfo.tradingdeskpassword,
              postalcode: userInfo.postalcode,
              firstnamecn: userInfo.firstnamecn,
              tel: userInfo.tel,
              id: userInfo.id,
              email: userInfo.email,
              dateofapprove: userInfo.dateofapprove,
              createtime: userInfo.createtime,
              issalesportaluser: userInfo.issalesportaluser,
              address: userInfo.address,
              myguid: userInfo.myguid,
              lastnamecn: userInfo.lastnamecn,
              isupdated: userInfo.isupdated,
              lastname: userInfo.lastname,
              tradingexp: userInfo.tradingexp,
              nationality: userInfo.nationality,
              companyname: userInfo.companyname,
              isregcompleted: userInfo.isregcompleted,
              isok: userInfo.isok,
              idcard: userInfo.idcard,
              comment: userInfo.comment,
              updatetime: userInfo.updatetime
            }
          })

          this.$store.dispatch('Setting/authUserAction', {
            auth: true,
            authType: 'jwt',
            user: {
              country: userInfo.country,
              firstname: userInfo.firstname,
              gender: userInfo.gender,
              city: userInfo.city,
              ibcode: userInfo.ibcode,
              accounttype: userInfo.accounttype,
              parentib: userInfo.parentib,
              annualincome: userInfo.annualincome,
              wheremyfx: userInfo.wheremyfx,
              employmentstatus: userInfo.employmentstatus,
              tradingdeskpassword: userInfo.tradingdeskpassword,
              postalcode: userInfo.postalcode,
              firstnamecn: userInfo.firstnamecn,
              tel: userInfo.tel,
              id: userInfo.id,
              email: userInfo.email,
              dateofapprove: userInfo.dateofapprove,
              createtime: userInfo.createtime,
              issalesportaluser: userInfo.issalesportaluser,
              address: userInfo.address,
              myguid: userInfo.myguid,
              lastnamecn: userInfo.lastnamecn,
              isupdated: userInfo.isupdated,
              lastname: userInfo.lastname,
              tradingexp: userInfo.tradingexp,
              nationality: userInfo.nationality,
              companyname: userInfo.companyname,
              isregcompleted: userInfo.isregcompleted,
              isok: userInfo.isok,
              idcard: userInfo.idcard,
              comment: userInfo.comment,
              updatetime: userInfo.updatetime
            }
          })

          setLocal('user', JSON.stringify(userInfo), 1)
          setLocal('access_token', res.data.accessToken, 0.5)
          setLocal('refresh_token', res.data.refreshToken, 1)
          // this.$message.success(res.msg)
          this.loginLoading = false
          this.LPOACheck()
          // this.checkPromotionAuthHandle()
          this.$router.push('/').catch(error => {
            console.log(error)
          })

        }
      }).catch(error => {
        // console.log('登录失败', error.toString())
        this.$emit('loginFail', error.toString().replace(/^Error:\s*/, ''))
        this.clickCodeImage()
        this.loginLoading = false
      })
    },

    passportLogin () {
      auth.login(this.user)
        .then(response => {
          if (response.status) {
            localStorage.setItem('user', JSON.stringify(response.data))
            localStorage.HaveSecurityQuestion = res.data.haveSecurityQuestion
            this.LPOACheck()
            this.$router.push({ name: 'dashboard.home-1' })
          } else if (response.data.errors.length > 0) {
            this.$refs.form.setErrors(response.data.errors)
          }
        })
        .catch(error => {
          this.$emit('loginFail', error.toString().replace(/^Error:\s*/, ''))
        })
        .finally(() => {
          this.loading = false
        })
    },
    getCheckCode () {
      this.captchaLoading = true
      this.googleRecaptchaVisible = false
      auth.getCaptcha().then(res => {
        if (res.type === 'googleCaptcha') {
          this.googleRecaptchaVisible = true
          return
        }
        if (this.stateCaptcha.token) {
          this.$store.dispatch('Setting/authCaptchaAction', {
            codeImage: '',
            token: ''
          })
        }
        const codeData = res.data
        if (!codeData) {
          this.$message.error('Error: Get check code error!')
          return
        }
        this.$store.dispatch('Setting/authCaptchaAction', {
          codeImage: codeData.codeImage,
          token: codeData.token
        })
        this.codePicture = this.stateCaptcha.codeImage
        this.captchaLoading = false
      })
    },

    clickCodeImage () {
      this.getCheckCode()
    },
    clickGoogleCaptcha (val) {
      this.$store.dispatch('Setting/authCaptchaAction', {
        codeImage: '',
        token: val
      })
    }
  }
}
</script>

<style scoped lang="scss">
.captchaPicture {
  float: right;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
  width: 100%;
  text-align: center;
  border-radius: 4px;

  .btn1 {
    display: flex;
    font-size: 12px;
    gap: 10px;
  }

  .btn2 {
    width: 120px;
    display: flex;
    justify-content: flex-end;
  }
}

.btns .btn1 {
  display: flex;
  gap: 20px;
}

.pwd-box {
  position: relative;

  .el-icon-view {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    cursor: pointer;
    color: #c0c4cc;
  }

  .is-invalid + .el-icon-view {
    right: 30px;
  }
}

@media screen and (max-width: 1200px) {
  .btn {
    .btn1 {
      flex-direction: column;
      text-align: left;
    }
  }
}
</style>
