<template>
  <b-container fluid="">
    <div v-if="screenWidth > 720">
      <b-row style="margin-bottom: 10px;">
        <b-col style="padding: 0 8px" sm="12" lg="8">
          <div class="card-box">
            <div class="card-content" style="margin-right: 20px;">
              <div class="card-icon" style="transform: translate(0, 20px)">
                <img src="@/assets/images/dashboard/name.png" alt="user-icon">
              </div>
            </div>
            <div class="card-content" style="flex: 2">
              <p class="title">{{ $t('sidebar.name') }}：</p>
              <p class="title-content text">{{ generalInformation.name || '--' }}</p>
            </div>
            <!--            <div class="card-content" style="flex: 2">
                          <p class="title">{{ $t('sidebar.phone') }}：</p>
                          <p class="title-content text">{{ generalInformation.phoneNumber || '--' }}</p>
                        </div>-->
            <div class="card-content" style="flex: 2">
              <p class="title">{{ $t('sidebar.email') }}：</p>
              <p class="title-content text">{{ generalInformation.emailAddress || '--' }}</p>
            </div>
          </div>
        </b-col>
        <b-col style="padding: 0 8px" sm="12" lg="4">
          <!--          <router-link :to="{name: 'production.siteNews', params: {name: 'announcement'} }">-->
          <div class="card-box" style="justify-content: unset; height: 100%"
               v-viewpoint.click="{ message: `[Dashboard] Click Market News/Announcement` }">
            <div class="card-content">
              <!--  style="transform: translate(0, -20px)" -->
              <div class="card-icon">
                <img src="@/assets/images/dashboard/news.png" alt="news-icon">
              </div>
            </div>
            <div class="card-content" style="margin-left: 20px;">
              <p class="title-content text link-button" style="color: #495EEB;"
                 @click="$router.push({name: 'production.siteNews', params: {name: 'announcement'} })">
                <span style="cursor:pointer;">{{ $t('dashboard1.card3') }}</span>
              </p>
              <h4 v-if="numberPanel.announcement != 0" class="title"
                  style="color: #495eeb; font-weight: 700; font-size: 18px;">
                <Counter :value="numberPanel.announcement"></Counter>
              </h4>
            </div>
          </div>
          <!--          </router-link>-->
        </b-col>
      </b-row>
      <b-row style="margin-bottom: 10px;">
        <b-col sm="12" lg="8" style="padding: 8px">
          <div style="display: flex; flex-direction: column; gap: 20px;height: 100%">
            <div class="card-box" style="justify-content: space-between; padding: 30px 20px;">

              <div class="card-content" style="margin-right: 20px;">
                <div class="card-icon">
                  <img src="@/assets/images/dashboard/history.png" alt="trade-icon">
                </div>
              </div>
              <!--             -->
              <div style="display: flex;align-items: center; cursor: pointer; flex: 1;"
                   v-viewpoint.click="{ message: `[Dashboard] Click Trade History` }">
                <div class="card-content">
                  <p class="link-button" style="color: #495EEB; margin: 0 auto 0" @click="openAccountDetails">
                    <span style="cursor:pointer;">{{ $t('dashboard1.card1') }}</span>
                  </p>
                </div>
                <!--              @click.stop="serverClick"-->
                <div class="card-content"
                     style="flex: 1; display: flex; flex-direction: row; justify-content: flex-end; align-items: center;"
                >
                  <!--                <div style="width: fit-content;display: flex;align-items: center;">-->
                  <!--                  &lt;!&ndash;              <span style="margin-top:18px;  color: #495eeb;font-size: calc(1rem + 0.2vw);  font-weight: 700;"&ndash;&gt;-->
                  <!--                  &lt;!&ndash;                    v-if="serverActive">MT4</span>&ndash;&gt;-->
                  <!--                  &lt;!&ndash;                <span style="margin-top:18px; color: #495eeb;font-size: calc(1rem + 0.2vw);  font-weight: 700;"&ndash;&gt;-->
                  <!--                  &lt;!&ndash;                      v-if="!serverActive">MT5</span>&ndash;&gt;-->
                  <!--                  <div class="mt-box" :class="{ 'mt5-show': !serverActive }">-->
                  <!--                    <div class="mt4-icon"></div>-->
                  <!--                    <div class="mt5-icon"></div>-->
                  <!--                  </div>-->
                  <!--                  <a href="javascript:;">-->
                  <!--                    <i-->
                  <!--                      style="font-size: calc(1rem + 0.1vw); color: #FBA913; transform: translate(2px, -2px); line-height: unset;"-->
                  <!--                      class="las la-sync"></i>-->
                  <!--                  </a>-->
                  <!--                </div>-->
                </div>
                <div style="width: 10px;"></div>
              </div>

              <div style="flex: 2;display: flex;align-items: center;justify-content: space-evenly;">
                <div class="card-content" style="flex: 1">
                  <p class="title">
                    <!--                  {{ $t('dashboard1.account') }}-->
                    <div class="mt-type" style="width: fit-content;">
                      <div
                        class="mt-type-item"
                        :class="{ 'mt-type-active': item.id === mtActive }"
                        v-for="item in mtType"
                        :key="item.id"
                        @click="mtTypeChange(item.id)"
                        v-viewpoint.click="{ message: `[Dashboard] Click change tab ${item.name}` }"
                      >
                        {{ item.name }}
                      </div>
                      <div class="slider" :style="{ left: `${(mtActive-1) * 50 + (mtActive === 1 ? -4 : 0)}px` }"></div>
                    </div>
                  </p>
                  <div class="title-content select">
                    <div v-if="accounDetailsBalanceActive">
                      <el-select
                        v-model="selValue"
                        :placeholder="$t('dashboard1.placeholder')"
                        clearable
                        @change="handleChange"
                      >
                        <el-option
                          v-for="item in tradingAccountMT4List"
                          :key="item.myguid"
                          :label="item.login"
                          :value="item.login"
                          v-if="serverActive">
                        </el-option>
                        <el-option
                          v-for="item in tradingAccountMT5List"
                          :key="item.myguid"
                          :label="item.login"
                          :value="item.login"
                          v-if="!serverActive">
                        </el-option>
                      </el-select>
                    </div>
                    <div v-if="!accounDetailsBalanceActive">
                      <el-select v-model="selValue" :placeholder="$t('dashboard1.placeholder')" clearable
                                 @change="handleChange">
                        <el-option
                          v-for="item in tradingAccountMT4List"
                          :key="item.myguid"
                          :label="item.login"
                          :value="item.login"
                          v-if="serverActive">
                        </el-option>
                        <el-option
                          v-for="item in tradingAccountMT5List"
                          :key="item.myguid"
                          :label="item.login"
                          :value="item.login"
                          v-if="!serverActive">
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
                <div class="card-content account" style="flex: 1">
                  <div style="display: flex; align-items: center;">
                    <p class="title" style="margin-right: 8px;">{{ $t('dashboard1.Status.title') }}</p>
                    <el-tooltip placement="right" effect="light" v-if="selectedTradingAccount.status == -3">
                      <div slot="content" style="width: 240px;color: #000000;">
                        <!--                      Your trading account is archived, for-->
                        <!--                      assistance with your account balance,-->
                        <!--                      please contact our customer support.-->
                        {{ $t('dashboard1.Status.hint') }}
                      </div>
                      <div style="cursor:pointer; color: #495EEB">
                      <span
                        style="color: #000000">{{ $t('dashboard1.Status.status_02') }}</span>
                      </div>
                    </el-tooltip>
                    <span v-else style="color: #000000;">{{ $t('dashboard1.Status.status_01') }}</span>
                  </div>
                  <div style="display: flex; align-items: center;">
                    <p class="title" style="margin-right: 8px">{{ $t('dashboard1.accountBalance') }} </p>
                    <!--          账户-->
                    <div v-loading="accounDetailsBalanceLoading">
                      <!--                              <div class="hidden-md-and-up" v-if="!tradingAccountLoading">
                                                      <MobilePicker icon-name="el-icon-more-outline" @confirm="getBalanceMobile"
                                                                    :data-list="mobileSelectionConfig"></MobilePicker>
                                                    </div>-->

                      <h4 style="height: 70%; color: black;font-size: 18px;"
                          v-if="!accounDetailsBalanceActive">
                        <Counter :value="numberPanel.accountDetails"></Counter>
                      </h4>
                      <h4 style="height: 70%; color: black;font-size: 18px;"
                          v-if="accounDetailsBalanceActive">--
                      </h4>
                    </div>
                  </div>
                </div>
              </div>

            </div>

            <div style="flex: 1;background: #fff;height: 100%;border-radius: 8px;padding: 40px 20px 20px">
              <div style="display: flex; gap: 10px; align-items: center;">
                <img width="40" src="@/assets/images/dashboard/promotion.png" alt="pro-icon">
                <span style="color: #495EEB;font-size: 16px;">{{ $t('dashboard1.card4') }}</span>
              </div>

              <div class="promotion-card">
                <!--   cashback campaign 2025 May  -->
                <div class="promotion-card-item" v-loading="cashback2025MayLoading"
                     v-if="!cashback2025MayShow">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2025-07-04 23:59:59')">
                  <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/cashback2025May-ja_JP.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <img style="border-radius: 10px;aspect-ratio: 300 / 150;" src="/img/promotion/cashback2025May-en_US.png" v-else>
                  <div
                    class="promotion-card-item-btn"
                    :style="joinedAccountList.length > 0 ? {} : { background: '#01FF96', color: '#0d1350' }"
                    @click="$router.push('/promotion/cashback-2025-may')">
                    {{ joinedAccountList.length > 0 ? $t('promotion.up600.button_ok') : $t('promotion.up600.button') }}
                  </div>
                </div>

                <!--   line 1000  -->
                <div class="promotion-card-item" v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2025-03-13 23:59:59')">
                  <img src="/img/promotion/LINE_1000.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <div
                    class="promotion-card-item-btn"
                    :style="new Date() > new Date('2025-03-13 23:59:59') ? {} : {
                      background: '#01FF96', color: '#0d1350'
                    }"
                    @click="$router.push('/promotion/line-1000')"
                  >
                    {{ new Date() > new Date('2025-03-13 23:59:59') ? $t('promotion.newYear2025.viewBtn') : $t('promotion.up600.button') }}
                  </div>
                </div>

                <!--   new year  -->
                <div class="promotion-card-item" v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2025-01-31 23:59:59')">
                  <img src="/img/promotion/new-year-2025-ja_JP.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <div
                    class="promotion-card-item-btn"
                    @click="$router.push('/promotion/new-year-2025')"
                  >
                    {{ $t('promotion.newYear2025.viewBtn') }}
                  </div>
                </div>

                <!--   christmas  -->
<!--                <div class="promotion-card-item" v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2024-12-27 23:59:59')">
                  <img src="/img/promotion/christmas.gif" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <div
                    class="promotion-card-item-btn"
                    @click="$router.push('/promotion/christmas')"
                  >
                    {{ $t('promotion.christmas.viewBtn') }}
                  </div>
                </div>-->


                <!--   cyberMonday  -->
                <div class="promotion-card-item">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2024-12-03 23:59:59')">
                  <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/cyberMonday-ja_JP.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                  <img v-else src="/img/promotion/cyberMonday-en_US.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <div
                    class="promotion-card-item-btn"
                    @click="$router.push('/promotion/cyber-monday')"
                  >
                    {{ $t('promotion.cyberMonday.viewBtn') }}
                  </div>
                </div>

                <!--   blackFriday  -->
<!--                <div class="promotion-card-item">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2024-11-29 23:59:59')">
                  <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/blackFriday-ja_JP.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                  <img v-else src="/img/promotion/blackFriday-en_US.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <div
                    class="promotion-card-item-btn"
                    @click="$router.push('/promotion/black-friday')"
                  >
                    {{ $t('promotion.blackFriday.viewBtn') }}
                  </div>
                </div>-->


<!--                <div class="promotion-card-item">
                  <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="">
                  <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/halloween-ja_JP.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                  <img v-else src="/img/promotion/halloween-en_US.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                  <div
                    class="promotion-card-item-btn"
                    @click="$router.push('/promotion/halloween')"
                  >
                    {{ $t('promotion.halloween.viewBtn') }}
                  </div>
                </div>-->
<!--                <div class="promotion-card-item" v-loading="welcomeLoading">-->
<!--                  &lt;!&ndash;                  <div class="promotion-card-item-title">Welcome Bonus</div>&ndash;&gt;-->
<!--                  &lt;!&ndash;                  <div class="promotion-card-item-subtitle">50% Bonus up to <span>$600</span></div>&ndash;&gt;-->
<!--                  <img :src="upCover" style="border-radius: 10px; object-fit: cover">-->
<!--                  <div-->
<!--                    class="promotion-card-item-btn"-->
<!--                    :style="-->
<!--                      up600Status > 0 ? {} : { background: '#01FF96', color: '#0d1350' }-->
<!--                    "-->
<!--                    @click="showPopup"-->
<!--                  >-->
<!--                    {{ up600Status > 0 ? $t('promotion.up600.button_ok') : $t('promotion.up600.button') }}-->
<!--                  </div>-->
<!--                </div>-->
<!--                <div class="promotion-card-item">-->
<!--                  <img :src="require('@/assets/images/dashboard/fp1.jpg')" style="border-radius: 10px; object-fit: cover">-->
<!--                </div>-->
<!--                <div class="promotion-card-item">-->
<!--                  <img :src="require('@/assets/images/dashboard/fp2.jpg')" style="border-radius: 10px; object-fit: cover">-->
<!--                </div>-->
              </div>
            </div>
          </div>
        </b-col>

        <b-col sm="12" lg="4" style="padding: 8px">
          <div style="position:relative;z-index: 2" v-loading.lock="depositWithdrawalDataLoading">
            <iq-card class="iq-card-block iq-card-stretch iq-card-height" style="margin-bottom: 0;">
              <template v-slot:headerTitle>
                <h5 class="card-title" style="font-size: 16px; color: #495eeb">
                  {{ $t('dashboard1.depositAndWithdrawal') }}</h5>
              </template>
              <template v-slot:headerAction>
                <b-dropdown class="hidden-md-and-down" id="dropdownMenuButton1" right variant="none"
                            data-toggle="dropdown">
                  <template v-slot:button-content>
                    <span class="text-primary"><i class="ri-more-fill"></i></span>
                  </template>
                  <b-dropdown-item @click="getDepositWithdrawalHistoryListDesktop(0)"
                                   v-viewpoint.click="{ message: `[Dashboard] Click deposit and withdrawal details` }">
                    <i class="ri-eye-fill mr-2"></i>{{ $t('dropdown.view') }}
                  </b-dropdown-item>
                </b-dropdown>
                <i
                  @click="mobileDepositWithdrawalHistoryShow = true;getDepositWithdrawalHistoryListMobile(0)"
                  class="el-icon-menu hidden-lg-and-up"
                  v-viewpoint.click="{ message: `[Dashboard] Click deposit and withdrawal details` }"></i>
              </template>
              <template v-slot:body>
                <div class="deposit">
                  <ul v-if="depositWithdrawalData.length !== 0 && !depositWithdrawalDataLoading"
                      class="suggestions-lists m-0 p-0">
                    <li v-for="(invoice,index) in depositWithdrawalData" :key="index"
                        :class="'d-flex align-items-center ' + invoice.spaces">
                      <div v-if="invoice.type === 'Deposit'" :class="'profile-icon iq-bg-' + invoice.color"
                           :style="{ color: '#00DF82 !important', background: `rgba(3, 255, 150, 0.3) !important` }">
                        <span><i class="ri-arrow-right-up-line"></i></span>
                      </div>
                      <div v-else :class="'profile-icon iq-bg-' + invoice.color">
                        <span><i class="ri-arrow-right-down-line"></i></span>
                      </div>
                      <div class="media-support-info ml-3">
                        <h6 :style="{ color: invoice.type === 'Deposit' ? '#00DF82' : '#495EEB' }">
                          {{ $t('dashboard1.type.' + invoice.type) }}</h6>
                        <p class="mb-0 fontsize-sm">
                          <!--                        :class="'text-' + invoice.color"-->
                          <span>{{ invoice.login }}</span>
                        </p>
                      </div>
                      <div class="media-support-amount ml-3">
                        <h6>
                          <span :class="'text-secondary'"></span>
                          <b :style="{ color: invoice.type === 'Deposit' ? '#00DF82' : '#495EEB', fontWeight: 400 }">
                            {{ invoice.currency }}&nbsp;<span>{{ invoice.profit }}</span>
                          </b>
                        </h6>
                        <p class="mb-0 d-flex justify-content-end">{{ invoice.time }}</p>
                      </div>
                    </li>
                  </ul>
                  <el-empty v-if="depositWithdrawalData.length === 0 && !depositWithdrawalDataLoading"></el-empty>
                </div>
              </template>
            </iq-card>
          </div>
          <!--          &lt;!&ndash;          <router-link :to="{name: 'sidebar.promotion', params: {name: 'promotions'} }">&ndash;&gt;-->
          <!--          &lt;!&ndash;          <router-link :to="{ path: '/promotion' }">&ndash;&gt;-->
          <!--          <div class="card-box" style="justify-content: unset;height: 100%">-->
          <!--            <div class="card-content">-->
          <!--              &lt;!&ndash;  style="transform: translate(0, -19px)" &ndash;&gt;-->
          <!--              <div class="card-icon">-->
          <!--                <img src="../../assets/images/dashboard/promotion.png" alt="pro-icon">-->
          <!--              </div>-->
          <!--            </div>-->
          <!--            <div class="card-content" style="margin-left: 20px;">-->

          <!--              <p class="title-content text link-button" style="color: #495EEB;">-->
          <!--                <span style="cursor:pointer;" @click="$router.push('/promotion')"-->
          <!--                      v-if="isPromotionUrl">{{ $t('dashboard1.card4') }}</span>-->
          <!--                <span style="cursor:pointer;" @click="$router.push('/siteNews')" v-else>-->
          <!--                  {{ $t('dashboard1.card4') }}</span>-->
          <!--              </p>-->
          <!--              <h4 v-if="numberPanel.promotion != 0" class="title"-->
          <!--                  style="color: #495eeb; font-weight: 700; font-size: 18px;">-->
          <!--                <Counter :value="numberPanel.promotion"></Counter>-->
          <!--              </h4>-->
          <!--            </div>-->
          <!--          </div>-->
          <!--          &lt;!&ndash;          </router-link>&ndash;&gt;-->
        </b-col>
      </b-row>
      <!--      <b-row>-->
      <!--        <b-col sm="12" lg="8">-->
      <!--          <div style="background: #fff;height: 100%;border-radius: 8px;padding: 20px">-->
      <!--            <div style="display: flex; gap: 10px; align-items: center;">-->
      <!--              <img width="40" src="../../assets/images/dashboard/promotion.png" alt="pro-icon">-->
      <!--              <span style="color: #495EEB;font-size: 16px;">{{ $t('dashboard1.card4') }}</span>-->
      <!--            </div>-->

      <!--            <div class="promotion-card">-->
      <!--              <div class="promotion-card-item">-->
      <!--                <div class="promotion-card-item-title">Welcome Bonus</div>-->
      <!--                <div class="promotion-card-item-subtitle">50% Bonus up to <span>$600</span></div>-->
      <!--                <div class="promotion-card-item-btn">Join Now!</div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          &lt;!&ndash;        <div style="position:relative;z-index: 2" v-loading.lock="chartistChartLoading">&ndash;&gt;-->
      <!--          &lt;!&ndash;          <iq-card class="iq-card-block iq-card-stretch ">&ndash;&gt;-->
      <!--          &lt;!&ndash;            <template v-slot:headerTitle>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <h5 class="card-title">{{ $t('dashboard1.balance') }}</h5>&ndash;&gt;-->
      <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
      <!--          &lt;!&ndash;            <template v-slot:headerAction>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <b-form-select class="hidden-md-and-down" @change="getChartistChartData" v-model="selectedTradingAccount"&ndash;&gt;-->
      <!--          &lt;!&ndash;                             style="margin-right: 20px;width: 30%">&ndash;&gt;-->
      <!--          &lt;!&ndash;                <b-select-option v-for="item in tradingAccountList" :value="item"> {{ item.login }}</b-select-option>&ndash;&gt;-->
      <!--          &lt;!&ndash;              </b-form-select>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <el-date-picker class="hidden-md-and-down" style="width: 80%" v-model="chDateTime" type="daterange"&ndash;&gt;-->
      <!--          &lt;!&ndash;                              @change="getChartistChartData" :clearable="false" size="medium"&ndash;&gt;-->
      <!--          &lt;!&ndash;                              format="yyyy-MM-dd"&ndash;&gt;-->
      <!--          &lt;!&ndash;                              value-format="yyyy-MM-dd"&ndash;&gt;-->
      <!--          &lt;!&ndash;                              :unlink-panels="false"&ndash;&gt;-->
      <!--          &lt;!&ndash;                              range-separator="to" start-placeholder="Start Time" end-placeholder="End Time">&ndash;&gt;-->
      <!--          &lt;!&ndash;              </el-date-picker>&ndash;&gt;-->

      <!--          &lt;!&ndash;              <MobilePicker @confirm="getChartistChartDataMobileLogin" v-if="!tradingAccountLoading"&ndash;&gt;-->
      <!--          &lt;!&ndash;                            icon-name="el-icon-user" class="hidden-lg-and-up"&ndash;&gt;-->
      <!--          &lt;!&ndash;                            :data-list="mobileSelectionConfig"&ndash;&gt;-->

      <!--          &lt;!&ndash;              ></MobilePicker>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <MobileDateTimePicker @confirm="getChartistChartDataMobileDateTime" v-if="!tradingAccountLoading"&ndash;&gt;-->
      <!--          &lt;!&ndash;                                    icon-name="el-icon-date" class="hidden-lg-and-up"></MobileDateTimePicker>&ndash;&gt;-->
      <!--          &lt;!&ndash;              &lt;!&ndash;              <div v-if="screenWidth < 720">&ndash;&gt;-->
      <!--          &lt;!&ndash;                              <i style="font-size: calc(1rem + 0.1vw)" class="el-icon-date" @click="showCalendar = true"></i>&ndash;&gt;-->
      <!--          &lt;!&ndash;                            </div>&ndash;&gt;&ndash;&gt;-->
      <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
      <!--          &lt;!&ndash;            <template v-slot:body>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <div style="min-height: 300px;">&ndash;&gt;-->
      <!--          &lt;!&ndash;                &lt;!&ndash;                <ChartistChart v-if="!chartistChartLoading && chart4.data.labels.length!==0" :id="chart4.id"&ndash;&gt;-->
      <!--          &lt;!&ndash;                                               :data="chart4.data"&ndash;&gt;-->
      <!--          &lt;!&ndash;                                               :options="chart4.options" :type="chart4.type"/>&ndash;&gt;-->
      <!--          &lt;!&ndash;                                <el-empty v-else></el-empty>&ndash;&gt;&ndash;&gt;-->
      <!--          &lt;!&ndash;                <ChartistChart2 v-if="!chartistChartLoading && chart4.data.labels.length!==0" :id="chart4.id"&ndash;&gt;-->
      <!--          &lt;!&ndash;                                :chart4Data="chart4"></ChartistChart2>&ndash;&gt;-->
      <!--          &lt;!&ndash;                <el-empty v-else></el-empty>&ndash;&gt;-->
      <!--          &lt;!&ndash;              </div>&ndash;&gt;-->
      <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
      <!--          &lt;!&ndash;          </iq-card>&ndash;&gt;-->
      <!--          &lt;!&ndash;        </div>&ndash;&gt;-->
      <!--          &lt;!&ndash;        <div style="position:relative;z-index: 2" v-loading.lock="renderColumnChart">&ndash;&gt;-->
      <!--          &lt;!&ndash;          <iq-card style="height: 400px" class-name="iq-card-block iq-card-stretch iq-card-height overflow-hidden">&ndash;&gt;-->
      <!--          &lt;!&ndash;            <template v-slot:headerTitle>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <h5 class="card-title" style="font-size: 16px; color: #495eeb">{{ $t('dashboard1.winningOrders') }}</h5>&ndash;&gt;-->
      <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
      <!--          &lt;!&ndash;            <template v-slot:headerAction>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <el-date-picker&ndash;&gt;-->
      <!--          &lt;!&ndash;                v-model="dateTime"&ndash;&gt;-->
      <!--          &lt;!&ndash;                type="daterange"&ndash;&gt;-->
      <!--          &lt;!&ndash;                @change="getColumnChartData"&ndash;&gt;-->
      <!--          &lt;!&ndash;                :clearable="false"&ndash;&gt;-->
      <!--          &lt;!&ndash;                format="yyyy-MM-dd"&ndash;&gt;-->
      <!--          &lt;!&ndash;                value-format="yyyy-MM-dd"&ndash;&gt;-->
      <!--          &lt;!&ndash;                range-separator="to"&ndash;&gt;-->
      <!--          &lt;!&ndash;                class="hidden-sm-and-down">&ndash;&gt;-->
      <!--          &lt;!&ndash;              </el-date-picker>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <MobileDateTimePicker icon-name="el-icon-date" @confirm="getColumnChartDataMobile"&ndash;&gt;-->
      <!--          &lt;!&ndash;                                    class="hidden-md-and-up"></MobileDateTimePicker>&ndash;&gt;-->
      <!--          &lt;!&ndash;              &lt;!&ndash;              <div v-if="screenWidth < 720">&ndash;&gt;&ndash;&gt;-->
      <!--          &lt;!&ndash;              &lt;!&ndash;                <i style="font-size: calc(1rem + 0.1vw)" class="el-icon-date" @click="showCalendar = true"></i>&ndash;&gt;&ndash;&gt;-->
      <!--          &lt;!&ndash;              &lt;!&ndash;              </div>&ndash;&gt;&ndash;&gt;-->

      <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
      <!--          &lt;!&ndash;            <template v-slot:body>&ndash;&gt;-->
      <!--          &lt;!&ndash;              <div style="min-height: 300px;">&ndash;&gt;-->
      <!--          &lt;!&ndash;                <ApexChart v-if="chart1.xaxis.categories.length !== 0 && !renderColumnChart" element="home-chart-02"&ndash;&gt;-->
      <!--          &lt;!&ndash;                           v-bind:chartOption="chart1" style="min-height: 300px;"/>&ndash;&gt;-->
      <!--          &lt;!&ndash;                <el-empty v-if="chart1.xaxis.categories.length === 0 && !renderColumnChart"&ndash;&gt;-->
      <!--          &lt;!&ndash;                          :description="$t('dashboard1.winningOrdersEmpty')"></el-empty>&ndash;&gt;-->
      <!--          &lt;!&ndash;              </div>&ndash;&gt;-->

      <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
      <!--          &lt;!&ndash;          </iq-card>&ndash;&gt;-->
      <!--          &lt;!&ndash;        </div>&ndash;&gt;-->
      <!--        </b-col>-->
      <!--      </b-row>-->
    </div>

    <div v-else>
      <b-row sm="12">
        <b-col>
          <div class="card-box-mobile" style="align-items: flex-start">
            <div class="card-right">
              <div class="card-icon" style="width: 24px;height: 24px;line-height: 24px">
                <img width="12" src="@/assets/images/dashboard/name.png" alt="user-icon">
              </div>
            </div>
            <div class="card-left">
              <p class="title">
                {{ $t('sidebar.name') }}: <span style="color: #2E2138;font-weight: normal;">
                {{ generalInformation.name }}</span>
              </p>
              <!--              <p class="title">
                              {{ $t('sidebar.phone') }}: <span
                              style="color: #2E2138;font-weight: normal;">{{ generalInformation.phoneNumber }}</span>
                            </p>-->
              <p class="title" style="height: auto!important;">
                {{ $t('sidebar.email') }}: <span
                style="color: #2E2138;font-weight: normal;">{{ generalInformation.emailAddress }}</span>
              </p>
            </div>

          </div>
        </b-col>
      </b-row>
      <b-row sm="12">
        <b-col>
          <div class="card-box-mobile">
            <div class="card-left">

              <div style="display: flex">
                <!--                @click="serverClick"-->
                <div class="title2" style="width: fit-content;display: flex;align-items: center;"
                >
                  <!--                <span v-if="serverActive" class="isMT">MT4</span>-->
                  <!--                <span v-if="!serverActive" class="isMT">MT5</span>-->
                  <!--                  <div class="mt-box" :class="{ 'mt5-show': !serverActive }">-->
                  <!--                    <div class="mt4-icon"></div>-->
                  <!--                    <div class="mt5-icon"></div>-->
                  <!--                  </div>-->
                  <!--                  <a href="javascript:;">-->
                  <!--                    <i style="font-size: calc(1rem + 0.1vw); color: #FBA913; transform: translate(4px, -1px)"-->
                  <!--                       class="las la-sync"></i>-->
                  <!--                  </a>-->

                </div>
                <div style="flex: 1;">
                  <div class="title2" style="height: auto;display: flex;align-items: center;">
                    <!--                 @click="showAccount"-->
                    <div style="width: fit-content;margin-right: 10px;">
                      <!--                      {{ $t('dashboard1.account') }}-->
                      <div class="mt-type">
                        <div
                          class="mt-type-item"
                          :class="{ 'mt-type-active': item.id === mtActive }"
                          v-for="item in mtType"
                          :key="item.id"
                          @click="mtTypeChange(item.id)"
                          v-viewpoint.click="{ message: `[Dashboard] Click change tab ${item.name}` }"
                        >
                          {{ item.name }}
                        </div>
                        <div class="slider"
                             :style="{ left: `${(mtActive-1) * 50 + (mtActive === 1 ? -4 : 0)}px` }"></div>
                      </div>
                    </div>
                    <!--                  <div  v-if="!tradingAccountLoading">
                                        <MobilePicker icon-name="el-icon-more-outline" @confirm="getBalanceMobile"
                                                      :data-list="mobileSelectionConfig"></MobilePicker>
                                      </div>-->
                    <div class="account" style="margin-left: 0;width: 160px;">
                      <!--                  {{ defaultAccount }}-->
                      <el-select
                        v-model="selValue"
                        size="mini"
                        :placeholder="$t('dashboard1.placeholder')"
                        clearable
                        @change="handleChange"
                      >
                        <el-option
                          v-for="item in tradingAccountMT4List"
                          :key="item.myguid"
                          :label="item.login"
                          :value="item.login"
                          v-if="serverActive">
                        </el-option>
                        <el-option
                          v-for="item in tradingAccountMT5List"
                          :key="item.myguid"
                          :label="item.login"
                          :value="item.login"
                          v-if="!serverActive">
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="title2"
                       style="display: flex;padding-left: 34px;margin-top: 10px;">
                    <div
                      style="height: 100%; max-width: 330px;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;display: flex"
                      v-loading="accounDetailsBalanceLoading">
                      <div style="margin-right: 10px;">{{ $t('dashboard1.Status.title') }}</div>
                      <div style="color: #495eeb; height: 100%;">
                        <span v-if="selectedTradingAccount.status == -3"
                              style="color: #000000;">{{ $t('dashboard1.Status.status_02') }}</span>
                        <span v-else style="color: #000000;">{{ $t('dashboard1.Status.status_01') }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="title2" style="display: flex;padding-left: 34px;margin-bottom: 10px;">
                    <div
                      style="height: 100%; max-width: 330px;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;display: flex"
                      v-loading="accounDetailsBalanceLoading">
                      <div style="margin-right: 10px;">{{ $t('dashboard1.accountBalance') }}</div>
                      <div style="color: black; height: 100%; font-weight: 400"
                           v-if="!accounDetailsBalanceActive">
                        <Counter :value="numberPanel.accountDetails"></Counter>
                      </div>
                      <div style="color: black; height: 100%; line-height: 50px; " v-if="accounDetailsBalanceActive">
                        {{ '- -' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--              -->
              <div style="display: flex; align-items: center;"
                   v-viewpoint.click="{ message: `[Dashboard] Click Trade History` }">
                <div class="card-right">
                  <div class="card-icon" style="width: 24px;height: 24px;line-height: 24px">
                    <img width="12" src="@/assets/images/dashboard/history.png" alt="user-icon">
                  </div>
                </div>
                <div class="title2 link-button" @click="openAccountDetails">
                  <span style="color: #495EEB">{{ $t('dashboard1.card1') }}</span>
                </div>
              </div>
            </div>

          </div>
        </b-col>
      </b-row>
      <b-row sm="12">
        <b-col>
          <div class="card-box-mobile" style="padding: 16px 10px 16px 20px">
            <div class="card-right">
              <div class="card-icon" style="width: 24px;height: 24px;line-height: 24px">
                <img width="12" src="@/assets/images/dashboard/news.png" alt="user-icon">
              </div>
            </div>
            <div class="card-left">
              <div class="title2">
                <!--                <router-link :to="{name: 'production.siteNews', params: {name: 'announcement'} }"-->
                <!--                v-viewpoint.click="{ message: `[Dashboard] Click Market News/Announcement` }">-->
                <div style="display: flex; color: #7f8dfa;">
                  <p class="link-button">
                    <span
                      style="color: #495EEB"
                      @click="$router.push({name: 'production.siteNews', params: {name: 'promotions'} })"
                      v-viewpoint.click="{ message: `[Dashboard] Click Market News/Announcement` }"
                    >{{ $t('dashboard1.card3') }}</span>
                  </p>
                  <h5 v-if="numberPanel.promotion != 0"
                      style="font-weight: 700; margin-left: 14px;line-height: 40px; color: #495eeb;">
                    <Counter :value="numberPanel.promotion"></Counter>
                  </h5>
                </div>
                <!--                </router-link>-->
              </div>
            </div>
          </div>
        </b-col>
      </b-row>

      <b-row sm="12" style="margin-bottom: 10px;">
        <b-col>
          <div style="flex: 1;background: #fff;height: 100%;border-radius: 8px;padding: 20px">
            <div style="display: flex; gap: 10px; align-items: center;">
              <img width="24" src="@/assets/images/dashboard/promotion.png" alt="pro-icon">
              <span style="color: #495EEB;font-size: 14px;">{{ $t('dashboard1.card4') }}</span>
            </div>

            <div class="promotion-card" style="flex-direction: column">

              <!--   cashback campaign 2025 May  -->
              <div class="promotion-card-item" v-loading="cashback2025MayLoading"
                   v-if="!cashback2025MayShow">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2025-07-04 23:59:59')">
                <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/cashback2025May-ja_JP.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                <img style="border-radius: 10px;aspect-ratio: 300 / 150;" src="/img/promotion/cashback2025May-en_US.png" v-else>
                <div
                  class="promotion-card-item-btn"
                  :style="joinedAccountList.length > 0 ? {} : { background: '#01FF96', color: '#0d1350' }"
                  @click="$router.push('/promotion/cashback-2025-may')">
                  {{ joinedAccountList.length > 0 ? $t('promotion.up600.button_ok') : $t('promotion.up600.button') }}
                </div>
              </div>

              <!--   line 1000  -->
              <div class="promotion-card-item" v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2025-03-13 23:59:59')">
                <img src="/img/promotion/LINE_1000.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                <div
                  class="promotion-card-item-btn"
                  :style="new Date() > new Date('2025-03-13 23:59:59') ? {} : {
                    background: '#01FF96', color: '#0d1350'
                  }"
                  @click="$router.push('/promotion/line-1000')"
                >
                  {{ new Date() > new Date('2025-03-13 23:59:59') ? $t('promotion.newYear2025.viewBtn') : $t('promotion.up600.button') }}
                </div>
              </div>

              <!--   new year  -->
              <div class="promotion-card-item" v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2025-01-31 23:59:59')">
                <img src="/img/promotion/new-year-2025-ja_JP.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                <div
                  class="promotion-card-item-btn"
                  @click="$router.push('/promotion/new-year-2025')"
                >
                  {{ $t('promotion.newYear2025.viewBtn') }}
                </div>
              </div>

              <!--   christmas  -->
<!--              <div class="promotion-card-item" v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2024-12-27 23:59:59')">
                <img src="/img/promotion/christmas.gif" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                <div
                  class="promotion-card-item-btn"
                  @click="$router.push('/promotion/christmas')"
                >
                  {{ $t('promotion.christmas.viewBtn') }}
                </div>
              </div>-->

              <!--   cyberMonday  -->
              <div class="promotion-card-item">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2024-12-03 23:59:59')">
                <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/cyberMonday-ja_JP.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                <img v-else src="/img/promotion/cyberMonday-en_US.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                <div
                  class="promotion-card-item-btn"
                  @click="$router.push('/promotion/cyber-monday')"
                >
                  {{ $t('promotion.cyberMonday.viewBtn') }}
                </div>
              </div>

              <!--   blackFriday  -->
<!--              <div class="promotion-card-item">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="" v-if="new Date() > new Date('2024-11-29 23:59:59')">
                <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/blackFriday-ja_JP.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                <img v-else src="/img/promotion/blackFriday-en_US.png" style="border-radius: 10px;aspect-ratio: 300 / 150;">
                <div
                  class="promotion-card-item-btn"
                  @click="$router.push('/promotion/black-friday')"
                >
                  {{ $t('promotion.blackFriday.viewBtn') }}
                </div>
              </div>-->

<!--              <div class="promotion-card-item">
                <img class="expired" :src="'/img/promotion/expired-' + $i18n.locale + '.png'" alt="">
                <img v-if="$i18n.locale === 'ja_JP'" src="/img/promotion/halloween-ja_JP.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                <img v-else src="/img/promotion/halloween-en_US.png" style="border-radius: 10px; aspect-ratio: 300 / 150;">
                <div
                  class="promotion-card-item-btn"
                  @click="$router.push('/promotion/halloween')"
                >
                  {{ $t('promotion.halloween.viewBtn') }}
                </div>
              </div>-->
<!--              <div class="promotion-card-item" v-loading="welcomeLoading">-->
<!--                &lt;!&ndash;                <div class="promotion-card-item-title">Welcome Bonus</div>&ndash;&gt;-->
<!--                &lt;!&ndash;                <div class="promotion-card-item-subtitle">50% Bonus up to <span>$600</span></div>&ndash;&gt;-->
<!--                <img :src="upCover" style="border-radius: 10px">-->
<!--                <div-->
<!--                  class="promotion-card-item-btn"-->
<!--                  :style="-->
<!--                    up600Status > 0 ? {} : { background: '#01FF96', color: '#0d1350' }-->
<!--                  "-->
<!--                  @click="showPopup"-->
<!--                >-->
<!--                  {{ up600Status > 0 ? $t('promotion.up600.button_ok') : $t('promotion.up600.button') }}-->
<!--                </div>-->
<!--              </div>-->
<!--              <div class="promotion-card-item">-->
<!--                <img :src="require('@/assets/images/dashboard/fp1.jpg')" style="border-radius: 10px">-->
<!--              </div>-->
<!--              <div class="promotion-card-item">-->
<!--                <img :src="require('@/assets/images/dashboard/fp2.jpg')" style="border-radius: 10px">-->
<!--              </div>-->
            </div>
          </div>
          <!--          <div class="card-box-mobile">-->
          <!--            <div class="card-right">-->
          <!--              <div class="card-icon" style="width: 24px;height: 24px;line-height: 24px">-->
          <!--                <img width="12" src="../../assets/images/dashboard/promotion.png" alt="user-icon">-->
          <!--                <span>{{ $t('dashboard1.card4') }}</span>-->
          <!--              </div>-->
          <!--            </div>-->

          <!--            <div class="promotion-card">-->
          <!--              <div class="promotion-card-item">-->
          <!--                <div class="promotion-card-item-title">Welcome Bonus</div>-->
          <!--                <div class="promotion-card-item-subtitle">50% Bonus up to <span>$600</span></div>-->
          <!--                <div class="promotion-card-item-btn">Join Now!</div>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--            <div class="card-left">-->
          <!--              <div class="title2">-->
          <!--                &lt;!&ndash;                <router-link :to="{name: 'production.siteNews', params: {name: 'promotions'} }"&ndash;&gt;-->
          <!--                &lt;!&ndash;                <router-link :to="{ path: '/promotion' }"&ndash;&gt;-->
          <!--                &lt;!&ndash;                             v-viewpoint.click="{ message: `[Dashboard] Click Promotions` }">&ndash;&gt;-->
          <!--                <div style="display: flex; color: #7f8dfa;">-->
          <!--                  <p class="link-button">-->
          <!--                      <span-->
          <!--                        style="color: #495EEB"-->
          <!--                        @click="$router.push({path: '/promotion'})"-->
          <!--                        v-viewpoint.click="{ message: `[Dashboard] Click Promotions` }"-->
          <!--                        v-if="isPromotionUrl"-->
          <!--                      >{{ $t('dashboard1.card4') }}</span>-->
          <!--                    <span-->
          <!--                      style="color: #495EEB"-->
          <!--                      @click="$router.push({path: '/siteNews'})"-->
          <!--                      v-viewpoint.click="{ message: `[Dashboard] Click Promotions` }"-->
          <!--                      v-else-->
          <!--                    >{{ $t('dashboard1.card4') }}</span>-->
          <!--                  </p>-->
          <!--                  <h5 v-if="numberPanel.announcement != 0"-->
          <!--                      style="font-weight: 700; margin-left: 14px;line-height: 40px; color: #495eeb;">-->
          <!--                    <Counter :value="numberPanel.announcement"></Counter>-->
          <!--                  </h5>-->
          <!--                </div>-->
          <!--                &lt;!&ndash;                </router-link>&ndash;&gt;-->
          <!--              </div>-->
          <!--            </div>-->

          <!--          </div>-->
        </b-col>
      </b-row>

      <div style="position:relative;z-index: 2" v-loading.lock="depositWithdrawalDataLoading">
        <iq-card class="iq-card-block iq-card-stretch iq-card-height" style="margin-bottom: 0;">
          <template v-slot:headerTitle>
            <h5 class="card-title" style="font-size: 16px; color: #495eeb">
              {{ $t('dashboard1.depositAndWithdrawal') }}</h5>
          </template>
          <template v-slot:headerAction>
            <b-dropdown class="hidden-md-and-down" id="dropdownMenuButton1" right variant="none"
                        data-toggle="dropdown">
              <template v-slot:button-content>
                <span class="text-primary"><i class="ri-more-fill"></i></span>
              </template>
              <b-dropdown-item @click="getDepositWithdrawalHistoryListDesktop(0)"
                               v-viewpoint.click="{ message: `[Dashboard] Click deposit and withdrawal details` }">
                <i class="ri-eye-fill mr-2"></i>{{ $t('dropdown.view') }}
              </b-dropdown-item>
            </b-dropdown>
            <i
              @click="mobileDepositWithdrawalHistoryShow = true;getDepositWithdrawalHistoryListMobile(0)"
              class="el-icon-menu hidden-lg-and-up"
              v-viewpoint.click="{ message: `[Dashboard] Click deposit and withdrawal details` }"></i>
          </template>
          <template v-slot:body>
            <div class="deposit">
              <ul v-if="depositWithdrawalData.length !== 0 && !depositWithdrawalDataLoading"
                  class="suggestions-lists m-0 p-0">
                <li v-for="(invoice,index) in depositWithdrawalData" :key="index"
                    :class="'d-flex align-items-center ' + invoice.spaces">
                  <div v-if="invoice.type === 'Deposit'" :class="'profile-icon iq-bg-' + invoice.color"
                       :style="{ color: '#00DF82 !important', background: `rgba(3, 255, 150, 0.3) !important` }">
                    <span><i class="ri-arrow-right-up-line"></i></span>
                  </div>
                  <div v-else :class="'profile-icon iq-bg-' + invoice.color">
                    <span><i class="ri-arrow-right-down-line"></i></span>
                  </div>
                  <div class="media-support-info ml-3">
                    <h6 :style="{ color: invoice.type === 'Deposit' ? '#00DF82' : '#495EEB' }">
                      {{ $t('dashboard1.type.' + invoice.type) }}</h6>
                    <p class="mb-0 fontsize-sm">
                      <!--                        :class="'text-' + invoice.color"-->
                      <span>{{ invoice.login }}</span>
                    </p>
                  </div>
                  <div class="media-support-amount ml-3">
                    <h6>
                      <span :class="'text-secondary'"></span>
                      <b :style="{ color: invoice.type === 'Deposit' ? '#00DF82' : '#495EEB', fontWeight: 400 }">
                        {{ invoice.currency }}&nbsp;<span>{{ invoice.profit }}</span>
                      </b>
                    </h6>
                    <p class="mb-0 d-flex justify-content-end">{{ invoice.time }}</p>
                  </div>
                </li>
              </ul>
              <el-empty v-if="depositWithdrawalData.length === 0 && !depositWithdrawalDataLoading"></el-empty>
            </div>
          </template>
        </iq-card>
      </div>

      <!--      <b-row>
              <b-col md="12" sm="12" lg="4">
                <iq-card>
                  <template v-slot:body>
                    <div class="d-flex align-items-center justify-content-between">

                      &lt;!&ndash;                      <b-select size="sm" style="width: 100%;" v-model="accountLoginServer" @change="getBalance">
                                              <b-select-option v-if="tradingAccountMT4List.length == 0" value="">
                                                <i v-if="tradingAccountMT4List.length == 0" style="padding: 0;margin-left: 5px"
                                                   class="el-icon-loading"></i>
                                              </b-select-option>
                                              <b-select-option v-for="(item,index) in tradingAccountMT4List"
                                                               v-bind:value="{'accountLogin':item.login,'server':item.server}" v-if="serverActive">
                                                {{item.login}}
                                              </b-select-option>
                                              <b-select-option v-for="(item,index) in tradingAccountMT5List"
                                                               v-bind:value="{'accountLogin':item.login,'server':item.server}" v-if="!serverActive">
                                                {{item.login}}
                                              </b-select-option>
                                            </b-select>&ndash;&gt;

                      <div>
                        <div style="position:relative;z-index: 2" v-loading="accounDetailsBalanceLoading">
                          <div class="hidden-md-and-up" v-if="!tradingAccountLoading">
                            <MobilePicker icon-name="el-icon-more-outline" @confirm="getBalanceMobile"
                                          :data-list="mobileSelectionConfig"></MobilePicker>
                          </div>

                          <h4 style="height: 70%" v-if="!accounDetailsBalanceActive">
                            &lt;!&ndash;                    {{ numberPanel.accountDetails }}&ndash;&gt;
                            <Counter :value="numberPanel.accountDetails"></Counter>
                            <el-dropdown @command="getBalanceDesktop" placement="bottom" trigger="click"
                                         class=" hidden-sm-and-down">
                              <i style="font-size: calc(1rem + 0.1vw)" class="el-icon-more-outline m-0  hidden-sm-and-down"></i>
                              <el-dropdown-menu style="overflow-y: auto;max-height: 200px">
                                <el-dropdown-item v-for="(item,index) in tradingAccountMT4List"
                                                  v-bind:command="{'accountLogin':item.login,'server':item.server}"
                                                  v-if="serverActive">{{ item.login }}
                                </el-dropdown-item>
                                <el-dropdown-item v-for="(item,index) in tradingAccountMT5List"
                                                  v-bind:command="{'accountLogin':item.login,'server':item.server}"
                                                  v-if="!serverActive">{{ item.login }}
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </h4>
                          <h4 style="height: 70%" v-if="accounDetailsBalanceActive">
                            {{ '- -' }}
                            <el-dropdown @command="getBalanceDesktop" placement="bottom" trigger="click"
                                         class=" hidden-sm-and-down">
                              <i style="font-size: calc(1rem + 0.1vw)" class="el-icon-more-outline m-0  hidden-sm-and-down"></i>
                              <el-dropdown-menu style="overflow-y: auto;max-height: 200px">
                                <el-dropdown-item v-for="(item,index) in tradingAccountMT4List"
                                                  v-bind:command="{'accountLogin':item.login,'server':item.server}"
                                                  v-if="serverActive">{{ item.login }}
                                </el-dropdown-item>
                                <el-dropdown-item v-for="(item,index) in tradingAccountMT5List"
                                                  v-bind:command="{'accountLogin':item.login,'server':item.server}"
                                                  v-if="!serverActive">{{ item.login }}
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </h4>

                        </div>
                        <div>
                          <a href="javascript:;">
                            <p style=";" @click="openAccountDetails" class="fontsize-sm m-0">{{ $t('dashboard1.card1') }}</p>
                          </a>
                        </div>
                      </div>

                      <div class="rounded-circle iq-card-icon iq-bg-primary ">
                        &lt;!&ndash; <i class="ri-inbox-fill"></i> &ndash;&gt;
                        <span style="margin-top:18px;  color: #6546D2;font-size: calc(1rem + 0.2vw);  font-weight: 700;"
                              v-if="serverActive">MT4</span>
                        <span style="margin-top:18px; color: #6546D2;font-size: calc(1rem + 0.2vw);  font-weight: 700;"
                              v-if="!serverActive">MT5</span>
                        <a class="hidden-sm-and-down" href="javascript:;" @click="serverClick">
                          <i style="font-size: calc(0.7rem + 0.1vw)" class="las la-sync"></i>
                        </a>
                      </div>
                    </div>
                  </template>
                </iq-card>
              </b-col>
            </b-row>-->

      <wd-popup v-model="selectMobileShow" position="bottom" :style="{ 'height': '230px', 'overflow': 'hidden' }">
        <div
          style="display: flex; justify-content: space-between; padding: 10px 20px; font-weight: 700; color: #656568">
          <div @click="selectMobileShow = false">{{ $t('dashboard1.dropdown_cancel') }}</div>
          <div style="color: #7f8dfa;" @click="selectMobileDone">{{ $t('dashboard1.dropdown_done') }}</div>
        </div>
        <div>
          <wd-picker-view :columns="columns" v-model="selectValueMobile" @change="selectChangeMobile"></wd-picker-view>
        </div>
      </wd-popup>
    </div>

    <!--    <b-row>-->

    <!--      <b-col sm="12" lg="4">-->
    <!--        <div style="position:relative;z-index: 2" v-loading.lock="depositWithdrawalDataLoading">-->
    <!--          <iq-card class="iq-card-block iq-card-stretch iq-card-height" style="margin-bottom: 0;">-->
    <!--            <template v-slot:headerTitle>-->
    <!--              <h5 class="card-title" style="font-size: 16px; color: #495eeb">-->
    <!--                {{ $t('dashboard1.depositAndWithdrawal') }}</h5>-->
    <!--            </template>-->
    <!--            <template v-slot:headerAction>-->
    <!--              <b-dropdown class="hidden-md-and-down" id="dropdownMenuButton1" right variant="none"-->
    <!--                          data-toggle="dropdown">-->
    <!--                <template v-slot:button-content>-->
    <!--                  <span class="text-primary"><i class="ri-more-fill"></i></span>-->
    <!--                </template>-->
    <!--                <b-dropdown-item @click="getDepositWithdrawalHistoryListDesktop(0)"-->
    <!--                                 v-viewpoint.click="{ message: `[Dashboard] Click deposit and withdrawal details` }">-->
    <!--                  <i class="ri-eye-fill mr-2"></i>{{ $t('dropdown.view') }}-->
    <!--                </b-dropdown-item>-->
    <!--              </b-dropdown>-->
    <!--              <i-->
    <!--                @click="mobileDepositWithdrawalHistoryShow = true;getDepositWithdrawalHistoryListMobile(0)"-->
    <!--                class="el-icon-menu hidden-lg-and-up"-->
    <!--                v-viewpoint.click="{ message: `[Dashboard] Click deposit and withdrawal details` }"></i>-->
    <!--            </template>-->
    <!--            <template v-slot:body>-->
    <!--              <div class="deposit">-->
    <!--                <ul v-if="depositWithdrawalData.length !== 0 && !depositWithdrawalDataLoading"-->
    <!--                    class="suggestions-lists m-0 p-0">-->
    <!--                  <li v-for="(invoice,index) in depositWithdrawalData" :key="index"-->
    <!--                      :class="'d-flex align-items-center ' + invoice.spaces">-->
    <!--                    <div v-if="invoice.type === 'Deposit'" :class="'profile-icon iq-bg-' + invoice.color"-->
    <!--                         :style="{ color: '#00DF82 !important', background: `rgba(3, 255, 150, 0.3) !important` }">-->
    <!--                      <span><i class="ri-arrow-right-up-line"></i></span>-->
    <!--                    </div>-->
    <!--                    <div v-else :class="'profile-icon iq-bg-' + invoice.color">-->
    <!--                      <span><i class="ri-arrow-right-down-line"></i></span>-->
    <!--                    </div>-->
    <!--                    <div class="media-support-info ml-3">-->
    <!--                      <h6 :style="{ color: invoice.type === 'Deposit' ? '#00DF82' : '#495EEB' }">-->
    <!--                        {{ $t('dashboard1.type.' + invoice.type) }}</h6>-->
    <!--                      <p class="mb-0 fontsize-sm">-->
    <!--                        &lt;!&ndash;                        :class="'text-' + invoice.color"&ndash;&gt;-->
    <!--                        <span>{{ invoice.login }}</span>-->
    <!--                      </p>-->
    <!--                    </div>-->
    <!--                    <div class="media-support-amount ml-3">-->
    <!--                      <h6>-->
    <!--                        <span :class="'text-secondary'"></span>-->
    <!--                        <b :style="{ color: invoice.type === 'Deposit' ? '#00DF82' : '#495EEB', fontWeight: 400 }">-->
    <!--                          {{ invoice.currency }}&nbsp;<span>{{ invoice.profit }}</span>-->
    <!--                        </b>-->
    <!--                      </h6>-->
    <!--                      <p class="mb-0 d-flex justify-content-end">{{ invoice.time }}</p>-->
    <!--                    </div>-->
    <!--                  </li>-->
    <!--                </ul>-->
    <!--                <el-empty v-if="depositWithdrawalData.length === 0 && !depositWithdrawalDataLoading"></el-empty>-->
    <!--              </div>-->
    <!--            </template>-->
    <!--          </iq-card>-->
    <!--        </div>-->
    <!--      </b-col>-->
    <!--    </b-row>-->

    <!--    <b-row>-->
    <!--      <b-col sm="12" lg="8">-->
    <!--        <div style="position:relative;z-index: 2" v-loading.lock="renderColumnChart">-->
    <!--          <iq-card style="height: 400px" class-name="iq-card-block iq-card-stretch iq-card-height overflow-hidden">-->
    <!--            <template v-slot:headerTitle>-->
    <!--              <h5 class="card-title">{{ $t('dashboard1.winningOrders') }}</h5>-->
    <!--            </template>-->
    <!--            <template v-slot:headerAction>-->
    <!--              <el-date-picker-->
    <!--                v-model="dateTime"-->
    <!--                type="daterange"-->
    <!--                @change="getColumnChartData"-->
    <!--                :clearable="false"-->
    <!--                format="yyyy-MM-dd"-->
    <!--                value-format="yyyy-MM-dd"-->
    <!--                range-separator="to"-->
    <!--                start-placeholder="Start Time"-->
    <!--                end-placeholder="End Time"-->
    <!--                class="hidden-sm-and-down">-->
    <!--              </el-date-picker>-->
    <!--              <MobileDateTimePicker icon-name="el-icon-date" @confirm="getColumnChartDataMobile"-->
    <!--                                    class="hidden-md-and-up"></MobileDateTimePicker>-->
    <!--              &lt;!&ndash;              <div v-if="screenWidth < 720">&ndash;&gt;-->
    <!--              &lt;!&ndash;                <i style="font-size: calc(1rem + 0.1vw)" class="el-icon-date" @click="showCalendar = true"></i>&ndash;&gt;-->
    <!--              &lt;!&ndash;              </div>&ndash;&gt;-->

    <!--            </template>-->
    <!--            <template v-slot:body>-->
    <!--              <div style="min-height: 300px;">-->
    <!--                <ApexChart v-if="chart1.xaxis.categories.length !== 0 && !renderColumnChart" element="home-chart-02"-->
    <!--                           v-bind:chartOption="chart1" style="min-height: 300px;"/>-->
    <!--                <el-empty v-if="chart1.xaxis.categories.length === 0 && !renderColumnChart"-->
    <!--                          :description="$t('dashboard1.winningOrdersEmpty')"></el-empty>-->
    <!--              </div>-->

    <!--            </template>-->
    <!--          </iq-card>-->
    <!--        </div>-->
    <!--      </b-col>-->
    <!--      <b-col sm="12" lg="4">-->
    <!--                <div style="height: 400px; margin-bottom: 10px; position:relative;z-index: 2" v-loading.lock="isDestroyed">-->
    <!--                  <iq-card class=" iq-card-block iq-card-stretch " style="height: 100%">-->
    <!--                    <template v-slot:headerTitle>-->
    <!--                      <h5 class="card-title">{{ $t('dashboard1.marketSentiment') }}</h5>-->
    <!--                    </template>-->
    <!--                    <template v-slot:headerAction>-->
    <!--                      <el-select class="hidden-md-and-down" v-model="selectValue" @change="selectSymbol">-->
    <!--                        <el-option v-for="(item,index) in marketSentiment" :key="index" :label="index" :value="index">-->
    <!--                        </el-option>-->
    <!--                      </el-select>-->
    <!--                      <wd-picker class="hidden-lg-and-up" @confirm="selectSymbol" :columns="marketSentimentMobile"-->
    <!--                                 v-model="selectValue">-->
    <!--                        <i class="fas fa-coins"></i>-->
    <!--                      </wd-picker>-->
    <!--                    </template>-->
    <!--                    <template v-slot:body>-->
    <!--                      <div style="width: 100%;">-->
    <!--                        <MyChart class="echarts4" ref="myChart" type="gauge-chart" style="margin: auto;"/>-->
    <!--                      </div>-->
    <!--                    </template>-->
    <!--                  </iq-card>-->
    <!--                </div>-->
    <!--      </b-col>-->
    <!--    </b-row>-->

    <el-dialog :visible.sync="depositWithdrawalDataAllDialog" width="60%" @close="closeDepositWithdrawalDialog">
      <div slot="title" style="font-size: 18px;">
        {{ $t('dashboard1.deposit_withdrawal_detail') }}
      </div>
      <div v-loading="depositWithdrawalDataAllLoading" style="overflow-x: auto; height: 500px">
        <ul class="suggestions-lists m-0 p-0">
          <li v-for="(invoice,index) in depositWithdrawalDataAll" :key="index"
              :class="'d-flex  align-items-center ' + invoice.spaces">
            <div v-if="invoice.type === 'Deposit'" :class="'profile-icon iq-bg-' + invoice.color">
              <span>
                <i class="ri-arrow-right-up-line"></i>
              </span>
            </div>
            <div v-else :class="'profile-icon iq-bg-' + invoice.color"><span>
                <i class="ri-arrow-right-down-line"></i></span>
            </div>
            <div class="media-support-info ml-3">
              <h6>{{ invoice.comment }}</h6>
              <p class="mb-0 fontsize-sm"><span :class="'text-' + invoice.color">{{ invoice.login }}</span></p>
            </div>
            <div class="media-support-amount ml-3">
              <h6><span :class="'text-secondary'"></span><b> {{ invoice.profit }} {{ invoice.currency }}</b></h6>
              <p class="mb-0 d-flex justify-content-end">{{ invoice.time }}</p>
            </div>
          </li>
        </ul>
      </div>
    </el-dialog>
    <wd-popup v-model="mobileDepositWithdrawalHistoryShow" position="bottom" closable
              style="height: 80%; width: calc(100% - 10px); overflow: hidden">
      <h5 style="padding: 15px; background-color: #e6e7fb;">{{ $t('dashboard1.deposit_withdrawal_detail') }}</h5>
      <div style="height: 90%;overflow-y: auto">
        <div id="depositWithdrawalHistory" v-loading="depositWithdrawalDataAllLoading"
             style="height: 100%;margin: 15px">
          <ul class="suggestions-lists m-0 p-0">
            <li v-for="(invoice,index) in depositWithdrawalDataAll" :key="index"
                :class="'d-flex  align-items-center ' + invoice.spaces">
              <div v-if="invoice.type === 'Deposit'" :class="'profile-icon iq-bg-' + invoice.color"><span>
                <i class="ri-arrow-right-up-line"></i></span>
              </div>
              <div v-else :class="'profile-icon iq-bg-' + invoice.color"><span>
                <i class="ri-arrow-right-down-line"></i></span>
              </div>
              <div class="media-support-info ml-3">
                <h6>{{ invoice.comment.substring(0, 10) }}...</h6>
                <p class="mb-0 fontsize-sm"><span :class="'text-' + invoice.color">{{ invoice.login }}</span></p>
              </div>
              <div class="media-support-amount ml-3">
                <h6><span :class="'text-secondary'"></span><b> {{ invoice.profit }} {{ invoice.currency }}</b></h6>
                <p class="mb-0 d-flex justify-content-end">{{ invoice.time }}</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </wd-popup>
    <!--  日期选择  -->
    <!--    <wd-popup v-model="showCalendar" position="bottom" :style="{ 'height': '400px', 'overflow': 'hidden' }">
        <div style="display: flex; justify-content: space-between; padding: 10px 20px; font-weight: 700; color: #656568">
          <div @click="showCalendar = false">{{ $t('dashboard1.dropdown_cancel') }}</div>
          <div style="color: #7f8dfa;" @click="changeTimeDone">{{ $t('dashboard1.dropdown_done') }}</div>
        </div>
        <div>
          <wd-calendar-view
            type="datetime"
            :max-range="60"
            show-panel-title
            v-model="winningOrdersTime"
            :max-date="new Date()"
            :min-date="new Date(2023, 0, 1)"/>
        </div>
      </wd-popup>-->
    <el-dialog v-if="accounDetailsActive" :title="$t('dashboard1.account_details')" :visible.sync="accounDetailsActive"
               :width="dialogWidth"
               ref="openDialog"
               @close="closeAccountDetails">
      <tab-nav :tabs="true" id="myTab-1">
        <tab-nav-items :active="true" id="home-tab" ariaControls="Position" role="tab" :ariaSelected="true"
                       :title="$t('dashboard1.position')"
                       v-viewpoint.click="{ message: `[Dashboard] Click trade history change tab to position` }"/>
        <tab-nav-items :active="false" id="profile-tab" ariaControls="Closing" role="tab" :ariaSelected="false"
                       :title="$t('dashboard1.history')"
                       v-viewpoint.click="{ message: `[Dashboard] Click trade history change tab to History` }"/>
      </tab-nav>
      <tab-content id="myTabContent">
        <tab-content-item :active="true" id="Position" aria-labelled-by="home-tab">
          <AccountDetailsPosition ref="positionTable"></AccountDetailsPosition>
        </tab-content-item>
        <tab-content-item :active="false" id="Closing" aria-labelled-by="profile-tab">
          <AccountDetailsClosing ref="closingTable"></AccountDetailsClosing>
        </tab-content-item>
      </tab-content>
    </el-dialog>

    <el-dialog
      :visible.sync="securityDialogShow"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :width="screenWidth > 720 ? '50%' : '80%'"
      @close="closeSecurityDialog"
    >
      <template #title>
        <h3 style="text-align: center">{{ $t('securityDialog.title') }}</h3>
        <div class="security-dialog-content">{{ $t('securityDialog.content') }}</div>
        <div
          class="security-dialog-button"
          @click="gotoSecurity"
          v-viewpoint.click="{ message: `[Dashboard] Click go to security` }">
          {{ $t('securityDialog.button') }}
        </div>
      </template>
    </el-dialog>

    <el-dialog
      class="dialog"
      :visible.sync="questionnaireDialogVisible"
    >
      <img v-if="$i18n.locale === 'ja_JP'" src="@/assets/images/questionnaire/img-jp.png" alt=""
           @click="goToQuestionnaire">
      <img v-else src="@/assets/images/questionnaire/img-en.png" alt="" @click="goToQuestionnaire">
    </el-dialog>


    <!--  up600 promotion  -->
    <el-dialog :width="screenWidth > 720 ? '30%' : '80%'" :visible.sync="up600Show">

      <div style="display: flex; justify-content: center; align-items: center; flex-direction: column; gap: 30px;">

        <el-checkbox v-model="updateCheck">
          <div v-html="$t('promotion.up600.hint.content')"></div>
        </el-checkbox>

        <el-select size="small" v-model="up600Value" :placeholder="$t('sidebar.select')" style="width: 66%">
          <el-option
            v-for="item in accountNameLoginList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>

      <div style="display: flex;justify-content: center" slot="footer">
        <el-button size="small" :disabled="up600Loading" @click="up600Show = false">
          {{ this.$t('promotion.up600.hint.cancel') }}
        </el-button>
        <el-button size="small" type="primary" :loading="up600Loading" @click="joinNowHandle">
          {{ this.$t('promotion.up600.hint.confirm') }}
        </el-button>
      </div>
    </el-dialog>

    <!--  christmas promotion  -->
<!--    <el-dialog
      class="christmasDialog"
      :close-on-click-modal="false"
      :width="screenWidth > 720 ? '30%' : '90%'"
      :visible.sync="christmasShow"
      :show-close="false"
      top="25vh"
      v-if="JSON.parse(getLocal('user')).country.includes('Japan') || JSON.parse(getLocal('user')).nationality.includes('Japan')">
      <div style="position: relative; overflow: hidden; border-radius: 10px">
        <div style="z-index: 1">
          <a :href="$i18n.locale === 'ja_JP' ? 'https://myfxmarkets.com/ja/promotion/christmas2024/' : 'https://myfxmarkets.com/promotion/christmas2024/'" target="_blank">
            <img style="width: 100%; height: 100%;" src="/img/promotion/christmas-dialog.gif" alt="christmas">
          </a>
        </div>
        <div style="position: absolute; top: 8px; right: 8px; color: #000000;font-weight: bold; z-index: 10; cursor: pointer;" @click="christmasShow = false">
          <i class="el-icon-close" style="font-size: 20px"></i>
        </div>
      </div>
    </el-dialog>-->

    <el-dialog
      class="christmasDialog"
      :close-on-click-modal="false"
      :width="screenWidth > 720 ? '30%' : '90%'"
      :visible.sync="cashback2025May"
      :show-close="false"
      top="25vh">
      <div style="position: relative; overflow: hidden; border-radius: 10px">
        <div style="z-index: 1">
          <router-link to="/promotion/cashback-2025-may" v-if="$i18n.locale === 'ja_JP'">
            <img style="width: 100%; height: 100%; cursor: pointer" src="/img/promotion/cashback2025MayDialog-jp.gif" alt="cashback2025May">
          </router-link>
          <router-link to="/promotion/cashback-2025-may" v-else >
            <img style="width: 100%; height: 100%; cursor: pointer" src="/img/promotion/cashback2025MayDialog-en.gif" alt="cashback2025May">
          </router-link>
        </div>
        <div style="position: absolute; top: 8px; right: 8px; color: #000000;font-weight: bold; z-index: 10; cursor: pointer;" @click="closeCashback2025May">
          <i class="el-icon-close" style="font-size: 20px; font-weight: bold"></i>
        </div>
      </div>
    </el-dialog>

  </b-container>
</template>
<script>
import { core } from '@/config/pluginInit'
import MyChart from '@/components/core/charts/MyChart'
import ApexChart from '@/components/core/charts/ApexChart'
import ChartistChart from '@/components/core/charts/ChartistChart'
import ChartistChart2 from '@/components/core/charts/ChartistChart2'
import { getLocal } from '@/Utils/authLocalStorage'
import {
  getBalanceData,
  getChartistChartData,
  getColumnChartData,
  getCurrencyPosition,
  getDepositWithdrawalData,
  getNumberPanelData
} from '@/services/dashboard'
import { Message } from 'element-ui'
import { fetchTradingAccount } from '@/services/account'
import MobilePicker from '@/components/MyfxComponent/MobileComponent/MobilePicker'
import MobileDateTimePicker from '@/components/MyfxComponent/MobileComponent/MobileDateTimePicker'
import Counter from '@/components/core/counter/Counter'
import $i18n_ from 'wot-design/lib/locale'
import i18n from '@/i18n'
import { isShowQuestionnaire, participateQuestionnaire } from '@/services/checkQuestionnaire'
import {checkPromotionAuth, getCashback2025MayJoinedAccount, isRegisterChristmas} from '@/services/promotion'
import { getWelcomeBonus, joinNow } from '@/services/promotion'
import { Account, TransactionHistory } from '@/services/allExport'

export default {
  name: 'Dashboard1',
  components: {
    Counter,
    MobileDateTimePicker,
    MobilePicker,
    ApexChart,
    ChartistChart,
    ChartistChart2,
    MyChart,
    AccountDetailsPosition: () => import('@/views/Dashboards/AccountDetailsCMD/AccountDetailsPosition'),
    AccountDetailsClosing: () => import('@/views/Dashboards/AccountDetailsCMD/AccountDetailsClosing')
  },
  data () {
    return {
      securityDialogShow: false,
      mtType: [
        { id: 1, name: 'MT4' },
        { id: 2, name: 'MT5' }
      ],
      mtActive: 1,
      winningOrdersTime: [],
      showCalendar: false,
      selectLogin: '',
      selectServer: '',
      defaultAccount: '',
      changeAccount: '',
      selectMobileShow: false,
      selectValueMobile: '1213',
      columns: [],
      screenWidth: 0,
      userInfo: {
        name: 'test111111111111',
        phone: '************',
        email: '<EMAIL>'
      },
      generalInformation: {
        name: '',
        emailAddress: '',
        phoneNumber: ''
      },
      selValue: '',
      balanceActive: false,
      accounDetailsActive: false,
      accounDetailsBalanceActive: true,
      accounDetailsBalanceLoading: false,
      serverActive: true,
      tradingAccountMT4List: [],
      tradingAccountMT5List: [],
      mobileSelectionConfig: {},
      accountLoginServer: {},
      numberPanel: {
        accountDetails: '0.00',
        announcement: 0,
        promotion: 0
      },
      marketSentiment: [],
      marketSentimentMobile: [],
      selectValue: '',
      dateTime: [],
      chDateTime: [],
      renderColumnChart: false,
      isDestroyed: false,
      chart1: {
        series: [
          /* {
                    name: 'Net Profit',
                    data: [44, 55, 57, 56, 61, 58, 63, 60, 66]
                  },  */
          {
            name: 'Order',
            data: []
          }
        ],
        chart: {
          type: 'bar',
          height: 300,
          toolbar: {
            show: false
          }
        },
        colors: ['#495EEB'],
        plotOptions: {
          bar: {
            horizontal: !1,
            columnWidth: '55%',
            borderRadius: 4
          }
        },
        dataLabels: {
          enabled: !1
        },
        stroke: {
          show: !0,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: []
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '14px'
            }
          }
        },
        fill: {
          opacity: 1
        }
      },
      chart4: {
        type: 'Line',
        id: 'home-chart-01',
        data: {
          labels: [],
          series: [
            []
          ]
        },
        options: {
          height: 220,
          width: '97%',
          fullWidth: false
        }

      },
      depositWithdrawalData: [],
      depositWithdrawalDataAll: [],
      depositWithdrawal: {
        name: '',
        currency: '',
        profit: '',
        time: '',
        type: '',
        color: '',
        spaces: ''
      },
      tradingAccountList: [],
      accountNameLoginList: [],
      selectedTradingAccount: {},
      depositWithdrawalDataAllDialog: false,
      depositWithdrawalDataLoading: false,
      depositWithdrawalDataAllLoading: false,
      chartistChartLoading: false,
      tradingAccountLoading: false,
      mobileDepositWithdrawalHistoryShow: false,
      dialogWidth: '',
      questionnaireDialogVisible: false,
      isPromotionUrl: false,

      up600Status: -1,
      up600Loading: false,
      up600Show: false,
      up600Value: '',
      upCover: '',
      updateCheck: false,
      welcomeLoading: false,
      christmasShow: false,
      cashback2025May: false,
      joinedAccountList: [],
      joinLoading: false,
      cashback2025MayLoading: false,
      cashback2025MayVisibleCountry: ['Indonesia', 'India', 'Malaysia', 'Thailand', 'Vietnam']
    }
  },
  async created () {
    this.getCashback2025MayJoinedAccount()
    this.checkCashback2025May()
    this.checkChristmas()

    // this.getTradingAccountsUp600()
    this.getWelcomeBonus()

    window.addEventListener('resize', this.updateWindowDimensions)
    this.updateWindowDimensions() // 初始化
    // await this.checkPromotionAuthHandle()
    this.showDialog()
    this.securityDialogShow = localStorage.HaveSecurityQuestion === '0'

    this.getGeneralInformation()
    this.setDialogWidth()
    this.tradingAccountLoading = true
    this.renderColumnChart = true
    this.chartistChartLoading = true
    this.depositWithdrawalDataLoading = true
    this.isDestroyed = true
    await this.getTradingAccounts()
    await this.setMobileTradingAccountSelection(this.tradingAccountList)
    await this.defaultDate()
    this.getNumberPanelData()
    await this.getColumnChartData()
    await this.getChartistChartData()
    this.getDepositWithdrawalHistoryList(1)
    this.getCurrencyPositionData()
  },
  computed: {
    $i18n_ () {
      return $i18n_
    },
    format () {
      return (percentage, successPercentage) => {
        return `BUY / SELL`
      }
    },
    cashback2025MayShow () {
      const userInfo = JSON.parse(getLocal('user'))
      return this.cashback2025MayVisibleCountry.includes(userInfo.country) ||
        this.cashback2025MayVisibleCountry.includes(userInfo.nationality) ||
        userInfo.accounttype === 'Company'
    }
  },
  mounted () {
    core.index()
    /* this.accounDetailsActive = true
    this.$refs.openDialog.rendered = true
    this.accounDetailsActive = false */
    window.onresize = () => {
      return (() => {
        this.setDialogWidth()
      })()
    }
  },
  destroyed () {
    window.removeEventListener('resize', this.updateWindowDimensions)
  },
  methods: {
    /**
     * @description 获取参加过cashback2025May的交易账号
     */
    async getCashback2025MayJoinedAccount () {
      try {
        this.cashback2025MayLoading = true
        const { data } = await getCashback2025MayJoinedAccount()
        this.joinedAccountList = data
        this.cashback2025MayLoading = false
      } catch (e) {
        console.log(e)
        this.cashback2025MayLoading = false
      }
    },
    /**
     *@description cashback 2025 may弹窗是否可见
     */
    checkCashback2025May () {
      if (this.cashback2025MayShow) return
      const cashback2025MayKey = JSON.parse(window.localStorage.getItem('cashback2025May'))
      if (cashback2025MayKey) {
        if (cashback2025MayKey === '1') {
          this.cashback2025May = false
        }
      } else {
        localStorage.setItem('cashback2025May', JSON.stringify(0))
        this.cashback2025May = new Date() < new Date('2025-07-04 23:59:59')
      }
    },
    closeCashback2025May () {
      localStorage.setItem('cashback2025May', JSON.stringify(1))
      this.cashback2025May = false
    },
    /**
     *@description 未注册过显示christmas弹窗
     */
    async checkChristmas () {
      try {
        const { code, active } = await isRegisterChristmas()
        console.log('active', active)

        if (code === 200) {
          this.christmasShow = active === 0
        }
      } catch (e) {}
    },

    getLocal,

    async getTradingAccountsUp600 () {
      this.accountLoading = true
      const email = JSON.parse(getLocal('user')).email
      const myGuid = JSON.parse(getLocal('user')).myguid
      await Account.fetchTransactionsTradingAccount(myGuid, email).then(res => {
        const tradingAccountList = res.data
        const tradingAccount = []
        tradingAccountList.forEach(item => {
          if (Number(item.status) >= 0) {
            tradingAccount.push(item)
          }
        })
        this.$store.commit('commonSet', {
          keys: ['tradingAccountList'],
          data: tradingAccount
        })
      })
      await TransactionHistory.getEWalletAmount(myGuid).then(res => {
        const sumAccountList = res.data
        this.$store.commit('commonSet', {
          keys: ['sumAccountList'],
          data: sumAccountList
        })
      })

      this.getTradingAccount()

    },

    getTradingAccount () {
      this.accountNameLoginList = []
      this.tradingAccountList = this.$store.getters.commonGet(['tradingAccountList'])
      this.tradingAccountList.forEach(item => {

        item.accountType = item.accountType || ''
        if (!item.mt4group?.toLowerCase()?.includes('rebate') &&
          !item.mt4group?.toLowerCase()?.includes('-pf-') &&
          !item.accountType?.toLowerCase()?.includes('ctrader') &&
          !item.mt4group?.toLowerCase()?.includes('disabled') &&
          !item.mt4group?.toLowerCase()?.includes('-pf') &&
          !item.mt4group?.toLowerCase()?.includes('removed') /* &&
          !item.accountType.toLowerCase().includes('master') */) {
          if ((item.mt4group?.includes('MAM') || item.mt4group?.includes('PAMM'))) {
            if (item.mt4group?.toUpperCase()?.includes('-SUB-') ||
              item.mt4group?.toUpperCase()?.includes('-CLT') ||
              item.mt4group?.toUpperCase()?.includes('-CLIENT')) {
              // for testing purpose.)
              const accountNameLogin = item.login
              const accountName = item.accountName
              this.accountNameLoginList.push({
                label: accountNameLogin + '-' + accountName,
                value: item.myguid
              })
            }
          } else {
            const accountNameLogin = item.login
            const accountName = item.accountName
            this.accountNameLoginList.push({
              label: accountNameLogin + '-' + accountName,
              value: item.myguid
            })
          }
        }
      })
    },


    /**
     * @description 加入活动
     */
    async joinNowHandle () {
      try {
        if (!this.up600Value) return
        this.up600Loading = true
        const result = await joinNow({
          coGuid: JSON.parse(getLocal('user')).myguid,
          mt4AccountGuid: this.up600Value
        })
        if (result.code == 200) {
          console.log(result)
        } else {

        }
        this.up600Loading = false
        this.up600Show = false
        this.getWelcomeBonus()
      } catch (err) {
        this.up600Loading = false
        console.log(err)
      }
    },

    /**
     * @description 活动信息
     */
    async getWelcomeBonus () {
      try {
        this.welcomeLoading = true
        const result = await getWelcomeBonus({ coGuid: JSON.parse(getLocal('user')).myguid })
        if (result.code == 200) {
          this.up600Status = result.data.promotionStatus
          this.upCover = this.up600Status == 1 ? 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/campaign/welcome-bonus/up600_4.png' : 'https://myfx-static-assets.s3.us-west-2.amazonaws.com/campaign/welcome-bonus/up600_1.png'
        } else {

        }
        this.welcomeLoading = false
      } catch (err) {
        this.welcomeLoading = false
        console.log(err)
      }
    },

    async showPopup () {
      try {
        if (this.up600Status > 0) return

        this.$router.push('/promotion/promotionInfo-up600')

        // if (JSON.parse(getLocal('user')).isok == 0) {
        //   await this.$confirm(
        //     this.$t('promotion.up600.isok_no'),
        //     {
        //       showCancelButton: false,
        //       confirmButtonText: this.$t('promotion.up600.hint.confirm')
        //     }
        //   )
        //   return
        // }
        //
        // this.up600Show = true

        // await this.$confirm(
        //   this.$t('promotion.up600.hint.content'),
        //   {
        //     dangerouslyUseHTMLString: true,
        //     cancelButtonText: this.$t('promotion.up600.hint.cancel'),
        //     confirmButtonText: this.$t('promotion.up600.hint.confirm')
        //   }
        // )
      } catch (err) {
        // this.$router.push('/')
      }
    },

    // 是否显示promotion
    async checkPromotionAuthHandle () {
      try {
        const { code, promotionStatus } = await checkPromotionAuth({
          coguid: JSON.parse(getLocal('user')).myguid,
          promotionguid: 'ba3a9cf22ed011ef9bdf024d253502b9'
        })
        if (code == 200) {
          if (promotionStatus) {
            //isPromotionUrl = true,跳转到promotion,否则跳转到/siteNews
            this.isPromotionUrl = true
          }
        }
      } catch (err) {
        console.log(err)
      }
    },

    //是否展示问卷调查弹窗
    showDialog () {
      const currentDate = new Date()
      const endDate = new Date('2024-07-31 23:59:59')
      const myguid = JSON.parse(getLocal('user')).myguid

      if (currentDate < endDate) {
        if (this.$i18n.locale === 'en_US' || this.$i18n.locale === 'ja_JP') {
          isShowQuestionnaire(myguid).then(res => {
            //isJoin:true 参加过
            this.questionnaireDialogVisible = !res.isJoin
          })
        } else {
          this.questionnaireDialogVisible = false
        }
      } else {
        this.questionnaireDialogVisible = false
      }
    },

    // 填写问卷调查
    goToQuestionnaire () {
      const myguid = JSON.parse(getLocal('user')).myguid

      participateQuestionnaire(myguid).then(res => {
        if (this.$i18n.locale === 'en_US') {
          window.open('https://forms.gle/ekEd663ShhASUtcdA', '_blank')
        } else {
          window.open('https://forms.gle/iA5vmfyRW5kSy1Ur8', '_blank')
        }
        this.questionnaireDialogVisible = false
      })
    },

    closeAccountDetails () {
      this.$vp.add({ message: '[Dashboard] Click close trade history dialog' })
    },
    closeDepositWithdrawalDialog () {
      this.$vp.add({ message: '[Dashboard] Click close deposit withdrawal details dialog' })
    },
    closeSecurityDialog () {
      this.$vp.add({ message: '[Dashboard] Click close security dialog' })
    },

    /**
     * @description Goto Security
     * <AUTHOR>
     */
    gotoSecurity () {
      this.$router.push({ path: '/resetSecret' })
      this.securityDialogShow = false
    },

    /**
     * @description MT Type Change
     * <AUTHOR>
     */
    mtTypeChange (id) {
      this.mtActive = id
      this.serverClick()
    },

    updateWindowDimensions () {
      this.screenWidth = window.innerWidth
    },
    getGeneralInformation () {
      const user = JSON.parse(getLocal('user'))
      this.generalInformation.name = user.firstname + '    ' + user.lastname
      this.generalInformation.emailAddress = user.email
      this.generalInformation.phoneNumber = user.tel

      if (user.accounttype === 'Company') {
        this.generalInformation.name = user.companyname
      }
    },
    setDialogWidth () {
      const val = document.body.clientWidth
      const def = 1000 // 默认宽度
      if (val < def) {
        this.dialogWidth = '100%'
      } else {
        this.dialogWidth = def + 'px'
      }
    },
    defaultDate () {
      // 获取新的时间
      const date = new Date()
      // 获取当前时间的年份转为字符串
      const year = date.getFullYear().toString()
      // 获取月份，由于月份从0开始，此处要加1，判断是否小于10，如果是在字符串前面拼接'0'
      const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      // 获取天，判断是否小于10，如果是在字符串前面拼接'0'
      const da = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()

      const hour = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      const minute = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      const second = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()

      // 字符串拼接，开始时间，结束时间
      const end = year + '-' + month + '-' + da // 当天
      const beg = year + '-' + month + '-01'// 当月第一天
      this.dateTime = [beg, end] // 将值设置给插件绑定的数据
      this.chDateTime = [beg, end]

      this.winningOrdersTime = [new Date(this.dateTime[0]), new Date(this.dateTime[1])]
      console.log('time', this.winningOrdersTime)
    },
    getNumberPanelData () {
      const myguid = JSON.parse(getLocal('user')).myguid
      const email = JSON.parse(getLocal('user')).email
      getNumberPanelData(myguid, email).then(res => {
        this.numberPanel.announcement = res.data.announcement
        this.numberPanel.promotion = res.data.promotion
      })
    },
    async getColumnChartData () {
      if (this.dateTime == null) {
        console.log(this.dateTime)
        Message.error('Please select correct dateTime')
        this.chart1.xaxis.categories.length = 0
        // this.renderColumnChart = false
        return
      }
      this.renderColumnChart = true
      const myguid = JSON.parse(getLocal('user')).myguid
      const email = JSON.parse(getLocal('user')).email
      const startDate = this.dateTime[0]
      const endDate = this.dateTime[1]
      await getColumnChartData({
        myguid: myguid,
        email: email,
        startTime: startDate,
        endTime: endDate
      }).then(res => {
        console.log(res.data)
        if (res.data === undefined || res.data.length === 0) {
          console.log(res.data)
          this.renderColumnChart = false
          return
        }
        console.log('99999', res)
        this.chart1.series[0].data = []
        this.chart1.xaxis.categories = []
        this.chart1.plotOptions.bar.columnWidth = ''
        const data = res.data
        const categories = []
        if (data.length !== 0) {
          // 动态分配条形统计图宽度
          if (data.length <= 9) {
            this.chart1.plotOptions.bar.columnWidth = (data.length * 3).toString() + '%'
          } else {
            this.chart1.plotOptions.bar.columnWidth = '57%'
          }
          // symbol数据归档
          data.forEach(item => {
            categories.push(item.symbol)
            this.chart1.series[0].data.push(item.symbolCount)
          })
        }
        this.chart1.xaxis.categories = categories
        this.renderColumnChart = false
      })
    },
    getColumnChartDataMobile (val) {
      this.dateTime = val
      this.getColumnChartData()
    },
    getCurrencyPositionData () {
      this.isDestroyed = true
      getCurrencyPosition().then(res => {
        this.marketSentiment = res.data
        for (const key in this.marketSentiment) {
          this.marketSentimentMobile.push(key)
        }
        const resultProcess = []

        for (const item in this.marketSentiment) {
          resultProcess.push(this.marketSentiment[item])
          this.selectValue = item
          break
        }
        this.$refs.myChart?.gaugeChart(resultProcess)
      })
      this.isDestroyed = false
    },
    selectSymbol (v) {
      this.isDestroyed = true
      const position = []
      for (const item in this.marketSentiment) {
        if (item == v) {
          position.push(this.marketSentiment[item])
          break
        }
      }
      this.$refs.myChart?.gaugeChart(position)
      this.isDestroyed = false
      this.$vp.add({ message: `[Dashboard] click market sentiment ${ v }` })
    },
    getDepositWithdrawalHistoryList (type) {
      const myguid = JSON.parse(getLocal('user')).myguid
      const email = JSON.parse(getLocal('user')).email
      if (type === 1) {
        this.depositWithdrawalData = []
        this.depositWithdrawalDataLoading = true
      } else {
        this.depositWithdrawalDataAll = []
      }
      getDepositWithdrawalData(myguid, email, type).then(res => {
        if (type === 1) {
          if (res.data === undefined || res.data.length === 0) {
            console.log(res.data)
            this.depositWithdrawalDataLoading = false
            return
          }
          const data = res.data
          data.forEach(item => {
            const depositWithdrawal = {}
            depositWithdrawal.profit = item.profit
            depositWithdrawal.name = item.name
            depositWithdrawal.time = item.time
            depositWithdrawal.currency = item.currency
            depositWithdrawal.type = item.type
            depositWithdrawal.login = item.login
            depositWithdrawal.comment = item.comment
            if (item.type === 'Deposit') {
              depositWithdrawal.color = 'success'
            } else {
              depositWithdrawal.color = 'primary'
            }
            depositWithdrawal.spaces = 'mb-3'
            this.depositWithdrawalData.push(depositWithdrawal)
          })

          // const target = data[data.length - 1]
          // if (target.type === 'Deposit') {
          //   target.color = 'success'
          // } else {
          //   target.color = 'primary'
          // }
          // target.spaces = 'mb-3'
          // this.depositWithdrawalData.push(target)
          // this.depositWithdrawalData.push(target)

          this.depositWithdrawalDataLoading = false
        } else {
          if (res.data === undefined || res.data.length === 0) {
            console.log(res.data)
            this.depositWithdrawalDataAllLoading = false
            return
          }
          const data = res.data
          data.forEach(item => {
            const depositWithdrawal = {}
            depositWithdrawal.profit = item.profit
            depositWithdrawal.name = item.name
            depositWithdrawal.time = item.time
            depositWithdrawal.currency = item.currency
            depositWithdrawal.type = item.type
            depositWithdrawal.login = item.login
            depositWithdrawal.comment = item.comment
            if (item.type === 'Deposit') {
              depositWithdrawal.color = 'success'
            } else {
              depositWithdrawal.color = 'primary'
            }
            depositWithdrawal.spaces = 'mb-4'
            this.depositWithdrawalDataAll.push(depositWithdrawal)
          })
          this.depositWithdrawalDataAllLoading = false
        }
      })
    },
    getDepositWithdrawalHistoryListMobile (type) {
      this.depositWithdrawalDataAllLoading = true
      this.getDepositWithdrawalHistoryList(type)
    },
    getDepositWithdrawalHistoryListDesktop (type) {
      this.depositWithdrawalDataAllDialog = true
      this.depositWithdrawalDataAllLoading = true
      this.getDepositWithdrawalHistoryList(type)
    },
    async getChartistChartData () {
      this.chartistChartLoading = true
      this.chart4.data.series = []
      this.chart4.data.labels = []
      const myguid = this.selectedTradingAccount?.myguid
      if (!myguid) return this.chartistChartLoading = false
      const email = this.selectedTradingAccount.login
      const startTime = this.chDateTime[0]
      const endTime = this.chDateTime[1]
      await getChartistChartData(myguid, email, startTime, endTime).then(res => {
        const data = res.data
        const seriesList = []
        data.forEach(item => {
          this.chart4.data.labels.push(item.time)
          seriesList.push(item.balance)
        })
        this.chart4.data.series.push(seriesList)
        console.log(this.chart4.data)
        this.chartistChartLoading = false
      }).catch(error => {
        this.chartistChartLoading = false
      })
    },
    getChartistChartDataMobileLogin (val) {
      this.tradingAccountList.forEach(item => {
        if (item.login === val[1]) {
          this.selectedTradingAccount = item
        }
      })
      this.getChartistChartData()
    },
    getChartistChartDataMobileDateTime (val) {
      this.chDateTime = val
      this.getChartistChartData()
    },
    handleChange (value) {
      this.accounDetailsBalanceActive = true
      const targetAccount = this.tradingAccountList.find(v => v.login == value)
      try {
        if (targetAccount) {
          this.defaultDate()
          this.selectedTradingAccount = targetAccount
          this.getChartistChartData()
        }
      } catch (err) {
        console.log(err)
      }

      // console.log(value)
      if (this.accounDetailsBalanceActive) {
        if (this.serverActive) {
          const selectServer = this.tradingAccountMT4List.filter(item => item.login == value)
          if (selectServer.length > 0) {
            this.getBalanceDesktop({ accountLogin: value, server: selectServer[0]?.server })
            this.$vp.add({ message: `[Dashboard] Click MT4 ${ value }` })
          }
        } else {
          const selectServer = this.tradingAccountMT5List.filter(item => item.login == value)
          if (selectServer.length > 0) {
            this.getBalanceDesktop({ accountLogin: value, server: selectServer[0]?.server })
            this.$vp.add({ message: `[Dashboard] Click MT5 ${ value }` })
          }
        }
      } else {
        if (this.serverActive) {
          const selectServer = this.tradingAccountMT4List.filter(item => item.login == value)
          if (selectServer.length > 0) {
            this.getBalanceDesktop({ accountLogin: value, server: selectServer[0]?.server })
            this.$vp.add({ message: `[Dashboard] Click MT4 ${ value }` })
          }
        } else {
          const selectServer = this.tradingAccountMT5List.filter(item => item.login == value)
          if (selectServer.length > 0) {
            this.getBalanceDesktop({ accountLogin: value, server: selectServer[0]?.server })
            this.$vp.add({ message: `[Dashboard] Click MT5 ${ value }` })
          }
        }
      }

      // --------------
    },
    filterValue () {
      const defMT4 = this.tradingAccountMT4List[this.tradingAccountMT4List.length - 1]?.login
      const defMT4Account = this.tradingAccountMT4List[this.tradingAccountMT4List.length - 1]?.accountName
      const defMT4Server = this.tradingAccountMT4List[this.tradingAccountMT4List.length - 1]?.server
      const defMT5 = this.tradingAccountMT5List[this.tradingAccountMT4List.length - 1]?.login
      this.defaultAccount = defMT4 + ' ' + defMT4Account
      if (this.accounDetailsBalanceActive) {
        if (this.serverActive) {
          this.filterValue2(defMT4)
        } else {
          this.filterValue2(defMT5)
        }
      } else {
        if (this.serverActive) {
          this.filterValue2(defMT4)
        } else {
          this.filterValue2(defMT5)
        }
      }
      if (this.screenWidth < 720) {
        if (defMT4 && defMT4Server) {
          getBalanceData(defMT4, defMT4Server, this.selectedTradingAccount.myguid).then(res => {
            console.log('res', res)
            if (!JSON.stringify(res.data) != '{}' && res.data != null) {
              for (const item in res.data) {
                const balance = JSON.stringify(res.data[item])
                if (balance.includes('.')) {
                  const sp = balance.split('.')
                  const l = sp[0].replace(/(?=(?!\b)(\d{3})+$)/g, ',')
                  const r = sp[1]
                  this.numberPanel.accountDetails = `${ l }.${ r }` + ' ' + item
                } else {
                  this.numberPanel.accountDetails = `${ String(balance).replace(/(?=(?!\b)(\d{3})+$)/g, ',') }` + ' ' + item
                }

                // let balanceApend = ''
                // let number = balance.length - 1
                // for (var i = number; i >= 0; i--) {
                //   if (i == balance.length - 1) {
                //     number -= 2
                //   }
                //   if (balance.charAt(i) == '.') {
                //     number -= 3
                //   }
                //   if (i == number && number != 0 && balance.charAt(i) != '.') {
                //     balanceApend = ',' + balance.charAt(i) + balanceApend
                //     number -= 3
                //   } else {
                //     balanceApend = balance.charAt(i) + balanceApend
                //   }
                // }
                // if (item.toLowerCase() != 'jpy'.toLowerCase() && !balanceApend.includes('.')) {
                //   balanceApend = balanceApend + '.00'
                // }
                // this.numberPanel.accountDetails = balanceApend + ' ' + item
              }
            }
            this.accounDetailsBalanceActive = false
            this.accounDetailsBalanceLoading = false
          }).catch(error => {
            this.accounDetailsBalanceLoading = false
          })
        }
      }
    },
    filterValue2 (value) {
      this.handleChange(value)
      this.selValue = value
    },
    async getTradingAccounts () {
      const myguid = JSON.parse(getLocal('user')).myguid
      const email = JSON.parse(getLocal('user')).email
      await fetchTradingAccount(myguid, email).then(res => {
        this.tradingAccountMT4List = []
        this.tradingAccountMT5List = []
        this.tradingAccountList = res.data

        if (!res.data.length) return

        this.setMobileTradingAccountSelection(this.tradingAccountList)
        this.selectedTradingAccount = this.tradingAccountList[0]
        // console.log(this.tradingAccountList)
        for (const item of this.tradingAccountList) {
          // if (item.status !== '-3') {
          if ((item.server + ''.toLocaleLowerCase() == 'us01'.toLocaleLowerCase()) ||
            (item.server + ''.toLocaleLowerCase() == 'us07'.toLocaleLowerCase())) {
            this.tradingAccountMT4List.push(item)
          } else {
            this.tradingAccountMT5List.push(item)
          }
          // }
        }
        this.tradingAccountLoading = false
        this.filterValue()
      })
    },
    getBalance (a, b) {
      this.accounDetailsBalanceActive = true
      this.accounDetailsBalanceLoading = true
      this.numberPanel.accountDetails = '0.00'
      if (this.accountLoginServer.accountLogin != undefined) {
        if (a === undefined) a = this.accountLoginServer.accountLogin
        if (b === undefined) b = this.accountLoginServer.server
        getBalanceData(
          this.screenWidth > 720 ? this.accountLoginServer.accountLogin : a,
          this.screenWidth > 720 ? this.accountLoginServer.server : b,
          this.selectedTradingAccount.myguid
        ).then(res => {
          console.log('res', res)
          if (!JSON.stringify(res.data) != '{}' && res.data != null) {
            for (const item in res.data) {
              const balance = JSON.stringify(res.data[item])
              if (balance.includes('.')) {
                const sp = balance.split('.')
                const l = sp[0].replace(/(?=(?!\b)(\d{3})+$)/g, ',')
                const r = sp[1]
                this.numberPanel.accountDetails = `${ l }.${ r }` + ' ' + item
              } else {
                this.numberPanel.accountDetails = `${ String(balance).replace(/(?=(?!\b)(\d{3})+$)/g, ',') }` + ' ' + item
              }
              // let balanceApend = ''
              // let number = balance.length - 1
              // for (var i = number; i >= 0; i--) {
              //   if (i == balance.length - 1) {
              //     number -= 2
              //   }
              //   if (balance.charAt(i) == '.') {
              //     number -= 3
              //   }
              //   if (i == number && number != 0 && balance.charAt(i) != '.') {
              //     balanceApend = ',' + balance.charAt(i) + balanceApend
              //     number -= 3
              //   } else {
              //     balanceApend = balance.charAt(i) + balanceApend
              //   }
              // }
              // if (item.toLowerCase() != 'jpy'.toLowerCase() && !balanceApend.includes('.')) {
              //   balanceApend = balanceApend + '.00'
              // }
              // this.numberPanel.accountDetails = balanceApend + ' ' + item
            }
          }
          this.accounDetailsBalanceActive = false
          this.accounDetailsBalanceLoading = false
        }).catch(error => {
          this.accounDetailsBalanceLoading = false
        })
      }
    },
    getBalanceDesktop (value) {
      this.accountLoginServer = value
      this.getBalance()
    },
    // onchange for mobile trading account change
    getBalanceMobile (data) {
      const accountServer = {
        accountLogin: data[1],
        server: ''
      }
      this.tradingAccountList.forEach(item => {
        if (item.login === accountServer.accountLogin) {
          accountServer.server = item.server
        }
      })
      this.serverActive = data[0] === 'MT4'
      this.accountLoginServer = accountServer
      this.getBalance()
    },
    setMobileTradingAccountSelection (list) {
      if (!list.length) return

      const mobileSelection = {
        0: [{
          label: 'MT4',
          value: 'MT4'
        }, {
          label: 'MT5',
          value: 'MT5'
        }],
        MT4: [],
        MT5: []
      }
      for (const item of list) {
        if ((item.server + ''.toLocaleLowerCase() == 'us01'.toLocaleLowerCase()) ||
          (item.server + ''.toLocaleLowerCase() == 'us07'.toLocaleLowerCase())) {
          const data = {
            label: item.login + '' + item.accountName,
            value: item.login
          }
          mobileSelection.MT4.push(data)
        } else {
          const data = {
            label: item.login + '' + item.accountName,
            value: item.login
          }
          mobileSelection.MT5.push(data)
        }
      }
      this.mobileSelectionConfig = mobileSelection
    },
    openAccountDetails () {
      this.setDialogWidth()
      if (!this.accountLoginServer.accountLogin) {
        this.$message({
          showClose: true,
          message: 'please select account number',
          type: 'warning'
        })
      } else {
        this.accounDetailsActive = true
        setTimeout(() => {
          this.$refs.positionTable.getPositionOrder(this.accountLoginServer)
          this.$refs.closingTable.getClosingOrder(this.accountLoginServer)
        }, 500)
        setTimeout(() => {
          this.$vp.add({ message: '[Dashboard] Click trade history change tab to position' })
        }, 500)
      }
    },
    serverClick () {
      this.numberPanel.accountDetails = '0.00'
      console.log('details', this.numberPanel.accountDetails)
      this.accounDetailsBalanceActive = true
      this.accountLoginServer.accountLogin = ''
      this.serverActive = !this.serverActive
      // this.accounDetailsBalanceActive = false
      this.selValue = i18n.t('dashboard1.placeholder')
      this.defaultAccount = '--please select--'
    },
    showAccount () {
      this.selectMobileShow = true
      const content = document.querySelector('.isMT')?.innerHTML
      console.log('content', content)
      // if (content === 'MT4') {
      if (this.serverActive) {
        this.columns = []
        this.tradingAccountMT4List.forEach(item => {
          this.columns.push({
            label: item.login + ' ' + item.accountName,
            value: item.login,
            server: item.server
          })
        })
      } else {
        this.columns = []
        this.tradingAccountMT5List.forEach(item => {
          this.columns.push({
            label: item.login + ' ' + item.accountName,
            value: item.login,
            server: item.server
          })
        })
      }
    },
    selectChangeMobile (picker, value, index) {
      console.log(`当前选中项: ${ value }, 下标: ${ index }`)
      console.log(picker.formatColumns)
      this.changeAccount = picker.formatColumns[0][index].label
      this.selectLogin = picker.formatColumns[0][index].value
      this.selectServer = picker.formatColumns[0][index].server
    },
    selectMobileDone () {
      this.selectMobileShow = false
      this.getBalance(this.selectLogin, this.selectServer)
      /* console.log('2', this.tradingAccountMT5List) */
      this.defaultAccount = this.changeAccount
    },
    showCalendarData () {
      this.showCalendar = true
    },
    changeTimeDone () {
      this.showCalendar = false
      this.getChartistChartDataMobileDateTime(this.winningOrdersTime)
      this.getColumnChartDataMobile(this.winningOrdersTime)
    }
  }
}
</script>

<style lang="scss" scoped>
.card-box {
  min-width: 300px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px 20px 20px;
  background-color: #FFFFFF;
  border-radius: 10px;
  //margin-bottom: 20px;
  box-sizing: border-box;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 16px;
}

.account {
  //min-width: 240px;
}

.card-content .title {
  line-height: 40px;
  //color: #6477f9;
  color: #495EEB;
  margin: auto 0;
}

.card-content .title-content {
  line-height: 40px;
  //color: #5f5f63;
  color: black;
  margin: auto 0;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  text-align: center;
  background-color: #495EEB;
  border-radius: 50%;

  img {
    width: 100%;
    height: 100%;
    vertical-align: unset;
  }
}

.select {
  width: 180px;
}

.el-select > .el-input {
  height: 30px;
}

//移动端
.card-box-mobile {
  min-width: 300px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10px 10px 20px;
  background-color: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 12px;
  box-sizing: border-box;
}

.card-box-mobile .card-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-left .title {
  height: 30px;
  //color: #7f8dfa;
  color: #495EEB;
  margin: 0;
  //font-weight: 600;
}

.card-left .title span {
  color: #bbbbbd
}

.card-box-mobile .card-right {
  //width: 40px;
}

.card-right .card-icon {
  //width: 40px;
  //height: 40px;
  //line-height: 40px;
  text-align: center;
  //border-radius: 50%
}

.card-right .card-icon img {
  //width: 100%;
  //height: 100%;
  width: 30px;
  height: 30px;
}

.card-left .title2 {
  height: 40px;
  line-height: 40px;
  //color: #7f8dfa;
  color: #495EEB;
  margin: 0;
  //font-weight: 600;
}

.card-left .title2 .account {
  flex: 1;
  width: 0;
  margin-left: 10px;
  color: #bbbbbd;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.wd-popup {
  //z-index: 999999!important;
  margin: 0 5px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}

.wd-popup--bottom {
  bottom: 70px;
}

.deposit {
  min-height: 400px;
}

@media screen and (max-width: 750px) {

  .effect-button {
    height: 30px !important;
  }

  .card-box {
    padding: 10px;
  }
  .card-content {
    font-size: 13px;
  }
  .account {
    min-width: 100px;
  }
  .card-content .title-content {
    line-height: 20px;
  }
  /*.text {
    max-width: 6em;
    word-wrap: break-word;
  }*/
  .card-icon {
    margin-right: 10px;
    /*width: 40px;
    height: 40px;
    line-height: 40px;*/
  }
  .card-content .card-icon img {
    width: 20px;
    height: 20px;
  }
  .card-right .card-icon img {
    width: 100%;
    height: 100%;
  }

  .select {
    width: 100px;

  }
}

@media screen and (max-width: 1380px) {
  .card-content .text {
    font-size: 14px;
  }
  .select {
    width: 160px;
  }
  .iq-card-block.iq-card-height {
    //max-height: 400px;
    overflow: auto;
  }
}

@media screen and (max-width: 1100px) {
  .card-content .text {
    font-size: 13px;
  }
  .select {
    width: 150px;
  }

}

.el-icon-loader {
  background: url(~@/assets/images/loader/LoadingAnimation.gif) center no-repeat;
  font-size: 40px;
  background-size: cover;
  width: 100%;
}

.el-icon-loader:before {
  content: "dog";
  visibility: hidden;
}

.iq-card-body {
  flex: unset;
}

.el-date-table td.end-date span,
.el-date-table td.start-date span {
  background-color: #a2b0f9;
}

.el-date-table td.today span {
  color: #5538fa;
  font-weight: 700;
}

.el-date-table td.available:hover {
  color: #5c3df9;
}

.wd-button.is-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  font-size: 0;
  color: #5855f9;
}

::v-deep .wd-picker-view-content {
  background-color: #485eeb !important;
  color: #e1dfdf;
}

.mt-box {
  margin-right: 4px;
  position: relative;
  width: 60px;
  height: 44px;
  transition: all 0.5s;
  transform-style: preserve-3d;
}

.mt5-show {
  transform: rotateY(-180deg);
}

.mt4-icon,
.mt5-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 60px;
  height: 44px;
  backface-visibility: hidden;
  border: unset !important;
}

.mt4-icon {
  background: url("~@/assets/images/mt4.png") no-repeat center / contain;
}

.mt5-icon {
  transform: rotateY(-180deg);
  background: url("~@/assets/images/mt5.png") no-repeat center / contain;
}

.mt-type {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 12px;
  border-radius: 6px;
  //background-color: #495EEB;
  background-color: #fff;
  border: 1px solid #495EEB;

  &-item {
    position: relative;
    z-index: 2;
    width: 50px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    //color: #fff;
    color: #495EEB;
    border-radius: 6px;
    cursor: pointer;
    transition: all .3s;
  }

  &-active {
    //color: #495EEB;
    color: #FFFFFF;
    font-size: 14px;
  }

  .slider {
    position: absolute;
    top: -3px;
    width: 54px;
    height: 30px;
    border-radius: 5px;
    transition: all .3s;
    //background-color: #fff;
    background-color: #495EEB;
    box-shadow: 0 0 5px rgba(127, 142, 246, 1);
  }
}

.security-dialog-content {
  margin-top: 20px;
  font-size: 16px;
}

.security-dialog-button {
  margin: 30px auto 0;
  padding: 0 20px;
  width: fit-content;
  line-height: 50px;
  border-radius: 50px;
  cursor: pointer;
  color: #fff;
  background-color: #495EEB;
}

.link-button {
  &:hover {
    text-decoration: underline;
  }
}

.card-icon {
  //box-shadow: 0 2px 5px -1px rgba(0, 0, 0, 0.5), inset 0px 4px 4px rgba(0, 0, 0, 0.3);
}

::v-deep .el-date-editor {
  .el-range-separator {
    width: 7% !important;
  }
}

.dialog {
  width: 60%;
  margin: 0 auto;

  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}

@media screen and (max-width: 720px) {
  ::v-deep .el-dialog {
    width: 100%;
  }
}

.promotion-card {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
  gap: 20px;

  &::-webkit-scrollbar {
    display: none;
  }

  &-item {
    //flex: 1;
    //min-width: 300px;
    //padding: 20px;
    //border-radius: 4px;
    //border: 1px solid #DCDFE6;
    position: relative;

    img {
      display: block;
      margin: auto;
      max-width: 300px;
      width: 100%;
    }

    &-title {
      font-weight: bold;
      font-size: 20px;
    }

    &-subtitle {

      span {
        font-weight: bold;
      }
    }

    &-btn {
      margin: 20px auto 0;
      padding: 4px 20px;
      width: fit-content;
      color: #fff;
      background: #495EEB;
      border-radius: 99px;
      cursor: pointer;
    }

    .expired {
      position: absolute;
      width: 140px;
      height: 150px;
      top: -76px;
      right: -26px;
      transform: rotate(-10deg);
    }
  }
}

@media screen and (max-width: 767px) {
  .promotion-card {

    &-item {

      img {
        width: 100%;
      }

      .expired {
        right: -20px;
      }
    }
  }
}
::v-deep .el-dialog {
  border-radius: 10px;
  overflow: hidden;
}

::v-deep .christmasDialog {
  .el-dialog__header {
    padding: 0 !important;
  }

  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>
