<script>
export default {

  data () {
    return {
      show: true,
      ob: null
    }
  },

  mounted () {
    this.AnalysisIQ()
    this.monitorDOM()
  },

  destroyed () {
    this.ob.disconnect()
  },

  watch: {
    '$i18n.locale': {
      handler (n) {
        this.show = false
        this.ob.disconnect()
        setTimeout(() => {
          this.show = true
          this.$nextTick(() => {
            this.AnalysisIQ()
            this.monitorDOM()
          })
        }, 0)
      },
      deep: true
    }
  },

  computed: {
    // 对应语言谷歌文档地址映射
    googleDoc() {
      const LANGUAGE_GOOGLE_DOCS = {
        'en_US': 'https://docs.google.com/forms/d/e/1FAIpQLScOOZ9dG0NDFUJQ2oIPdIOX7bfGEFPPs-1QyFRKk1UFwvCQpA/viewform',
        'zh_CN': 'https://docs.google.com/forms/d/e/1FAIpQLSf4C9QDU57HJx2b3kBio0W1fHLEthrBB2vrj6H308JiqFc40w/viewform?usp=sharing',
        'ja_JP': 'https://docs.google.com/forms/d/e/1FAIpQLSeXOcmcRl5titoKL60yCkilFJzRosLQrPUTyyd9E4njZwm9Og/viewform?usp=sharing',
        'th': 'https://docs.google.com/forms/d/e/1FAIpQLSf0NRSZi0q2H92DzwKcYJ8Mdz61KTUKWoVAYyS5jxRv2j0yCg/viewform?usp=sharing',
      }

      return LANGUAGE_GOOGLE_DOCS[this.$i18n.locale] || LANGUAGE_GOOGLE_DOCS['en_US']
    }

  },

  methods: {

    // Language：en-US ja-JP zh-CN th-TH

    // AnalysisIQ
    AnalysisIQ () {

      AcuityWidgets.globals({
        apikey: '02a34dff-956d-4461-b7bc-0fb8227acb09',
        locale: this.$i18n.locale.replace('_', '-')
      })

      const widget = AcuityWidgets.CreateWidget(
        'acuityanalysisiq',
        document.getElementById('acuity-analysisiq'),
        { settingId: 6145 }
      )

      widget.mount()
    },

    /**
     * @description AnalysisIQ 挂在完成后，监听DOM变化
     */
    monitorDOM() {
      const subscribe = document.querySelector('.subscribe')
      const subscribeClone = subscribe.cloneNode(true)

      this.ob = new MutationObserver((mutations, ob) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const dom of mutation.addedNodes) {
              if (dom.className.includes('OpenedTradeIdea__topRowWrapper__')) {
                subscribeClone.classList = []
                subscribeClone.style.display = 'block'
                dom.classList.add('flex-between')
                dom.appendChild(subscribeClone)
                subscribe.style.display = 'none'
              }

              console.log(dom)

              if (dom.className.includes('TileView__wrapper__') || dom.className.includes('AcuityResearchTerminalWidget__')) {
                subscribe.style.display = 'block'
              }
            }
          }
        }
      })

      this.ob.observe(document.querySelector('.widget'), {
        childList: true,
        subtree: true
      })
    }

  }

}
</script>

<template>
  <div class="widget">
    <a
      target="_blank"
      :href="googleDoc"
      id="subscribe"
      class="subscribe"
      v-viewpoint.click="{ message: `[Acuity] Click Subscribe to Google` }"
    >{{ $t('tradingTools.analysisIQ.subscribe') }}</a>
    <div id="acuity-analysisiq" v-if="show"></div>
  </div>
</template>

<style scoped lang="scss">

.widget {
  position: relative;
  width: calc(100% - 30px);
  min-height: calc(100% - 30px);
  border-radius: 5px;
  margin: 0 15px 15px 15px;
  background: #fff;
}

::v-deep .flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#subscribe {
  padding: 0 20px;
  line-height: 44px;
  color: white;
  background: #495eea;
  z-index: 101;
  border-radius: 14px;
  cursor: pointer;
}

.subscribe {
  position: absolute;
  left: 370px;
  top: 37px;
}


@media screen and (max-width: 1694px) {
  .subscribe {
    left: 170px !important;
  }
}


@media screen and (max-width: 757px) {
  .subscribe {
    line-height: 40px !important;
    top: 24px !important;
    left: 10px !important;
  }
}



</style>

<style>
svg {
  vertical-align: unset !important;
}

.TradeIdeaDetails__row__BSmGM div {
  white-space: nowrap;
}
</style>
