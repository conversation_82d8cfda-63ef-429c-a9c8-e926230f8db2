<template>
  <div>
<!--    <div class="container-fluid p-0">
      <div class="row no-gutters">
        <div class="col-sm-12 text-center">
          <div class="iq-error">
            <img :src="logo" style="height:121px;width: 400px" class="img-fluid iq-error-img">
            <h2 class="mb-2">PAYMENT CONFIRMATION</h2>
            <div>
              <el-row type="flex" justify="center">
                <b-col md="6" sm="6" lg="3">
                  <iq-card>
                    <template v-slot:body>
                      <div class="d-flex d-flex align-items-center justify-content-between">
                        <div>
                          <h5 ite>SUCCESS</h5>
                          <p style="float: left" class="fontsize-sm m-0">Payment Status</p>
                        </div>
                        <div class="rounded-circle iq-card-icon  dark-icon-light iq-bg-success">
                          <i class="fas fa-check"></i>
                        </div>
                      </div>
                    </template>
                  </iq-card>
                </b-col>
                <b-col md="6" sm="6" lg="3">

                  <iq-card>
                    <template v-slot:body>
                      <div class="d-flex d-flex align-items-center justify-content-between">
                        <div>
                          <h5 ite>
                            {{ result.pspAmount }} {{ result.currency }} ({{ result.fundingAmount }}
                            {{ result.fundingCurrency }})
                          </h5>
                          <p style="float: left" class="fontsize-sm m-0">Amount</p>
                        </div>
                        <div class="rounded-circle iq-card-icon  dark-icon-light iq-bg-primary "><i
                          class="far fa-money-bill-alt"></i>
                        </div>
                      </div>
                    </template>
                  </iq-card>
                </b-col>
              </el-row>
              <el-row type="flex" justify="center">
                <b-col md="6" sm="6" lg="3">
                  <iq-card>
                    <template v-slot:body>
                      <div class="d-flex d-flex align-items-center justify-content-between">
                        <div>
                          <h5 ite> {{ result.mt4Login }} </h5>
                          <p style="float: left" class="fontsize-sm m-0">Trading Account Number</p>
                        </div>
                        <div class="rounded-circle iq-card-icon  dark-icon-light iq-bg-primary "><i
                          class="fas fa-user"></i>
                        </div>
                      </div>
                    </template>
                  </iq-card>
                </b-col>
                <b-col md="6" sm="6" lg="3">
                  <iq-card>
                    <template v-slot:body>
                      <div class="d-flex d-flex align-items-center justify-content-between">
                        <div>
                          <h5 ite>
                            {{ result.tid }}
                          </h5>
                          <p style="float: left" class="fontsize-sm m-0">Trans No</p>
                        </div>
                        <div class="rounded-circle iq-card-icon  dark-icon-light iq-bg-primary "><i
                          class="fas fa-clipboard-list"></i>
                        </div>
                      </div>
                    </template>
                  </iq-card>
                </b-col>

              </el-row>
            </div>

            <button
              @click="returnHomePage"
              class="btn btn-primary mt-3"
              v-viewpoint.click="{ message: `[Awepay Deposits] Success result page back to home page` }"><i class="ri-home-4-line"></i>Back to home page
            </button>
          </div>
        </div>
      </div>
    </div>-->
    <div class="app-container">
      <div class="info">
        <div class="info-header">{{ $t('transaction.deposit.depositCommonResult.title') }}</div>
        <div class="info-content" v-for="(item, index) in listData" :key="index">
          <div class="info-content-item">
            <div class="info-content-item-label">{{ item.label }}:</div>
            <div class="info-content-item-value">{{ item.value }}</div>
          </div>
        </div>
        <div class="info-footer">
          {{ $t('transaction.deposit.depositCommonResult.desc') }}
          <div class="info-logo">
            <img width="100" src="@/assets/images/logo-white.png">
          </div>
        </div>
      </div>

      <button
        @click="returnHomePage"
        class="btn btn-primary mt-3"
        style="margin-top: 50px !important;"
        v-viewpoint.click="{ message: `[Awepay Deposits] Success result page back to home page` }"><i
        class="ri-home-4-line"></i>{{ $t('transaction.deposit.depositCommonResult.back') }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AwepayDepositSuccessPage',
  data () {
    return {
      result: null,
      logo: require('../../assets/images/logo-1.png')
    }
  },
  computed: {
    listData () {
      return [
        {
          label: this.$t('transaction.deposit.depositCommonResult.payStatus'),
          value: this.$t('transaction.deposit.depositCommonResult.success')
        },
        {
          label: this.$t('transaction.deposit.depositCommonResult.fundingAmount'),
          value: this.result.fundingAmount + this.result.fundingCurrency || '--'
        },
        {
          label: this.$t('transaction.deposit.depositCommonResult.account'),
          value: this.result.mt4login || '--'
        },
        {
          label: this.$t('transaction.deposit.depositCommonResult.order'),
          value: this.result.transactionRef || '--'
        },
        {
          label: this.$t('transaction.deposit.depositCommonResult.time'),
          value: this.result.createTime || '--'
        }
      ]
    }
  },
  methods: {
    fetchResult (result) {
      this.result = result
    },
    returnHomePage () {
      this.$router.replace('/')
    }
  },
  created () {
    let data = {
      amount: this.$route.query.pspAmount,
      currency: this.$route.query.currency,
      fundingAmount: this.$route.query.fundingAmount,
      fundingCurrency: this.$route.query.fundingCurrency,
      mt4login: this.$route.query.mt4Login,
      transactionRef: this.$route.query.tid,
      createTime: this.$route.query.createTime
    }
    this.fetchResult(data)
  }
}
</script>

<style scoped lang="scss">
.app-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .info {
    width: 400px;
    box-shadow: 6px -6px 20px 2px rgba(0, 0, 0, 0.4);

    &-header,
    &-footer {
      color: #fff;
      background: #495EEB;
    }

    &-header {
      padding: 20px 0;
      text-align: center;
      font-size: 20px;
    }

    &-footer {
      position: relative;
      padding: 20px;

      &::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 20px;
        left: 0;
        bottom: -20px;
        //background: linear-gradient(-45deg, transparent 33.33%, #495EEB 33.33%, #495EEB 66.66%, transparent 66.66%),
        //linear-gradient(45deg, transparent 33.33%, #495EEB 33.33%, #495EEB 66.66%, transparent 66.66%);
        background-color: #495EEB;
        //background-size: 30px 60px;
        //transform: rotateX(180deg);
      }
    }

    &-content {
      padding: 0 10px;
      border-left: 1px solid rgba(0, 0, 0, 0.07);
      border-right: 1px solid rgba(0, 0, 0, 0.07);

      &-item {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid rgba(0, 0, 0, 0.07);

        &-label {
          font-weight: bold;
        }
      }

    }

    &-logo {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }

  @media screen and (max-width: 768px) {
    .info {
      width: 95% !important;
    }
  }
}
</style>




