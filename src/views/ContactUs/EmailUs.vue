<template>
  <b-container style="margin: 0 15px;width: unset;" fluid :class="{ 'submit-ok': isSubmit }">
    <b-row>
      <b-col class="row-col" xs="12" sm="12" md="12" :lg="isSubmit ? 12 : 9" style="padding: 0">
        <iq-card v-if="!isSubmit">
          <template v-slot:headerTitle>
            <h4 class="card-title">
              {{ $t('sidebar.emailUs') }}
              <i class="el-icon-loading" v-show="tradingAccountListLoading"></i>
            </h4>
            <el-col class="subtitle">{{ $t('emailus.title') }}</el-col>

          </template>

          <template v-slot:body>
            <div class="form-w" style="width: 666px">
              <ValidationObserver ref="form" v-slot="{ handleSubmit }">
                <b-form @submit.stop.prevent="handleSubmit(submitEmail)">

<!--                  <ValidationProvider name="sidebar.name" rules="required" v-slot="{ errors }">-->
<!--                    <b-form-group label="">-->
<!--                      <b-input-->
<!--                        :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
<!--                        v-model="emailForm.name"-->
<!--                        :placeholder="$t('sidebar.name')"-->
<!--                      ></b-input>-->
<!--                      <div class="validation">-->
<!--                        <span>{{ errors[0] }}</span>-->
<!--                      </div>-->
<!--                    </b-form-group>-->
<!--                  </ValidationProvider>-->

                  <ValidationProvider name="sidebar.topic" rules="required" v-slot="{ errors }">
                    <b-form-group label="">
<!--                      <b-input-->
<!--                        :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"-->
<!--                        v-model="emailForm.topic"-->
<!--                        :placeholder="$t('sidebar.topic')"-->
<!--                      ></b-input>-->
                      <b-select
                        :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                        v-model="emailForm.topic"
                        style="font-size: 14px;height: 45px"
                      >
                        <b-form-select-option disabled :label="$t('sidebar.topic')" value="" />
                        <b-form-select-option
                          v-for="(topic, idx) in topicType"
                          :key="idx"
                          :label="topic.label"
                          :value="topic.value"
                        >
                        </b-form-select-option>
                      </b-select>
                      <div class="validation">
                        <span>{{ errors[0] }}</span>
                      </div>
                    </b-form-group>
                  </ValidationProvider>

                  <ValidationProvider name="sidebar.email" rules="required|email" v-slot="{ errors }">
                    <b-form-group label="">
                      <b-input
                        :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                        v-model="emailForm.email"
                        :placeholder="$t('sidebar.email')"
                      ></b-input>
                      <div class="validation">
                        <span>{{ errors[0] }}</span>
                      </div>
                    </b-form-group>
                  </ValidationProvider>
                  <ValidationProvider name="sidebar.phone" rules="required" v-slot="{ errors }">
                    <!--                  $t('sidebar.phone')-->
                    <b-form-group label="">
                      <VuePhoneNumberInput
                        :error="errors.length>0"
                        @update="updatePhoneNumber"
                        v-model="emailForm.phone.phoneNumber"
                        :translations="{
                          countrySelectorError: $t('emailus.vuePhoneNumberInput.countrySelectorError'),
                          countrySelectorLabel: $t('emailus.vuePhoneNumberInput.countrySelectorLabel'),
                          phoneNumberLabel: $t('emailus.vuePhoneNumberInput.phoneNumberLabel'),
                          example: $t('emailus.vuePhoneNumberInput.example')
                        }"
                      >
                      </VuePhoneNumberInput>
                      <div class="validation">
                        <span>{{ errors[0] }}</span>
                      </div>
                    </b-form-group>
                  </ValidationProvider>
                  <ValidationProvider name="emailus.label4" rules="selectionValidate" v-slot="{ errors }">
                    <b-form-group label="">
                      <b-select
                        :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                        v-model="emailForm.tradingAccountNumber"
                        style="font-size: 14px;height: 45px"
                      >
                        <!--                      :value="$t('emailus.label4')"-->
                        <b-form-select-option value="0" disabled>{{ $t('emailus.label4') }}</b-form-select-option>

                        <b-select-option-group v-if="mt4AccountNameLoginList.length!==0" label="MT4">
                          <b-form-select-option
                            v-for="(account, idx) in mt4AccountNameLoginList"
                            :key="idx"
                            :label="account"
                            :value="account"
                          >
                          </b-form-select-option>
                        </b-select-option-group>
                        <b-select-option-group v-if="mt5AccountNameLoginList.length!==0" label="MT5">
                          <b-form-select-option
                            v-for="(account, idx) in mt5AccountNameLoginList"
                            :key="idx"
                            :label="account"
                            :value="account"
                          >
                          </b-form-select-option>
                        </b-select-option-group>
                      </b-select>
                      <div class="validation">
                        <span>{{ errors[0] }}</span>
                      </div>
                    </b-form-group>
                  </ValidationProvider>

                  <ValidationProvider name="emailus.label5" :rules="'required'" v-slot="{ errors }">
                    <b-form-group label="">
                      <b-form-textarea
                        :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                        style="font-size: 14px;"
                        v-model="emailForm.comment"
                        rows="5"
                        type="textarea"
                        :placeholder="$t('emailus.label5')"
                      ></b-form-textarea>
                      <div class="validation">
                        <span>{{ errors[0] }}</span>
                      </div>
                    </b-form-group>
                  </ValidationProvider>
                  <center style="margin-top: 40px;">
                    <b-button class="submit-btn" :class="{'submit-btn-ok': submitBtnClass}" type="submit"
                              variant="primary">
                      {{ $t('sidebar.submit') }}
                      <i v-if="submitLoading" style="padding: 0;margin-left: 5px" class="el-icon-loading"></i>
                      <span></span>
                    </b-button>
                  </center>
                </b-form>
              </ValidationObserver>
            </div>

            <div style="height: 30px"></div>
          </template>
          <!--          <el-dialog width="400px" :visible.sync="resultDialogVisible">-->
          <!--            <p>{{ $t('emailus.test1') }}</p>-->
          <!--            <p>{{ $t('emailus.test2') }}</p>-->
          <!--            <b-button @click="resultDialogVisible=false" variant="primary">{{ $t('sidebar.ok') }}</b-button>-->
          <!--                    <el-dialog  width="420px" top="30vh" :visible.sync="resultDialogVisible">-->
          <!--            <p>{{$t('emailus.test1')}}</p>
                      <p>{{$t('emailus.test2')}}</p>
                      <b-button @click="resultDialogVisible=false" variant="primary">{{$t('sidebar.ok')}}</b-button>-->
          <!--            <div class="dialog" v-if="showValue === 1">-->
          <!--              <div style="transform: translate(0, -30px)">-->
          <!--                <img src="@/assets/images/email/success.png" alt="">-->
          <!--              </div>-->
          <!--              <div class="text">{{ $t('sidebar.success') }}</div>-->
          <!--              <div>-->
          <!--                <b-button class="btn" @click="resultDialogVisible=false" variant="primary">{{ $t('sidebar.ok') }}-->
          <!--                </b-button>-->
          <!--              </div>-->
          <!--            </div>-->

          <!--            <div class="dialog" v-if="showValue === 2">-->
          <!--              <div style="transform: translate(0, -30px)">-->
          <!--                <img src="@/assets/images/email/error.png" alt="">-->
          <!--              </div>-->
          <!--              <div class="text" style="padding-bottom: 20px">-->
          <!--                <p>{{ $t('sidebar.error') }}</p>-->
          <!--                <p>-->
          <!--                  <a href="mailto:<EMAIL>"><EMAIL></a>-->
          <!--                </p>-->
          <!--              </div>-->
          <!--              <div>-->
          <!--                <b-button class="btn" @click="resultDialogVisible=false" variant="primary">{{ $t('sidebar.ok') }}-->
          <!--                </b-button>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </el-dialog>-->
        </iq-card>

        <div class="submit-msg" v-else>
          <div>
            <img src="@/assets/images/email-us-success.png" alt="">
          </div>
          <div class="text">{{ $t('emailus.success') }}</div>
        </div>
      </b-col>
      <b-col xs="12" sm="12" md="12" lg="3" class="tip" v-if="!isSubmit" style="">
        <iq-card>
          <!-- <template v-slot:headerTitle>
            {{$t('emailus.tips')}}
          </template> -->
          <template v-slot:body>
            <!--            <div>
                        <div v-if="$i18n.locale !== 'ja_JP'">

                          <b-col>
                            {{ $t('emailus.tips_test') }}
                          </b-col>
                        </div>-->
            <div>
              <b-col style="text-align: center">
                <!--                <p style="width:100%;">-->
                <br v-if="$i18n.locale !== 'ja_JP'" class="spbr"/><br v-if="$i18n.locale !== 'ja_JP'" class="spbr"/>
                <p>
                    <span
                      style="font-size:15px; border-bottom:solid 3px #575757; width:100%;">{{
                        $t('emailus.qrcodeTitle')
                      }}</span>
                </p>
                <p style="margin:1em 0em;"><span style="width:100%;">ID: @myfxmarkets</span></p>
                <a href="https://lin.ee/qGQ0l3b" target="_blank" rel="noopener noreferrer">
                  <img src="../../../public/img/M_452acbpo_GW.png" alt="LINE QRコード" width="140px"></a>
                </p>
              </b-col>
            </div>
          </template>
        </iq-card>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>
import { ElPhoneNumberInput } from 'el-phone-number-input'
import VuePhoneNumberInput from 'vue-phone-number-input'
import 'vue-phone-number-input/dist/vue-phone-number-input.css'
import { getLocal } from '@/Utils/authLocalStorage'
import { fetchTradingAccount } from '@/services/account'
import { extend } from 'vee-validate'
import { fetchSubmitEmail } from '@/services/contact'
import { Message } from 'element-ui'

export default {
  name: 'EmailUs',
  components: {
    ElPhoneNumberInput,
    VuePhoneNumberInput
  },
  data () {
    return {
      showValue: 3,
      tradingAccountList: [],
      mt4AccountNameLoginList: [],
      mt5AccountNameLoginList: [],
      tradingAccountListLoading: false,
      emailForm: {
        topic: '',
        name: '',
        email: '',
        phone: {
          phoneNumber: '',
          callingCode: ''
        },
        tradingAccountNumber: 0,
        comment: ''
      },
      submitForm: {
        topic: '',
        fullName: '',
        email: '',
        tel_country: '',
        tel: '',
        mt4Login: '',
        comments: ''
      },
      submitLoading: false,
      resultDialogVisible: false,

      // 是否提交成功
      isSubmit: false,
      submitBtnClass: false
    }
  },
  computed: {
    test () {
      return this.$t('emailus.tips_test')
    },

    topicType () {
      return [
        { label: this.$t('emailus.topic.select[0]'), value: 'General Support' },
        { label: this.$t('emailus.topic.select[1]'), value: 'Sales Inquires' },
        { label: this.$t('emailus.topic.select[2]'), value: 'Account Services' },
        { label: this.$t('emailus.topic.select[3]'), value: 'Partner Programs' },
        { label: this.$t('emailus.topic.select[4]'), value: 'Media Inquires' },
        { label: this.$t('emailus.topic.select[5]'), value: 'Compliance' }
      ]
    }
  },
  created () {
    this.init()
    this.getTradingAccount()
  },
  methods: {
    getTradingAccount () {
      this.tradingAccountListLoading = true
      const email = JSON.parse(getLocal('user')).email
      const myguid = JSON.parse(getLocal('user')).myguid
      fetchTradingAccount(myguid, email).then(res => {
        this.tradingAccountList = res.data
        this.tradingAccountList.forEach(item => {
          const accountNameLogin = item.login
          const accountName = item.accountName
          const server = item.server
          if (server.toLowerCase().includes('mt5')) {
            this.mt5AccountNameLoginList.push(accountNameLogin + '-' + accountName)
          } else {
            this.mt4AccountNameLoginList.push(accountNameLogin + '-' + accountName)
          }
        })
        this.tradingAccountListLoading = false
      }).catch(error => {
        this.tradingAccountListLoading = false
      })
    },
    init () {
      const user = JSON.parse(getLocal('user'))
      this.emailForm.name = user.firstname + ' ' + user.lastname
      this.emailForm.email = user.email
      this.emailForm.phone.phoneNumber = user.tel
      this.emailForm.tradingAccountNumber = 0
      this.emailForm.phone.callingCode = ''
      this.emailForm.comment = ''
    },
    updatePhoneNumber (val) {
      this.emailForm.phone.callingCode = val.countryCallingCode
    },
    submitEmail () {
      this.$vp.add({ message: '[EmailUs] Click submit button.' })

      this.submitLoading = true
      this.submitForm.topic = this.emailForm.topic
      this.submitForm.email = this.emailForm.email
      this.submitForm.tel_country = this.emailForm.phone.callingCode
      this.submitForm.tel = this.emailForm.phone.phoneNumber
      this.submitForm.comments = this.emailForm.comment
      this.submitForm.mt4Login = this.emailForm.tradingAccountNumber.split('-')[0]
      this.submitForm.fullName = this.emailForm.name
      const data = this.submitForm
      data.myguid = JSON.parse(getLocal('user')).myguid
      fetchSubmitEmail(data).then(res => {
        // Message.success(res.msg)
        this.submitBtnClass = true
        this.submitLoading = false
        setTimeout(() => {
          this.isSubmit = true
          this.resultDialogVisible = true
          this.init()
          if (res.code === 200) {
            this.showValue = 1
          } else {
            this.showValue = 2
          }
        }, 1300)
      }).catch(error => {
        Message.error('System Failed')
        this.submitLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.validation {
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #dc3545
}

.row-col {

  ::v-deep {
    .iq-card-header {
      padding: 0;

      .iq-header-title {
        width: 100%;

        h4 {
          padding: 20px;
        }
      }
    }
  }

  .subtitle {
    margin-top: 10px;
    padding: 0 20px;
    //line-height: 50px;
    //color: #4760EA;
    //background-color: #ECF2FE;
  }
}

@media screen and (max-width: 767px) {
  .form-w {
    width: 100% !important;
  }
}

.tip {

  ::v-deep {
    .iq-card {
      color: #4A5FEB;
      box-shadow: 0 11px 10px -10px rgba(0, 0, 0, 0.3);
      background-color: #E7E8FB;
    }
  }
}

.dialog {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-content: center;
  text-align: center
}

.dialog .text {
  padding-bottom: 40px;
  white-space: pre-wrap
}

.dialog .btn {
  width: 30%;
  text-align: center
}

.submit-msg {
  padding-top: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  //justify-content: center;

  img {
    width: 400px;
  }

  .text {
    margin-top: 50px;
    //color: #495EEB;
    font-weight: bold;
    text-align: center;
  }
}

@media screen and (max-width: 767px) {
  .submit-msg img {
    width: 300px !important;
  }
}

.submit-ok {
  height: calc(100vh - 120px);

  ::v-deep .row {
    height: 100%;

    .submit-msg {
      height: 100%;
    }
  }
}


.submit-btn {
  position: relative;
  width: 90px;
  overflow: hidden;


  span {
    top: 0;
    left: 0;
    position: absolute;
    width: 100%;
    transition: all .3s;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: -20px;
      width: 0px;
      height: 0px;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transition: all .3s;
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  }

  &:focus {
    box-shadow: 0 16px 5px -13px rgba(0, 0, 0, 0.5) !important;
  }

  //padding: 0px 40px;
  //position: relative;
  //border-radius: 10px;
  //border: 0;
  //height: 36px;
  //box-shadow: 0 14px 5px -11px rgba(0, 0, 0, 0.3);
  //transition: all .3s;
  //
  //&::before {
  //  content: '';
  //  position: absolute;
  //  top: 36px;
  //  left: 0;
  //  width: 100%;
  //  height: 80%;
  //  background-color: rgba(73,94,235, 0);
  //  border-radius: 50%;
  //  transition: all .3s, width 0s;
  //  filter: blur(4px);
  //}
  //
  //&>span {
  //  z-index: -1;
  //  position: absolute;
  //  top: 0;
  //  left: 100%;
  //  width: 36px;
  //  height: 36px;
  //  transform: scaleX(0);
  //  transform-origin: left center;
  //  border: 1px solid #495EEB;
  //  border-radius: 0 10px 10px 0;
  //  transition: transform .5s;
  //
  //  &::before {
  //    content: '';
  //    position: absolute;
  //    top: 50%;
  //    left: 50%;
  //    transform: translate(-50%, -50%);
  //    width: 16px;
  //    height: 16px;
  //    background: url("../../assets/images/email-us-button-icon.png") no-repeat;
  //    background-size: 100% 100%;
  //    border-radius: 50%;
  //  }
  //}

  //&:hover {
  //  border-radius: 10px 0 0 10px;
  //  box-shadow: unset;
  //  transform: translateY(-4px);
  //}
  //
  //&:hover::before {
  //  transform: scale(0.5, 0.3);
  //  background-color: rgba(0, 0, 0, .1);
  //}
  //
  //&:hover span {
  //  z-index: 1;
  //  transform: scaleX(1);
  //}
}

.submit-btn-ok {
  border-color: #53B502 !important;
  background: #53B502 !important;

  span {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #53B502;

    &::before {
      top: 44%;
      width: 20px;
      height: 10px;
    }
  }

  //border-radius: 10px 0 0 10px;
  //box-shadow: unset;
  //transform: translateY(-4px);
  //
  //&::before {
  //  transform: scale(0.5, 0.3);
  //  background-color: rgba(0, 0, 0, .1);
  //}
  //
  //&>span {
  //  z-index: 1;
  //  transform: scaleX(1);
  //}
  //
  //&:hover {
  //  border-radius: 10px 0 0 10px;
  //  box-shadow: unset;
  //  transform: translateY(-4px);
  //}
}

</style>
