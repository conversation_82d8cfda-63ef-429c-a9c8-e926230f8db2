import Vue from 'vue'
import VueRouter from 'vue-router'
import { getLocal, removeLocalItem, setLocal } from '@/Utils/authLocalStorage'
import OfficialWebSite from '@/views/OfficialWebsite/OfficialWebSite'
import { resetToken } from '@/Utils/RefreshToken'
import axios from 'axios'
import constant from '@/config/constant'
import XCoinsDepositSuccessResultPage from '@/views/Pages/XCoinsDepositSuccessResultPage.vue'
import XCoinsDepositFailtureResulPage from '@/views/Pages/XCoinsDepositFailtureResulPage.vue'

/* Layouts */

const Layout2 = () => import('../layouts/Layout2.vue')
const Default = () => import('../layouts/BlankLayout')
const Register = () => import('../layouts/Register')
const AuthLayout = () => import('../layouts/AuthLayouts/AuthLayout')
/* Dashboards View */
// const Dashboard1 = () => import('../views/Dashboards/Dashboard1.vue')
const Dashboard1 = () => import('../views/Dashboards/NewDashboard.vue')

const SignIn1 = () => import('../views/AuthPages/Default/SignIn1')
const RecoverPassword1 = () => import('../views/AuthPages/Default/RecoverPassword1')
/* Extra Pages */
const ErrorPage = () => import('../views/Pages/ErrorPage')

const MbwDepositSuccessResultPage = () => import('../views/Pages/MbwDepositSuccessResultPage')
const MbwDepositFailureResultPage = () => import('../views/Pages/MbwDepositFailtureResulPage.vue')
const AwepayDepositFailurePage = () => import('../views/Pages/AwepayDepositFailurePage')
const AwepayDepositSuccessPage = () => import('../views/Pages/AwepayDepositSuccessPage')
const Crypto365DepositResultPage = () => import('../views/Pages/Crypto365DepositResultPage')
const ResetError = () => import('../views/Reset/ResetError.vue')
const ResetSuccess = () => import('../views/Reset/ResetSuccess.vue')

/**
 * MyAccount
 */
const AccountInformation = () => import('@/views/MyAccount/AccountInformation')
const MyDocument = () => import('@/views/MyAccount/MyDocument')

/**
 * Account Transactions
 * @returns {Promise<{readonly default?: {name: string}}>}
 * @constructor
 */
const Transaction = () => import('@/views/AccountTransactions/Transaction')
const MyCard = () => import('@/views/Card/MyCard')
const AccountRegister = () => import('@/views/COAccountRegister/AccountRegister')
const IBPortalLogin = () => import('@/views/IBPortal/Login')
const EAccount = () => import('@/views/MyFxE-Account/E-Account')
const EmailUs = () => import('@/views/ContactUs/EmailUs')
const LiveChat = () => import('@/views/ContactUs/LiveChat')
const SiteNewsIndex = () => import('@/views/SiteNews/Index')
const PlatformComponent = () => import('@/views/Platform/Platform')
const ChangeLeverage = () => import('@/views/AccountSettings/ChangeLeverage')
const OpenAdditionalAccount = () => import('@/views/AccountSettings/OpenAdditionalAccount')
const ResetCoPassword = () => import('@/views/AccountSettings/ResetCoPassword')
const LPOASignAgain = () => import('@/views/signNewLPOA/LPOASignAgain')

Vue.use(VueRouter)

const authChildRoutes = (prop) => [
  {
    path: 'sign-in1',
    name: prop + '.sign-in1',
    meta: {
      auth: true
    },
    component: SignIn1
  },
  /*
    {
      path: 'sign-up1',
      name: prop + '.sign-up1',
      meta: { auth: true },
      component: SignUp1
    },
  */
  {
    path: 'password-reset1',
    name: prop + '.password-reset1',
    meta: {
      auth: true
    },
    component: RecoverPassword1
  }
  /*
    {
      path: 'lock-screen1',
      name: prop + '.lock-screen1',
      meta: { auth: true },
      component: LockScreen1
    },
    {
      path: 'confirm-mail1',
      name: prop + '.confirm-mail1',
      meta: { auth: true },
      component: ConfirmMail1
    }
  */
]

const pagesChildRoutes = (prop) => [
  {
    path: '/error/:code/:guid?/:message?',
    name: prop + '.error',
    meta: {
      auth: false
    },
    component: ErrorPage
  },
  {
    path: 'mbwSuccess',
    name: prop + '.mbwSuccess',
    meta: {
      auth: false
    },
    component: MbwDepositSuccessResultPage
  },
  {
    path: 'mbwFailure',
    name: prop + '.mbwFailure',
    meta: {
      auth: false
    },
    component: MbwDepositFailureResultPage
  },
  {
    path: 'xcoinsSuccess',
    name: prop + '.xcoinsSuccess',
    meta: {
      auth: false
    },
    component: XCoinsDepositSuccessResultPage
  },
  {
    path: 'xcoinsFailure',
    name: prop + '.xcoinsFailure',
    meta: {
      auth: false
    },
    component: XCoinsDepositFailtureResulPage
  },
  {
    path: 'grandPaySuccess',
    name: prop + '.grandPaySuccess',
    meta: {
      auth: false
    },
    component: () => import('@/views/Pages/GrandPayDepositSuccessResultPage.vue')
  },
  {
    path: 'grandPayFailure',
    name: prop + '.grandPayFailure',
    meta: {
      auth: false
    },
    component: () => import('@/views/Pages/GrandPayDepositFailtureResulPage.vue')
  },
  {
    path: 'awepayFailure',
    name: prop + '.awepayFailure',
    meta: {
      auth: false
    },
    component: AwepayDepositFailurePage
  },
  {
    path: 'awepaySuccess',
    name: prop + '.awepaySuccess',
    meta: {
      auth: false
    },
    component: AwepayDepositSuccessPage
  },
  {
    path: 'crypto365Result',
    name: prop + '.crypto365Result',
    meta: {
      auth: false
    },
    component: Crypto365DepositResultPage
  },
  {
    path: 'monetixSuccess',
    name: prop + '.monetixSuccess',
    meta: {
      auth: false
    },
    component: () => import('@/views/Pages/MonetixDepositSuccessResultPage.vue')
  },
  {
    path: 'monetixFailure',
    name: prop + '.monetixFailure',
    meta: {
      auth: false
    },
    component: () => import('@/views/Pages/MonetixDepositFailtureResulPage.vue')
  },
  {
    path: 'myPaySuccess',
    name: prop + '.myPaySuccess',
    meta: {
      auth: false
    },
    component: () => import('@/views/Pages/MyPayDepositSuccessResultPage.vue')
  },
  {
    path: 'myPayFailure',
    name: prop + '.myPayFailure',
    meta: {
      auth: false
    },
    component: () => import('@/views/Pages/MyPayDepositFailtureResultPage.vue')
  },
]

const myAccountRoute = (prop) => [
  {
    path: 'accountInformation',
    name: prop + '.accountInformation',
    meta: {
      auth: true,
      name: 'Account Information'
    },
    component: AccountInformation
  },
  {
    path: 'myDocument',
    name: prop + '.myDocument',
    meta: {
      auth: true,
      name: 'My Document'
    },
    component: MyDocument
  }
]

const accountTransactions = (prop) => [
  {
    path: 'transaction',
    name: prop + '.transaction',
    meta: {
      auth: true,
      name: 'Transaction'
    },
    component: Transaction
  }
]

const card = (prop) => [
  {
    path: 'mycard ',
    name: prop + '.mycard',
    meta: {
      auth: true,
      name: 'mycard'
    },
    component: MyCard
  }
]

const accountRegister = (prop) => [
  {
    path: 'accountRegister/:myguid/:language/:ibCode/:cCode/:tracker?',
    name: prop + '.accountRegister',
    meta: {
      auth: true,
      name: 'accountRegister'
    },
    component: AccountRegister
  }
]

const lpoaSignAgain = (prop) => [
  {
    path: 'LPOASignAgain/:coguid/:lang/:ibCode/:lastName/:firstName/:ibName/:actionparams/:accountType/:companyName/:actioncode',
    name: prop + '.LPOASignAgain',
    meta: {
      auth: true,
      name: 'LPOASignAgain'
    },
    component: LPOASignAgain
  }
]

const AccountSettings = (prop) => [
  {
    path: 'changeLeverage',
    name: prop + '.changeLeverage',
    meta: {
      auth: true,
      name: 'ChangeLeverage'
    },
    component: ChangeLeverage
  },
  {
    path: 'openAdditionalAccount',
    name: prop + '.openAdditionalAccount',
    meta: {
      auth: true,
      name: 'OpenAdditionalAccount'
    },
    component: OpenAdditionalAccount
  },
  {
    path: 'resetCoPassword',
    name: prop + '.resetCoPassword',
    meta: {
      auth: true,
      name: 'ResetCoPassword'
    },
    component: ResetCoPassword
  }
]

const IBPortal = (prop) => [
  {
    path: 'login',
    name: prop + '.login',
    meta: {
      auth: true,
      name: 'Login'
    },
    component: IBPortalLogin
  }
]
const ContactUs = (prop) => [
  {
    path: 'emailUs',
    name: prop + '.emailUs',
    meta: {
      auth: true,
      name: 'EmailUs'
    },
    component: EmailUs
  },
  {
    path: 'liveChat',
    name: prop + '.liveChat',
    meta: {
      auth: true,
      name: 'LiveChat'
    },
    component: LiveChat
  },
  {
    path: 'resetSuccess',
    name: 'resetSuccess',
    component: ResetSuccess,
    meta: {
      auth: true
    }
  }
]

const SiteNews = (prop) => [
  {
    path: '',
    name: prop + '.siteNews',
    meta: {
      auth: true,
      name: 'SiteNews'
    },
    component: SiteNewsIndex
  }
  /*{
    path: 'promotion',
    name: prop + '.promotion',
    meta: {
      auth: true,
      name: 'promotion'
    },
    component: Promotion
  },
  {
    path: '/SiteNews/PromotionInfo',
    name: prop + '.promotionInfo',
    meta: {
      auth: true,
      name: 'promotion'
    },
    component: () => import('@/views/SiteNews/PromotionInfo.vue')
  },*/
]

const routes = [
  {
    path: '/',
    name: 'dashboard',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [{
      path: '',
      name: 'dashboard.home-2',
      meta: {
        auth: true,
        name: 'Home 2'
      },
      component: Dashboard1
    }
    ]
  },
  {
    path: '/adminLogin/:email/:token',
    name: 'adminLogin',
    component: Layout2,
    meta: {
      auth: true
    }
    // children: [{
    //   path: '',
    //   name: 'dashboard.home-2',
    //   meta: {
    //     auth: true,
    //     name: 'Home 2'
    //   },
    //   component: Dashboard1
    // }]
  },
  {
    path: '/auth',
    name: 'auth1',
    component: AuthLayout,
    meta: {
      auth: true
    },
    children: authChildRoutes('auth1')
  },
  {
    path: '/register',
    name: 'register',
    component: Register,
    meta: {
      auth: true
    },
    children: accountRegister('register')
  },
  {
    path: '/signNewLPOA',
    name: 'signNewLPOA',
    component: Register,
    meta: {
      auth: true
    },
    children: lpoaSignAgain('register')
  },
  {
    path: '/pages',
    name: 'pages',
    component: Default,
    meta: {
      auth: true
    },
    children: pagesChildRoutes('default')
  },
  {
    path: '/myAccount',
    name: 'myAccount',
    component: Layout2,
    meta: {
      auth: true
    },
    children: myAccountRoute('production')
  },
  {
    path: '/accountTransactions',
    name: 'accountTransactions',
    component: Layout2,
    meta: {
      auth: true
    },
    children: accountTransactions('production')
  },
  {
    path: '/card',
    name: 'card',
    component: Layout2,
    meta: {
      auth: true
    },
    children: card('production')
  },

  {
    path: '/e-account',
    name: 'e-account',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '',
        name: 'vault.content',
        meta: {
          auth: true,
          name: 'MYFX e-Account'
        },
        component: EAccount
      }
    ]
  },
  {
    path: '/accountSettings',
    name: 'accountSettings',
    component: Layout2,
    meta: {
      auth: true
    },
    children: AccountSettings('production')
  },
  {
    path: '/platform',
    name: 'platform',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '',
        name: 'production.platform',
        meta: {
          auth: true,
          name: 'Platform'
        },
        component: PlatformComponent
      }
    ]
  },
  {
    path: '/ibPortal',
    name: 'ibPortal',
    component: Layout2,
    meta: {
      auth: true
    },
    children: IBPortal('production')
  },
  {
    path: '/siteNews',
    name: 'siteNews',
    component: Layout2,
    meta: {
      auth: true
    },
    children: SiteNews('production')
  },
  {
    path: '/contactUs',
    name: 'contactUs',
    component: Layout2,
    meta: {
      auth: true
    },
    children: ContactUs('production')
  },
  {
    path: '/officialWebSite',
    name: 'officialWebSite',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '',
        name: 'officialWebSite.content',
        meta: {
          auth: true,
          name: 'Official WebSite'
        },
        component: OfficialWebSite
      }
    ]
  },
  {
    path: '/backOldVersion',
    name: 'BackOldVersion',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/backOldVersion',
        name: 'Back to Old Version',
        meta: {
          auth: true,
          name: 'Back to Old Version'
        },
        component: () => import('../views/BackOldVersion/index.vue')
      }
    ]
  },
  // 个人资料
  {
    path: '/profile',
    name: 'profile',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/profile',
        name: 'profile',
        meta: {
          auth: true,
          name: 'Profile'
        },
        component: () => import('@/views/profile/profile.vue')
      }
    ]
  },

  // Widget
  {
    path: '/calendar',
    name: 'Calendar',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/calendar',
        name: 'Calendar',
        meta: {
          auth: true,
          name: 'Calendar'
        },
        component: () => import('@/views/widget/Calendar.vue')
      }
    ]
  },
  {
    path: '/analysisIQ',
    name: 'AnalysisIQ',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/analysisIQ',
        name: 'AnalysisIQ',
        meta: {
          auth: true,
          name: 'AnalysisIQ'
        },
        component: () => import('@/views/widget/AnalysisIQ.vue')
      }
    ]
  },
  {
    path: '/downloads-social-links',
    name: 'DownloadsSocialLinks',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/downloads-social-links',
        name: 'DownloadsSocialLinks',
        meta: {
          auth: true,
          name: 'DownloadsSocialLinks'
        },
        component: () => import('@/views/widget/DownloadsSocialLinks.vue')
      }
    ]
  },
  // {
  //   path: '/webinars',
  //   name: 'Webinars',
  //   component: Layout2,
  //   meta: {
  //     auth: true
  //   },
  //   children: [
  //     {
  //       path: '/webinars',
  //       name: 'Webinars',
  //       meta: {
  //         auth: true,
  //         name: 'Webinars'
  //       },
  //       component: () => import('@/views/educationHub/Webinars.vue')
  //     }
  //   ]
  // },

  {
    path: '/promotion',
    name: 'promotion',
    component: () => import('@/layouts/Layout2.vue'),
    meta: {
      auth: true
    },
    children: [
      {
        path: '/promotion',
        name: 'promotion',
        meta: {
          auth: true,
          name: 'promotions'
        },
        component: () => import('@/views/SiteNews/promotion.vue')
      },
      {
        path: 'promotionInfo',
        name: 'promotionInfo',
        meta: {
          auth: true,
          name: 'promotionInfo'
        },
        component: () => import('@/views/SiteNews/PromotionInfo.vue')
      },
      {
        path: 'promotionInfo-up600',
        name: 'promotionInfo-up600',
        meta: {
          auth: true,
          name: 'promotionInfo-up600'
        },
        component: () => import('@/views/SiteNews/PromotionInfo-up600.vue')
      },
      {
        path: 'promotionInfo2',
        name: 'promotionInfo2',
        meta: {
          auth: true,
          name: 'promotionInfo2'
        },
        component: () => import('@/views/SiteNews/PromotionInfo2.vue')
      },
      {
        path: 'promotionInfo-up600-verify',
        name: 'promotionInfo-up600-verify',
        meta: {
          auth: true,
          name: 'promotionInfo-up600-verify'
        },
        component: () => import('@/views/SiteNews/AccountVerify.vue')
      },
      {
        path: 'halloween',
        name: 'halloween',
        meta: {
          auth: true,
          name: 'halloween'
        },
        component: () => import('@/views/promotions/Halloween.vue')
      },
      {
        path: 'black-friday',
        name: 'black-friday',
        meta: {
          auth: true,
          name: 'black-friday'
        },
        component: () => import('@/views/promotions/BlackFriday.vue')
      },
      {
        path: 'cyber-monday',
        name: 'cyber-monday',
        meta: {
          auth: true,
          name: 'cyber-monday'
        },
        component: () => import('@/views/promotions/CyberMonday.vue')
      },
      {
        path: 'christmas',
        name: 'christmas',
        meta: {
          auth: true,
          name: 'christmas'
        },
        component: () => import('@/views/promotions/Christmas.vue')
      },
      {
        path: 'new-year-2025',
        name: 'new-year-2025',
        meta: {
          auth: true,
          name: 'new-year-2025'
        },
        component: () => import('@/views/promotions/NewYear2025.vue')
      },
      {
        path: 'line-1000',
        name: 'line-1000',
        meta: {
          auth: true,
          name: 'line-1000'
        },
        component: () => import('@/views/promotions/Line1000.vue')
      },
      {
        path: 'cashback-2025-may',
        name: 'cashback-2025-may',
        meta: {
          auth: true,
          name: 'cashback-2025-may'
        },
        component: () => import('@/views/promotions/Cashback2025May.vue')
      },
    ]
  },
  /*{
    path: '/SiteNews/PromotionInfo',
    name: prop + '.promotionInfo',
    meta: {
      auth: true,
      name: 'promotion'
    },
    component: () => import('@/views/SiteNews/PromotionInfo.vue')
  },*/
  {
    path: '/archiveTradeHistory',
    name: 'ArchiveTradeHistory',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/ArchiveTradeHistory',
        name: 'ArchiveTradeHistory',
        meta: {
          auth: true,
          name: ''
        },
        component: () => import('../views/MyAccount/ArchiveTradeHistory.vue')
      }
    ]
  },
  {
    path: '/reset',
    name: 'reset',
    component: Layout2,
    meta: {
      auth: true
    },
    children: [
      {
        path: '/resetSecret',
        name: 'resetSecret',
        meta: {
          auth: true
        },
        component: () => import('@/views/resetSecret/index.vue')
      }
    ]
  },
  {
    path: '/publicResetSecret',
    name: 'publicResetSecret',
    meta: {
      auth: false
    },
    component: () => import('@/views/publicResetSecret/index.vue')
  },
  {
    path: 'error-auth',
    name: 'error-auth',
    component: Layout2,
    meta: {
      auth: false
    },
    children: [
      {
        path: '/error-auth/:code/:guid?/:message?',
        name: 'default.error.auth',
        meta: {
          auth: false
        },
        component: ErrorPage
      }
    ]
  },
  {
    path: '/resetError',
    name: 'resetError',
    component: ResetError,
    meta: {
      auth: false
    }
  },
  {
    path: '/questionnaire',
    name: 'questionnaire',
    component: () => import('@/views/Questionnaire/index.vue'),
    meta: {
      auth: false
    }
  },
  {
    path: '/questionnaire-success',
    name: 'questionnaire-success',
    component: () => import('@/views/Questionnaire/QuestionnaireSuccess.vue'),
    meta: {
      auth: false
    }
  },
  {
    path: '/questionnaire-error',
    name: 'questionnaire-error',
    component: () => import('@/views/Questionnaire/QuestionnaireError.vue'),
    meta: {
      auth: false
    }
  },
  {
    path: '/lpoa-success',
    name: 'lpoa-success',
    component: () => import('@/views/signNewLPOA/component/lpoaSuccess.vue'),
    meta: {
      auth: false
    }
  },
  {
    path: '/becomeIBOnline',
    name: 'becomeIBOnline',
    component: () => import('@/views/BecomeIBOnline/index.vue'),
    meta: {
      auth: false
    }
  }
]

const router = new VueRouter({
  mode: 'hash',
  base: process.env.VUE_APP_BASE_URL,
  routes
})

const request = axios.create(
  {
    baseURL: window.location.origin.includes('myfx.group') ? constant.indonesiaBaseURL : constant.webBaseURL,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    timeout: 120000
  }
)

async function adminLogin (to, next) {
  await request({
    url: '/external/adminLoginCOCheck',
    method: 'post',
    params: {
      email: to.params.email,
      token: to.params.token
    }
  }).then(res => {
    console.log(res, '----res')
    setLocal('access_token', res.data.data.accessToken, 0.5)
    setLocal('refresh_token', res.data.data.refreshToken, 1)
    setLocal('user', JSON.stringify(res.data.data.accountEntity), 1)
    return next('/')
  }).catch(e => {
    return next('/error/404')
  })
}

router.beforeEach(async (to, from, next) => {
  if (window.vm) {
    if (!(from.path.startsWith('/error') || to.path.startsWith('/error'))) {
      vm.$vp?.add({
        eventType: 'Route change',
        message: `[Page Jumps] '${ from.path }' to '${ to.path }'`
      })
    }
  }

  //isok=1 my document页面不显示
  if (to.path === '/myAccount/myDocument'){
    return JSON.parse(getLocal('user')).isok === '1' ? next('/error/404') : next()
  }

  if (to.path === '/publicResetSecret') return next()

  const publicPages = ['/auth/sign-in1', '/auth/password-reset1', '/register/accountRegister', '/signNewLPOA/LPOASignAgain']
  /* if (getLocal('access_token') !== null){
    next('/')
  } */
  if (publicPages.includes(to.path)) {
    removeLocalItem('user')
    removeLocalItem('access_token')
    removeLocalItem('refresh_token')
  }
  let authRequired = true
  if (publicPages.includes(to.path) || to.path.indexOf(publicPages[2]) != -1 || to.path.indexOf(publicPages[3]) != -1) {
    authRequired = false
  }
  const loggedIn = getLocal('access_token')

  console.log(to, '----')

  if (to.path.startsWith('/adminLogin')) {
    await adminLogin(to, next)
    // setLocal('user', JSON.stringify(parm[4].data), 1)
    // setLocal('access_token', parm[2], 0.5)
    // setLocal('refresh_token', parm[3], 1)
  }

  if (to.path.startsWith('/error')) {
    return next()
  }

  if (to.matched.length === 0) {
    return next('/error/404')
  }


  // Hub
  const user = JSON.parse(getLocal('user'))
  if ((user?.nationality || '').toLowerCase() === 'japan' || (user?.country || '').toLowerCase() === 'japan') {
    if (to.path === '/webinars') {
      return next('/error/404')
    }
  }
  // Hub



  window.scrollTo({
    top: 0,
    left: 0
  })

  if (to.meta.auth) {
    if (authRequired && (loggedIn === null || loggedIn === undefined)) {
      if (getLocal('refresh_token') === null || getLocal('refresh_token') === undefined) {
        return next('/auth/sign-in1')
      }
      resetToken()
    } else if (to.name === 'dashboard.home-2') {
      return next()
    }
  }
  next()
})

export default router
