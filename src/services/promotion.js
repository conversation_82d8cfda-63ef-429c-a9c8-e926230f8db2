'use strict'

import axios from './index'


/**
 * @description 检测是否有权限参加
 */
export function checkPromotionAuth (params) {
  return axios({
    url: '/promotion/cashBack2024/check',
    method: 'GET',
    params
  })
}


/**
 * @description 活动数据信息
 */
export function activityData (params) {
  return axios({
    url: '/promotion/cashBack2024/getLots',
    method: 'GET',
    params
  })
}


/**
 * @description 加入活动
 */
export function joinNow (params) {
  return axios({
    url: `/promotion/joinWelcomeBonusPromotion2024`,
    method: 'GET',
    params
  })
}


/**
 * @description 活动信息
 */
export function getWelcomeBonus (params) {
  return axios({
    url: `/promotion/getWelcomeBonusPromotion2024`,
    method: 'GET',
    params
  })
}

/**
 * @description christmas promotion 用户是否注册过
 */
export function isRegisterChristmas (params) {
  return axios({
    url: '/promotion/checkChristmas2024',
    method: 'GET',
    params
  })
}

/**
 * @description 获取cashback2025May的交易账号
 */
export function getCashback2025MayAccount () {
  return axios({
    url: '/mt5TradingAccount',
    method: 'POST'
  })
}

/**
 * @description 获取已参加cashback2025May的交易账号
 */
export function getCashback2025MayJoinedAccount () {
  return axios({
    url: '/promotion/getCashback2025May',
    method: 'GET'
  })
}

/**
 * @description cashback2025May是否参加过
 */
export function checkCashback2025May (params) {
  return axios({
    url: '/promotion/checkCashback2025may',
    method: 'GET',
    params
  })
}

/**
 * @description cashback2025May加入活动
 */
export function joinCashback2025May (data) {
  return axios({
    url: '/promotion/joinCashback2025may',
    method: 'post',
    data
  })
}

