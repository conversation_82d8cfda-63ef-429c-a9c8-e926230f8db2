import axios from './index.js'

export function getAccountBalance (mt4AccountEntity) {
  return axios({
    url: '/withdrawal/getBalance',
    method: 'post',
    data:  mt4AccountEntity
  })
}

//BitWallet通道
export function sumBitWalletWithdrawal (mt4AccountDto) {
  return axios({
    url: '/withdrawal/walletBitWalletWithdrawal',
    method: 'post',
    data:  mt4AccountDto
  })
}

export function mt4BitWalletWithdrawal (mt4AccountDto) {
  return axios({
    url: '/withdrawal/mt4BitWalletWithdrawal',
    method: 'post',
    data:  mt4AccountDto
  })
}

export function mt5BitWalletWithdrawal (mt4AccountDto) {
  return axios({
    url: '/withdrawal/mt5BitWalletWithdrawal',
    method: 'post',
    data:  mt4AccountDto
  })
}
//--------》

//BankWire 通道
export function getBankAccount(myGuid,ppsName) {
  return axios({
    url: '/withdrawal/getBankAccount',
    method: 'get',
    params:  {myGuid,ppsName}
  })
}


export function sumBankWireWithdrawal (mt4AccountDto) {
  return axios({
    url: '/withdrawal/walletBankWireWithdrawal',
    method: 'post',
    data:  mt4AccountDto
  })
}

export function mt4BankWireWithdrawal (mt4AccountDto) {
  return axios({
    url: '/withdrawal/mt4BankWireWithdrawal',
    method: 'post',
    data:  mt4AccountDto
  })
}

export function mt5BankWireWithdrawal (mt4AccountDto) {
  return axios({
    url: '/withdrawal/mt5BankWireWithdrawal',
    method: 'post',
    data:  mt4AccountDto
  })
}

export function insertWithdrawCard (withdrawCardEntity) {
  return axios({
    url: '/withdrawCard/insertWithdrawCard',
    method: 'post',
    data:  withdrawCardEntity
  })
}
//--------》

//CryptoCurrency 通道
export function sumCryptoCurrencyWithdrawal (cryptoCurrencyForm) {
  return axios({
    url: '/withdrawal/walletCryptoCurrencyWithdrawal',
    method: 'post',
    data:  cryptoCurrencyForm
  })
}

export function mt4CryptoCurrencyWithdrawal (cryptoCurrencyForm) {
  return axios({
    url: '/withdrawal/mt4CryptoCurrencyWithdrawal',
    method: 'post',
    data:  cryptoCurrencyForm
  })
}

export function mt5CryptoCurrencyWithdrawal (cryptoCurrencyForm) {
  return axios({
    url: '/withdrawal/mt5CryptoCurrencyWithdrawal',
    method: 'post',
    data:  cryptoCurrencyForm
  })
}
//--------》

//CreditCard 通道
export function sumCreditCardWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/walletCreditCardWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt4CreditCardWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt4CreditCardWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt5CreditCardWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt5CreditCardWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}
//--------》


//JP DomesticBankWire 通道
export function getBankAccountJP(myGuid) {
  return axios({
    url: '/withdrawal/getBankAccountJP',
    method: 'get',
    params:  {myGuid}
  })
}

export function insertWithdrawCardJP (withdrawCardJPEntity) {
  return axios({
    url: '/withdrawCard/insertWithdrawCardJP',
    method: 'post',
    data:  withdrawCardJPEntity
  })
}


export function sumJPDomesticBankWireWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/walletJPDomesticBankWireWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt4JPDomesticBankWireWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt4JPDomesticBankWireWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt5JPDomesticBankWireWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt5JPDomesticBankWireWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

//--------》


//AWE Pay 通道
export function sumAWEPayWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/walletAWEPayWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt4AWEPayWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt4AWEPayWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt5AWEPayWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt5AWEPayWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

//--------》


//CNY Withdrawal 通道
export function sumCNYWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/walletCNYWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt4CNYWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt4CNYWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt5CNYWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt5CNYPayWithdrawal',
    method: 'post',
    data:  creditCardForm
  })
}

//--------》

//Unionpay 通道
export function sumUnionpayWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/walletUnionpay',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt4UnionpayWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt4Unionpay',
    method: 'post',
    data:  creditCardForm
  })
}

export function mt5UnionpayWithdrawal (creditCardForm) {
  return axios({
    url: '/withdrawal/mt5Unionpay',
    method: 'post',
    data:  creditCardForm
  })
}

//--------》


//MAM PAMM 出金
export function mamPammWithdrawal (MamPammForm) {
  return axios({
    url: '/withdrawal/mamPammWithdrawal',
    method: 'post',
    data:  MamPammForm
  })
}
//--------》


/**
 * @description 泰铢钱包出金
 */
export const thbWalletWithdrawal = (data) => axios({
  url: `/withdrawal/walletThaiBaht`,
  method: 'POST',
  data
})


/**
 * @description 泰铢MT4出金
 */
export const thbMT4Withdrawal = (data) => axios({
  url: `/withdrawal/mt4ThaiBaht`,
  method: 'POST',
  data
})


/**
 * @description 泰铢MT5出金
 */
export const thbMT5Withdrawal = (data) => axios({
  url: `/withdrawal/mt5ThaiBaht`,
  method: 'POST',
  data
})
