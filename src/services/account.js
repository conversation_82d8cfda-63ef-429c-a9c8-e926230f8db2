import axios from './index.js'

export function fetchTradingAccountParams (myGuid, email, data) {
  return axios({
    url: '/tradingAccountParams',
    method: 'post',
    params: {
      myGuid: myGuid,
      email: email
    },
    data: data
  })
}

export function fetchTradingAccount (myGuid, email) {
  return axios({
    url: '/tradingAccount',
    method: 'post',
    params: {
      myGuid: myGuid,
      email: email
    }
  })
}

export function fetchTransactionsTradingAccount (myGuid, email) {
  return axios({
    url: '/transactionsTradingAccount',
    method: 'post',
    params: {
      myGuid: myGuid,
      email: email
    }
  })
}

export function ibCheck (ibCode) {
  return axios({
    url: '/ibCheck',
    method: 'get',
    params: {
      ibCode: ibCode
    }
  })
}

export function submitOpenMamPammAccountRequest (signature, fullName, firstName, lastName, contractid, coguid, ibCode, dateCreated, pdf) {
  return axios({
    url: '/submitOpenMt4Request/MamPamm',
    method: 'post',
    data: pdf,
    params: {
      ibCode: ibCode,
      signature: signature,
      fullName: fullName,
      firstName: firstName,
      lastName: lastName,
      contractid: contractid,
      coguid: coguid,
      dateCreated: dateCreated
    }
  })
}

export function fetchTradingAccountMarginLevel (mt4accountMyguid) {
  return axios({
    url: '/marginLevel',
    method: 'post',
    params: {
      mt4accountMyguid: mt4accountMyguid
    }
  })
}

export function fetchTradingAccountBalance (myGuid) {
  return axios({
    url: '/balance',
    method: 'get',
    params: {
      myGuid: myGuid
    }
  })
}

export function submitChangeTradingAccountPassword (data, code, token) {
  return axios({
    url: '/tradingAccount/resetPassword',
    method: 'post',
    data: data,
    params: {
      token: token,
      code: code
    }
  })
}

/* export function uploadMyDocumentAttachment (data) {
  return axios({
    url: '/attachment/upload/attach',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [function () {
      return data
    }]
  })
} */

export function uploadMyDocumentAttachment (data, type) {
  return axios({
    url: `/attachment/upload/attachWithType?type=${ type }`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [function () {
      return data
    }]
  })
}

export function deleteDocument (coguid, myguid) {
  return axios({
    url: '/attachment/removeAttach',
    method: 'get',
    params: {
      coguid,
      myguid
    }
  })
}

export function getLeverageChangeHistory (data) {
  return axios({
    url: '/history',
    method: 'post',
    data: data
  })
}

export function updateChangeLeverageRequest (data) {
  return axios({
    url: '/changeLeverage',
    method: 'post',
    data: data
  })
}

export function submitOpenMt4AccountRequest (data) {
  return axios({
    url: '/submitOpenMt4Request',
    method: 'post',
    data: data
  })
}

export function verifyCurrentPassword (data) {
  return axios({
    url: '/password/currentVerify',
    method: 'post',
    data: data
  })
}

export function getSecurityQuestion (myguid) {
  return axios({
    url: '/securityQuestion/question',
    method: 'post',
    params: {
      myguid: myguid
    }
  })
}

export function verifySecurityQuestion (data) {
  return axios({
    url: 'securityQuestion/questionVerify',
    method: 'post',
    data: data
  })
}

export function submitPasswordChange (data, code, token) {
  return axios({
    url: '/password/resetPassword',
    method: 'post',
    data: data,
    params: {
      code: code,
      token: token
    }
  })
}

export function getInternalFundTransferAccount (email, myguid) {
  return axios({
    url: '/internal/account',
    method: 'post',
    params: {
      email: email,
      myguid: myguid
    }
  })
}

export function sendForgetPasswordEmailCode (username, token, code) {
  return axios({
    url: '/forgetPassword/sendEmail',
    method: 'post',
    params: {
      username: username,
      token: token,
      code: code
    }
  })
}

export function reSendForgetPasswordEmailCode (username, token, code) {
  return axios({
    url: '/forgetPassword/reSend',
    method: 'post',
    params: {
      username: username,
      token: token,
      code: code
    }
  })
}

export function verifyForgetPasswordEmailCode (username, code) {
  return axios({
    url: '/forgetPassword/verifyCode',
    method: 'get',
    params: {
      username: username,
      code: code
    }
  })
}

export function startForgetPassword (data, ticket) {
  return axios.post('/forgetPassword/setPassword', data, { headers: { ticket } })
}

export function urlParameterCheck (data) {
  return axios({
    url: '/urlParameterCheck',
    method: 'post',
    data: data
  })
}

export function registerStepOne (data) {
  return axios({
    url: '/registerStepOne',
    method: 'post',
    data: data
  })
}

export function sendAndSignLpoa (signature, firstname, lastname, contractid, coguid, ibCode, dateCreated, pdf) {
  return axios({
    url: '/registerLpoa',
    method: 'post',
    data: pdf,
    params: {
      ibCode: ibCode,
      signature: signature,
      firstname: firstname,
      lastname: lastname,
      contractid: contractid,
      coguid: coguid,
      dateCreated: dateCreated
    }
  })
}

export function registerStepThree (data) {
  return axios({
    url: '/registerStepThree',
    method: 'post',
    data: data
  })
}

export function getMamPamCancelSubRequest (myguid) {
  return axios({
    url: '/tradingAccount/getMamPamCancelSubRequest',
    method: 'get',
    params: {
      myguid: myguid
    }
  })
}

export function sendExitRequest (myguid, mt4accountMyguid) {
  return axios({
    url: '/tradingAccount/sendExitRequest',
    method: 'post',
    params: {
      myguid: myguid,
      mt4accountMyguid: mt4accountMyguid
    }
  })
}

export function getStrategyName (login, server) {
  return axios({
    url: '/tradingAccount/getMasterName',
    method: 'get',
    params: {
      login,
      server
    }
  })
}

export function recoveryMTAccount (mt4AccountGuid) {
  return axios({
    url: '/tradingAccount/apiRecoveryMT',
    method: 'post',
    params: {
      mt4AccountGuid
    }
  })
}

export function checkOpenAccount () {
  return axios({
    url: '/checkOpenAccount',
    method: 'get'
  })
}

/**
 * @description
 */
export function iBCodeMapping (params) {
  return axios({
    url: '/tradingAccount/getIBCodeMapping',
    method: 'GET',
    params
  })
}

/**
 * @description
 */
export function joinAgainLpoa (params, data) {
  return axios({
    url: `/tradingAccount/joinAgainLpoa`,
    method: 'POST',
    params,
    data
  })
}


/**
 * @description 获取当前最新 isok
 */
export const nowIsOk = (coGuid) => axios({
  url: `/account/getIsOk`,
  method: 'GET',
  params: {
    coGuid
  }
})
