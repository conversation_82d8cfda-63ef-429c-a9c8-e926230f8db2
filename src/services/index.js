import axios from 'axios'
import constant from '../config/constant'
import { getLocal, removeLocalItem, setLocal } from '@/Utils/authLocalStorage'
import i18n from '@/i18n'
import { resetToken } from '@/Utils/RefreshToken'
import router from '@/router'
import vm from '@/main.js'

const baseURL = window.location.origin.includes('myfx.group') ? constant.indonesiaBaseURL : constant.webBaseURL

const request = axios.create({
  // baseURL: constant.webBaseURL,
  baseURL: baseURL,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'X-CSRF-TOKEN': '',
    Lang: ''
  },
  timeout: 120000
})

request.interceptors.request.use(async config => {
  config.headers.Lang = i18n.locale
  if (getLocal('user') !== null && getLocal('user') !== undefined && getLocal('user') !== '') {
    config.headers.country = JSON.parse(getLocal('user')).country
  }
  if ((getLocal('access_token') === undefined || getLocal('access_token') === null) &&
    config.url !== '/captcha' &&
    config.url !== '/login' &&
    config.url !== '/resetToken' &&
    config.url !== '/forgetPassword/sendEmail' &&
    config.url !== '/forgetPassword/verifyCode' &&
    config.url !== '/forgetPassword/setPassword' &&
    config.url !== '/forgetPassword/reSend' &&
    config.url !== '/securityQuestion/getQueryAll' &&
    config.url !== '/urlParameterCheck' &&
    config.url !== '/registerStepOne' &&
    config.url !== '/securityQuestion/saveSecurityQuestion' &&
    config.url !== '/registerStepThree' &&
    config.url !== '/registerLpoa' &&
    config.url !== '/securityQuestion/checkResetSQPageAuth' &&
    config.url !== '/securityQuestion/saveCoSecurityQuestion' &&
    config.url !== '/ibCheckEmail' &&
    config.url !== '/saveIBQuestionnaire'
  ) {
    if (getLocal('refresh_token') === null || getLocal('refresh_token') === undefined) {
      router.push({
        name: 'auth1.sign-in1'
      }).then()
      return Promise.reject('to login(services/index)')
    }
    await resetToken()
  }
  config.headers['X-CSRF-TOKEN'] = getLocal('access_token')
  return config
}, error => {
  vm.$message.error('Error occurred') // do something with request error
  console.log(error) // for debug
  return Promise.reject(error)
})

request.interceptors.response.use(response => {
  const res = response.data
  // res.code = 1017
  // if the custom code is not 9999, it is judged as an error
  // console.log("url--"+response.config.url)
  if ((res.code !== 200 && res.code)) {
    if (res.code !== 400) {
      // if (res.code === 99999) {
      //   vm.$message.error('Error!')
      // }
      if (res.code === 1013) {
        return Promise.reject(res)
      } else if (res.code === 1017) {
        const email = JSON.parse(getLocal('user'))?.email
        removeLocalItem('user')
        removeLocalItem('access_token')
        removeLocalItem('refresh_token')
        router.push({
          name: 'auth1.sign-in1',
          query: {
            param: 'sqLocked',
            email
          }
        })
        return
      } else if (res.code === 444) {
        vm.$message.error(res.msg)
        return Promise.reject(res)
      } else if (res.code === 5004 || res.code === 5005 || res.code === 5006 || res.code === 5007) {
        router.push({
          path: '/questionnaire-error',
          query: {
            code: res.code,
            content: res.msg
          }
        }).then()
        return Promise.reject(res)
      } else if (res.code === 7001) {
        if (getLocal('user')) {
          router.push({
            name: 'dashboard.home-2'
          }).then()
          vm.$message.error(res.msg)
          return Promise.reject(res)
        } else {
          router.push({
            name: 'auth1.sign-in1'
          }).then()
          vm.$message.error(res.msg)
          return Promise.reject(res)
        }
      } else if (res.code === 1026) {
        removeLocalItem('user')
        removeLocalItem('access_token')
        removeLocalItem('refresh_token')
        vm.$router.push({
          name: 'auth1.sign-in1',
          query: {
            param: 'locked'
          }
        })
        return
      }

      if (getLocal('user')) {
        router.push({
          name: 'default.error.auth',
          params: {
            code: res.code,
            guid: res.data,
            message: (res.msg === null || res.msg === '') ? '' : res.msg
          }
        })
      } else {
        router.push({
          name: 'default.error',
          params: {
            code: res.code,
            guid: res.data,
            message: (res.msg === null || res.msg === '') ? '' : res.msg
          }
        })
      }
    } else {
      if (response.config.url === '/login') {

      } else {
        vm.$message.error(res.msg)
      }
    }

    return Promise.reject(new Error(res.msg || 'Error'))
  } else if (response.config.url === '/downloadTradeHistoryCsv') {
    return {
      data: res,
      headers: response.headers
    }
  } else if (response.config.url === '/adminLoginCO') {
    console.log(response.headers)
    console.log(res)
    setLocal('user', JSON.stringify(res.data.accountEntity), 1)
    setLocal('access_token', res.data.accessToken, 0.5)
    setLocal('refresh_token', res.data.refreshToken, 1)
    return {
      data: res,
      headers: response.headers
    }
  } else {
    return res
  }
  /* router.push({
    name: 'default.error.auth',
    params: {
      code: res.code,
      guid: res.code,
      message: (res.msg === null || res.msg === '') ? "" : res.msg
    }
  }) */
}, error => {
  console.log('message error' + error)
  vm.$message.error(error?.response?.data?.msg)
  return Promise.reject(error)
})

export default request
