import axios from './index.js'

export function getDepositWayList () {
  return axios({
    url: '/deposit/list',
    method: 'get'
  })
}

export function getDepositIsAvailable (data) {
  return axios({
    url: '/deposit/check/cumulativeDeposit',
    method: 'get',
    params: {
      coguid: data
    }
  })
}

export function mbwDeposit (data) {
  return axios({
    url: '/deposit/mbw',
    method: 'post',
    data: data
  })
}

export function mbwCheck () {
  return axios({
    url: '/deposit/mbw/check',
    method: 'get'
  })
}

export function xcoinsCheck (params) {
  return axios({
    url: '/deposit/xcoins/check',
    method: 'get',
    params
  })
}

export function xcoinsDeposit (data) {
  return axios({
    url: '/deposit/xcoins',
    method: 'post',
    data: data
  })
}

export function monetixCheck () {
  return axios({
    url: '/deposit/monetix/check',
    method: 'get'
  })
}

export function monetixDeposit (data) {
  return axios({
    url: '/deposit/monetix',
    method: 'post',
    data: data
  })
}

export function monetixPhpPayMethod() {
  return axios({
    url: '/deposit/monetix/payMethodList',
    method: 'get'
  })
}

export function monetixDepositPhp (data) {
  return axios({
    url: '/deposit/monetix/php',
    method: 'post',
    data: data
  })
}

export function monetixMYRCheck () {
  return axios({
    url: '/deposit/monetix/myr/check',
    method: 'get'
  })
}


export function monetixMYRDeposit (data) {
  return axios({
    url: '/deposit/monetix/myr',
    method: 'post',
    data: data
  })
}

export function jpBankDeposit (data) {
  return axios({
    url: '/deposit/jpBank',
    method: 'post',
    data: data
  })
}

export function jpBankCheck () {
  return axios({
    url: '/deposit/jpBank/check',
    method: 'get'
  })
}
export function mybanqCheck () {
  return axios({
    url: '/deposit/mybanq/check',
    method: 'get'
  })
}

export function mybanqDeposit (data) {
  return axios({
    url: '/deposit/mybanq',
    method: 'post',
    data: data
  })
}

export function mybanqDepositConfirm (data) {
  return axios({
    url: '/deposit/mybanq/confirm',
    method: 'post',
    data: data
  })
}

export function maxBankDepositConfirm (data) {
  return axios({
    url: '/deposit/mybanq/confirm',
    method: 'post',
    data: data
  })
}

export function jpConnectDepositConfirm (data) {
  return axios({
    url: '/deposit/jpConnect/confirm',
    method: 'post',
    data: data
  })
}

export function cryptoFundingDeposit (data) {
  return axios({
    url: '/deposit/cryptoFunding',
    method: 'post',
    data: data
  })
}

export function cryptoFundingCheck () {
  return axios({
    url: '/deposit/cryptoFunding/check',
    method: 'get'
  })
}

export function cryptoFundingDepositConfirm (data) {
  return axios({
    url: '/deposit/cryptoFunding/confirm',
    method: 'get',
    params: {
      txid: data.txid,
      orderId: data.orderId
    }
  })
}

export function awepayDeposit (data) {
  return axios({
    url: '/deposit/awepay',
    method: 'post',
    data: data
  })
}

export function awepayCheck () {
  return axios({
    url: '/deposit/awepay/check',
    method: 'get'
  })
}

export function getRegisteredCreditCard (accountMyguid) {
  return axios({
    url: '/depositCard/cardList',
    method: 'get',
    params: {
      accountMyguid: accountMyguid
    }
  })
}

export function CreditCardDeposit (data) {
  return axios({
    url: '/deposit/creditCard',
    method: 'post',
    data: data
  })
}

export function creditCardCheck () {
  return axios({
    url: '/deposit/creditCard/check',
    method: 'get'
  })
}

export function creditCardDepositConfirm (data) {
  return axios({
    url: '/deposit/creditCard/confirm',
    method: 'post',
    data: data
  })
}


export function addCard (data) {
  return axios({
    url: '/depositCard/addCard',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function grandPayCheck () {
  return axios({
    url: '/deposit/grandpay/check',
    method: 'get'
  })
}

export function grandPayDeposit (data) {
  return axios({
    url: '/deposit/grandpay',
    method: 'post',
    data
  })
}

export function grandPayDepositConfirm (data) {
  return axios({
    url: '/deposit/grandpay/confirm',
    method: 'post',
    data
  })
}

export function crypto365Validate (myguid) {
  return axios({
    url: 'deposit/crypto365/validate',
    method: 'get',
    params: {
      myguid: myguid
    }
  })
}

export function getCard (currentPage, pageSize, accountMyguid) {
  return axios({
    url: '/depositCard/getCard',
    method: 'get',
    params: { accountMyguid, currentPage, pageSize }
  })
}

export function removeCard (data) {
  return axios({
    url: '/depositCard/removeCard',
    method: 'post',
    data: data
  })
}

export function crypto365Deposit (data) {
  return axios({
    url: '/deposit/crypto365',
    method: 'post',
    data: data
  })
}

export function crypto365Check () {
  return axios({
    url: '/deposit/crypto365/check',
    method: 'get'
  })
}

export function ppsDBCheck (channel) {
  return axios({
    url: '/deposit/ppsDBCheck',
    method: 'get',
    params: { channel }
  })
}

export function orderConfirmationRmb2 (data) {
  return axios({
    url: '/deposit/orderConfirmation',
    method: 'post',
    data: data
  })
}

export function myPayGetOrder (mypayGuid) {
  return axios({
    url: '/deposit/mypay/getRecord',
    method: 'get',
    params: { mypayGuid }
  })
}

export function getMyPayMethods (channel) {
  return axios({
    url: '/deposit/mypay/getChannelPayType',
    method: 'get',
    params: { channel }
  })
}


