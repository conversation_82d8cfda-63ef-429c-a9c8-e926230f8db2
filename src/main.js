import '@babel/polyfill'
import Vue from 'vue'
import 'mutationobserver-shim'
import './Utils/fliter'
import App from './App.vue'
import router from './router'
import store from './store'
import <PERSON> from 'rapha<PERSON>/raphael'
import ElementUI from 'element-ui'
import '@/assets/scss/element-style.scss'
import './plugins'
import './registerServiceWorker'
import AlgoliaComponents from 'vue-instantsearch'
import i18n from './i18n'
import './directives'
import { checkLocal } from '@/Utils/authLocalStorage'
import './assets/style/icon/iconfont.css'
import './Utils/Validate'
import WotDesign from 'wot-design'
import 'wot-design/lib/theme-default/index.css'
import '@/assets/css/messageBox.css'
import ViewPoint from '@/plugins/viewpoint'
import { versionPrompt } from '@/components/VersionPrompt'
import '@/assets/css/button-common-shadow.css'

global.Raphael = <PERSON>.use(AlgoliaComponents)
Vue.use(WotDesign)

Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value)
})

Vue.prototype.$message = ElementUI.Message
// Vue.use(Form)
// Vue.use(FormItem)
// Vue.use(Checkbox)
// Vue.use(CheckboxGroup)
Vue.prototype.$msgbox = ElementUI.MessageBox
Vue.prototype.$confirm = ElementUI.MessageBox.confirm

// 版本提示
window.CO_VERSION = '1.0.109'
Vue.prototype.$versionPrompt = versionPrompt

// 埋点插件
Vue.use(ViewPoint, { i18n })

Vue.config.productionTip = false
setInterval(() => {
  checkLocal()
}, 60 * 1000)

const vm = new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')

window.vm = vm

// if (window.caches) {
//   (async function () {
//     const cs = await window.caches.keys()
//     console.log(cs, '执行清除缓存')
//     for (const v of cs) {
//       await caches.delete(v)
//     }
//     // location.reload()
//   })()
// }

export default vm
