import getters from './getters'
import actions from './actions'
import mutations from './mutations'

const state = {

  LPOAUpdateShow: false,

  // --------------

  lang: null,
  langOption: [
    {
      title: 'English',
      value: 'en_US',
      image: require('../../assets/images/small/flag-199.png')
    },
    /* { title: 'Arabic', value: 'ar', image: require('../../assets/images/small/flag-500.png') }, */
    {
      title: '简体中文',
      value: 'zh_CN',
      image: require('../../assets/images/small/flag-300.png')
    },
    {
      title: '日本語',
      value: 'ja_JP',
      image: require('../../assets/images/small/flag-06.png')
    },
    {
      title: '繁體中文',
      value: 'zh_TW',
      image: require('../../assets/images/small/flag-300.png')
    },
    {
      title: 'ภาษาไทย',
      value: 'th',
      image: require('../../assets/images/small/flag-07.png')
    },
    {
      title: 'Tiếng V<PERSON>',
      value: 'vi',
      image: require('../../assets/images/small/flag-08.jpg')
    }
    /* { title: 'Hindi', value: 'hi', image: require('../../assets/images/small/flag-100.png') },
    { title: 'Greek', value: 'gr', image: require('../../assets/images/small/flag-400.png') },
    { title: 'Franch', value: 'fr', image: require('../../assets/images/small/flag-200.png') } */
  ],
  colors: [
    { primary: '#827af3', primaryLight: '#b47af3', bodyBgLight: '#efeefd', bodyBgDark: '#1d203f' },
    { primary: '#e07af3', primaryLight: '#f37ab7', bodyBgLight: '#f7eefd', bodyBgDark: '#1d203f' },
    { primary: '#6475c7', primaryLight: '#7abbf3', bodyBgLight: '#eaf5ff', bodyBgDark: '#1d203f' },
    { primary: '#c76464', primaryLight: '#f3c37a', bodyBgLight: '#fff8ea', bodyBgDark: '#1d203f' },
    { primary: '#c764ad', primaryLight: '#de8ba9', bodyBgLight: '#ffeaf5', bodyBgDark: '#1d203f' },
    { primary: '#64c7ac', primaryLight: '#a3f37a', bodyBgLight: '#f0ffea', bodyBgDark: '#1d203f' },
    { primary: '#8ac764', primaryLight: '#dbf37a', bodyBgLight: '#f7ffea', bodyBgDark: '#1d203f' }
  ],
  authUser: {
    auth: false,
    authType: false,
    user: {}
  },
  users: {
    country: '',
    firstname: '',
    gender: '',
    city: '',
    ibcode: '',
    accounttype: '',
    parentib: '',
    annualincome: '',
    wheremyfx: '',
    employmentstatus: '',
    tradingdeskpassword: '',
    postalcode: '',
    firstnamecn: '',
    tel: '',
    id: 1,
    email: '',
    dateofapprove: '',
    createtime: '',
    issalesportaluser: '',
    address: '',
    myguid: '',
    lastnamecn: '',
    isupdated: '',
    lastname: '',
    tradingexp: '',
    nationality: '',
    companyname: '',
    isregcompleted: '',
    isok: '',
    idcard: '',
    comment: '',
    updatetime: ''
  },
  captcha: {
    token: '',
    codeImage: ''
  },
  activePage: {
    name: 'Dashboard',
    breadCrumb: [
      {
        html: '<i class="ri-home-4-line mr-1 float-left"></i>Home',
        to: { name: 'dashboard.home-1' }
      },
      {
        text: '',
        href: '#'
      }
    ]
  },
  questionnaireInfo: {
    questionnaireToken: '',
    myguid: ''
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
