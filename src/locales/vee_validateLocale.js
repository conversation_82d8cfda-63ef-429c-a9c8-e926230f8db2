const veeMessages = {
  en_US: {
    validation: {
      required: 'Please enter the {field}',
      selectionValidate: 'Please select {field}',
      email: 'Please enter a valid email',
      checkbox: 'Please tick and agree the terms and conditions',
      passwordInconsistency: 'The two passwords entered are inconsistent',
      passwordComplexity: 'Insufficient password complexity',
      twoDecimal: 'Please enter a number with two decimal places',
      chinaPhoneNumber: 'Please enter a valid China mainland phone number',
      depositAmount: 'The amount must be between {sign}{min} and {sign}{max}'
    }
    // 其他英文消息
  },
  zh_CN: {
    validation: {
      required: '请输入{field}',
      selectionValidate: '请选择{field}',
      email: '请输入正确的邮箱',
      checkbox: '请勾选并同意条款及条件',
      passwordInconsistency: '两次输入的密码不一致',
      passwordComplexity: '密码复杂度不够',
      twoDecimal: '请输入两位小数的数字',
      chinaPhoneNumber: '请输入一个有效的中国大陆电话号码',
      depositAmount: '金额必须在{sign}{min}和{sign}{max}之间'
    }
    // 其他中文消息

  },
  ja_JP: {
    validation: {
      required: '{field}を入力してください',
      selectionValidate: '{field}を選択してください',
      email: 'メールアドレスのフォーマットを確認してください',
      checkbox: '利用規約に同意してください',
      passwordInconsistency: 'パスワードが一致しません',
      passwordComplexity: '大文字、小文字、数字を組み合わせて8文字以上にしてください',
      twoDecimal: '少数点以下2桁の数字を入力してください',
      chinaPhoneNumber: '中国の電話番号を入力してください',
      depositAmount: '入金額は{sign}{min}～{sign}{max}の範囲で入力してください'
    }
  },
  zh_TW: {
    validation: {
      required: '請輸入{field}',
      selectionValidate: '請選擇{field}',
      email: '請輸入正確的郵箱',
      checkbox: '請勾選並同意條款及條件',
      passwordInconsistency: '兩次輸入的密碼不一致',
      passwordComplexity: '密碼複雜度不够',
      twoDecimal: '請輸入兩位小數的數位',
      chinaPhoneNumber: '請輸入一個有效的中國大陸電話號碼',
      depositAmount: '金額必須在{sign}{min}和{sign}{max}之間'
    }
  },
  th: {
    validation: {
      required: 'กรุณากรอก{field}',
      selectionValidate: 'กรุณาเลือก{field}',
      email: 'กรุณากรอกกล่องจดหมายที่ถูกต้อง',
      checkbox: 'กรุณาทำเครื่องหมายและยอมรับข้อกำหนดและเงื่อนไข',
      passwordInconsistency: 'รหัสผ่านที่ป้อนทั้งสองครั้งไม่สอดคล้องกัน',
      passwordComplexity: 'ความซับซ้อนของรหัสผ่านไม่เพียงพอ',
      twoDecimal: 'กรุณาใส่ตัวเลขสองทศนิยม',
      chinaPhoneNumber: 'กรุณากรอกหมายเลขโทรศัพท์ที่ถูกต้องในจีนแผ่นดินใหญ่',
      depositAmount: 'จำนวนเงินต้องอยู่ระหว่าง {sign}{min} และ {sign}{max}'
    }
  },
  vi: {
    validation: {
      required: 'Vui lòng nhập {field}',
      selectionValidate: 'Vui lòng chọn {field}',
      email: 'Vui lòng nhập đúng hộp thư',
      checkbox: 'Vui lòng đánh dấu và đồng ý với Điều khoản&Điều kiện',
      passwordInconsistency: 'Mật khẩu nhập hai lần không phù hợp',
      passwordComplexity: 'Mật khẩu không đủ phức tạp',
      twoDecimal: 'Vui lòng nhập hai số thập phân',
      chinaPhoneNumber: 'Vui lòng nhập số điện thoại hợp lệ của Trung Quốc đại lục',
      depositAmount: 'Số tiền phải nằm giữa {sign}{min} và {sign}{max}'
    }
  }
}

export default veeMessages
