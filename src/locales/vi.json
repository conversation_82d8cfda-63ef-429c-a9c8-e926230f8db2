{"sidebar": {"profile": "<PERSON><PERSON> s<PERSON>", "topic": "chủ đề", "dashboard": "Thông tin dữ liệu", "dashboard1": "Bảng điều khiển1", "dashboard2": "2<PERSON><PERSON><PERSON> đi<PERSON><PERSON> k<PERSON>n", "videoChat": "<PERSON><PERSON><PERSON> video", "app": "Ứng dụng", "eCommerce": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "productListing": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "productDetail": "chi ti<PERSON>t c<PERSON>a sản phẩm", "checkout": "thanh toán", "wishlist": "<PERSON><PERSON> s<PERSON>ch mong muốn", "socialApp": "Ứng dụng cho xã hội", "todo": "Todo", "projectManagement": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON>n", "email": "<PERSON><PERSON><PERSON> đi<PERSON>n tử", "inbox": "<PERSON><PERSON><PERSON> thư đến email", "emailCompose": "<PERSON><PERSON><PERSON><PERSON> phần email", "calendar": "l<PERSON><PERSON>", "user": "ng<PERSON><PERSON><PERSON> dùng", "userProfile": "<PERSON><PERSON> s<PERSON> ng<PERSON>ời dùng", "userEdit": "Chỉnh sửa người dùng", "userAdd": "<PERSON><PERSON><PERSON><PERSON> dùng tham gia", "userList": "<PERSON><PERSON> s<PERSON>ch ng<PERSON><PERSON> dùng", "components": "<PERSON><PERSON><PERSON> th<PERSON>nh p<PERSON>n", "uiElements": "Phần tử UI", "color": "<PERSON><PERSON><PERSON>", "typography": "ki<PERSON>u chữ", "alert": "b<PERSON><PERSON> động", "badges": "phù hiệu", "breadcrumb": "<PERSON><PERSON><PERSON><PERSON>", "button": "n<PERSON><PERSON> b<PERSON>m", "cards": "Thẻ", "carousel": "Carousel", "video": "video", "grid": "<PERSON><PERSON><PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON>", "listGroup": "<PERSON><PERSON> s<PERSON>ch nhó<PERSON>", "media": "<PERSON><PERSON><PERSON><PERSON><PERSON> thông", "modal": "mô hình", "notifications": "thông báo", "pagination": "số trang", "popovers": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> lên", "progressBars": "<PERSON>h tiến trình", "tabs": "<PERSON><PERSON><PERSON><PERSON>", "tooltips": "g<PERSON>i <PERSON>", "forms": "<PERSON><PERSON><PERSON> thức", "formElements": "<PERSON><PERSON><PERSON> y<PERSON>u tố hình thành", "formValidation": "<PERSON><PERSON><PERSON> minh hình thức", "formSwitch": "<PERSON><PERSON><PERSON> công tắc", "formRadio": "<PERSON><PERSON><PERSON> phát thanh ch<PERSON>h thức", "formCheckbox": "h<PERSON>nh thành hộp", "table": "b<PERSON>ng", "basicTable": "bả<PERSON> c<PERSON> bản", "dataTable": "bảng dữ liệu", "editable": "<PERSON><PERSON><PERSON> tập viên", "charts": "bi<PERSON><PERSON> đ<PERSON>", "highChart": "<PERSON><PERSON><PERSON><PERSON> đồ độ cao", "amChart": "<PERSON><PERSON><PERSON><PERSON> đồ của tôi", "apexChart": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> của <PERSON>", "icons": "<PERSON><PERSON><PERSON>", "dripicons": "bi<PERSON>u tượng dripicons", "fontAwsome5": "phông chữ. 5", "lineAwsomeIcon": "lineAwsomeIcon", "rimixIcon": "Remixicon", "unicon": "unicon", "treeView": "nh<PERSON>n c<PERSON>y", "pages": "c<PERSON>c trang web", "authentication": "x<PERSON>c thực", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký", "recoverPassword": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> mật kh<PERSON>u", "confirmMail": "<PERSON><PERSON><PERSON> thư", "lockScreen": "<PERSON><PERSON><PERSON><PERSON> màn hình", "maps": "b<PERSON><PERSON>", "googleMap": "<PERSON><PERSON>n <PERSON>", "extraPages": "<PERSON><PERSON><PERSON> trang <PERSON> b<PERSON> sung", "timeline": "<PERSON><PERSON><PERSON> thời gian", "invoice": "h<PERSON><PERSON> đ<PERSON>n", "blankPage": "trang tr<PERSON>ng", "error404": "Lỗi 404", "error500": "Lỗi 500", "pricing": "<PERSON><PERSON><PERSON> giá", "pricing1": "giá 1", "maintenance": "b<PERSON>o trì", "comingSoon": "<PERSON><PERSON><PERSON> đến sớm", "faq": "Câu hỏi thường gặp", "plugins": "plugin", "datepicker": "<PERSON><PERSON><PERSON> ch<PERSON>n ng<PERSON>y", "select": "l<PERSON><PERSON> ch<PERSON>n", "draggable": "kéo", "menuLevel": "của lớp menu", "menuOne": "Thực đơn 1", "menuTwo": "Thực đơn 2", "menuThree": "Thực đơn 3", "menuFour": "Thực <PERSON>ơ<PERSON> 4", "submenu": "<PERSON><PERSON> c<PERSON>a con", "submenuOne": "<PERSON><PERSON><PERSON><PERSON> con 1", "submenuTwo": "<PERSON><PERSON><PERSON><PERSON> con 2", "submenuThree": "<PERSON><PERSON><PERSON><PERSON> con 3", "myAccount": "<PERSON><PERSON> s<PERSON>ch các tà<PERSON>", "accountInformation": "Thông tin tài k<PERSON>n", "myDocument": "<PERSON><PERSON><PERSON><PERSON> lý tập tin", "vault": "ti<PERSON><PERSON> g<PERSON>i", "accountTransactions": "Tiền g<PERSON>&<PERSON><PERSON><PERSON><PERSON> k<PERSON>n nội bộ", "deposit": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "withdrawal": "<PERSON><PERSON><PERSON> ti<PERSON>n", "internalFundTransfer": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n n<PERSON> bộ", "transactionHistory": "<PERSON><PERSON><PERSON> sử giao dịch", "MYFXe-Account": "MYFX e-Account", "accountSettings": "Cài đặt tài k<PERSON>n", "resetCoPassword": "Đặt lại mật khẩu đăng nhập", "openAdditionalAccount": "Mở tài kho<PERSON>n giao dịch mới", "changeLeverage": "<PERSON><PERSON><PERSON> đổi đòn b<PERSON>y tài khoản giao dịch", "platform": "<PERSON><PERSON><PERSON> tảng giao d<PERSON>ch", "mt4Download": "Tải MT4 Client", "mt5Download": "Tải MT5 Client", "webTrader": "<PERSON><PERSON><PERSON> d<PERSON>ch trên web", "ibPortal": "Trung tâm IB", "siteNewsComponent": "<PERSON> tức trang web", "siteNews": "<PERSON> tức trang web", "promotion": "<PERSON><PERSON><PERSON> đ<PERSON>", "campaign": "<PERSON><PERSON><PERSON> dịch qu<PERSON>ng bá", "ex-DividendList": "<PERSON><PERSON> EX-DividendList", "contactUs": "<PERSON><PERSON><PERSON> h<PERSON> với chúng tôi", "emailUs": "<PERSON><PERSON><PERSON> qua email", "liveChat": "<PERSON><PERSON><PERSON> hệ trực tuyến", "officialWebSite": "Trang <PERSON> ch<PERSON>h thức", "backToOldVersion": "trở lại phiên bản cũ", "name": "tên", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "next": "ch<PERSON><PERSON> ch<PERSON>n", "next1": "ch<PERSON><PERSON> ch<PERSON>n", "next2": "ch<PERSON><PERSON> ch<PERSON>n", "submit": "<PERSON><PERSON> trình", "back": "l<PERSON>i lại", "ok": "<PERSON><PERSON><PERSON> th<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "return": "trở về", "add": "thêm", "add_account": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n thẻ ngân hàng", "confirm": "<PERSON><PERSON><PERSON>n", "confirm1": "<PERSON><PERSON><PERSON>n", "confirm2": "<PERSON><PERSON><PERSON>n", "address": "Địa chỉ", "link": "Đặt lại", "success": "<PERSON><PERSON>g tôi đã nhận đư<PERSON><PERSON> yêu cầu của bạn thành công", "error": "Oops.something bị lỗi", "empty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "back_home_page": "trở về trang đầu", "ArchiveTradeHistory": "<PERSON><PERSON><PERSON> tr<PERSON> lịch sử thư<PERSON><PERSON> mại", "partnerPortal": "<PERSON><PERSON><PERSON> thông tin đối tác", "headerBtn": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "tradingTools": "<PERSON><PERSON>ng cụ giao dịch", "acuityTools": "<PERSON>ông cụ độ chính xác", "analysisIQ": "Phân tích IQ", "economicCalendar": "<PERSON><PERSON><PERSON> kinh tế", "downloadsAndLinks": "<PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> kết xã hội"}, "account_information": {"show_hidden_account": "t<PERSON><PERSON>", "show_visible_account": "hiển thị tài k<PERSON>n", "floating": "floating and loss (lãi và lỗ nổi)", "mt4login": "Tài khoản MT4", "mt5login": "Tài khoản MT5", "account_type": "<PERSON><PERSON><PERSON> tà<PERSON>", "leverage": "<PERSON><PERSON><PERSON> b<PERSON>y", "currency": "ti<PERSON><PERSON> tệ", "hide_account": "<PERSON><PERSON><PERSON>", "balance_floating_pnl": "<PERSON><PERSON> dư & <PERSON><PERSON><PERSON> nhu<PERSON>n nổi", "reset_mt_password": "Thay đổi mật khẩu tài khoản này", "withdrawal": "<PERSON><PERSON><PERSON> ti<PERSON>n", "deposit": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "empty": "<PERSON><PERSON><PERSON> cả các tài khoản đã bị <PERSON>n", "exit": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> r<PERSON> lui", "recover": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hồi", "join": "<PERSON><PERSON> gia lại", "beingProcessed": "<PERSON><PERSON> lý", "recoveryBeingProcessed": "<PERSON>u<PERSON> trình khôi phục đang được xử lý", "joinBeingProcessed": "<PERSON><PERSON> tiến hành đăng ký lại", "exitMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tho<PERSON>", "recoverMessage": "Bạn có muốn khôi phục lại không", "dialog": {"title": "Thay đ<PERSON>i {type} mật khẩu tài khoản giao dịch -", "question1": "<PERSON><PERSON> lòng xác minh câu hỏi bảo mật của bạn", "question2": "<PERSON><PERSON> lòng thiết lập mới {type} M<PERSON>t khẩu đăng nhập。", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "success": "Thay đổi mật khẩu đã hoàn tất", "close": "<PERSON><PERSON><PERSON>"}, "msgBox": {"sure": "ch<PERSON><PERSON> ch<PERSON>n", "cancel": "Hủy bỏ"}, "recoveryAccountSuccessMessage": "<PERSON><PERSON><PERSON> kho<PERSON>n không hoạt động đã đư<PERSON><PERSON> khôi phục và gửi thư", "Table": {"column_01": "<PERSON><PERSON><PERSON>", "column_02": "<PERSON><PERSON><PERSON> tà<PERSON>", "column_03": "<PERSON><PERSON><PERSON> d<PERSON>", "column_04": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "column_05": "<PERSON><PERSON><PERSON><PERSON> thái", "column_06": "Sự cân bằng", "column_07": "Pnl nổi", "column_08": "<PERSON><PERSON><PERSON>", "column_05_01": "Pnl số dư/thả nổi", "column_05_02": "<PERSON><PERSON> t<PERSON>t cả", "column_05_03": "<PERSON><PERSON> số dư / lãi thả nổi", "viewAll": "Số dư ∙ Chế độ xem lãi lỗ thả nổi", "accountType": {"Standard": "Standard", "MAM Slave": "MAM Slave", "PAMM Slave": "PAMM Slave", "Affiliate Rebate": "Affiliate Rebate", "Pro": "Pro", "Crypto": "Crypto", "Rebate": "Rebate", "PAMM": "PAMM", "PAMM Master": "PAMM Master", "competition": "competition", "MAM Master": "MAM Master", "st_mt5": "st_mt5", "contest2023jp": "contest2023jp", "mt4_micro_standard": "Tài khoản Micro MT4 Standard", "DEMO": "DEMO", "pro_mt5": "pro_mt5", "Copy Trade": "Copy Trade"}}, "Status": {"status_1": "<PERSON><PERSON><PERSON>", "status_0": "chỉ đọc", "status_-1": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "status_-2": "xóa bỏ", "status_-3": "<PERSON><PERSON> lưu trữ", "status_-4": "<PERSON><PERSON><PERSON> trữ và xóa", "status_-5": "<PERSON><PERSON><PERSON><PERSON> phục tài k<PERSON>n MT4"}}, "mydocument": {"verifyTip1": "<span style=\"font-weight: bold; color: #000000\">Chưa xác minh:</span> <PERSON><PERSON> lòng tải lên các tập tin sau", "verifyTip2": "<PERSON><PERSON><PERSON> thực danh t<PERSON>h", "title": "<PERSON><PERSON> hoàn thành yêu cầu tài khoản của bạn, vui lòng tải lên các tài liệu sau", "attention": "chú ý：", "test1": "1. <PERSON><PERSON><PERSON> tệp đ<PERSON> phép：PDF, JPG, JPEG, PNG, BMP, GIF", "test2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> tập tin tải lên tối đa：5MB", "individual": {"load1": {"title1": "1. Bằng chứng nhận dạng", "title2": "tài liệu nhận dạng do chính phủ cấp kèm theo <PERSON>:", "test1": "1. <PERSON><PERSON><PERSON><PERSON> ph<PERSON>p lái xe", "test2": "2. <PERSON><PERSON>", "test3": "3. <PERSON><PERSON><PERSON>h thư", "test4": "(<PERSON><PERSON>u ý: Thẻ sinh viên và thẻ Medicare không được chấp nhận làm bằng chứng nhận dạng hợp lệ.)"}, "load2": {"title1": "2. <PERSON><PERSON><PERSON> <PERSON>h địa chỉ", "title2": "<PERSON><PERSON><PERSON> liệu nhận dạng hiển thị tên, đị<PERSON> chỉ hiện tại và ngày cấp:", "test1": "1. <PERSON><PERSON><PERSON> đơn tiện ích và giấy phép cư trú (<PERSON><PERSON><PERSON><PERSON> cấp trong vòng ba tháng)", "test2": "2. <PERSON><PERSON><PERSON> đơn điện tho<PERSON> (trong vòng 3 tháng)", "test3": "3. <PERSON><PERSON><PERSON> thông b<PERSON>o chung", "test4": "(<PERSON><PERSON><PERSON> ý: Thẻ Medicare không đủ điều kiện để xác minh.)", "test5": "*<PERSON><PERSON>t bản sao bằng chứng về địa chỉ, chẳng hạn như hóa đơn ngân hàng hoặc hóa đơn tiện ích, sẽ đư<PERSON><PERSON> phát hành trong vòng 3 tháng."}}, "company": {"load1": {"title1": "1. <PERSON> ti<PERSON>t công ty", "title2": "<PERSON><PERSON><PERSON> chắc chắn rằng tập tin được tải lên có chứa nội dung:", "test1": "1. <PERSON><PERSON><PERSON><PERSON> chứng nhận đăng ký công ty", "test2": "2. Bằng chứng về địa chỉ văn phòng kinh doanh (hóa đơn tiện ích hoặc bản sao kê ngân hàng hiển thị tên công ty và địa chỉ văn phòng kinh doanh trong vòng ba tháng gần nhất)", "test3": "3. <PERSON><PERSON><PERSON> sao sổ đăng ký gi<PERSON>m đốc (nếu c<PERSON>)", "test4": "4. <PERSON><PERSON><PERSON> sao danh sách cổ đông (n<PERSON><PERSON> có)"}, "load2": {"title1": "2. <PERSON><PERSON><PERSON><PERSON> tiết", "title2": "<PERSON><PERSON><PERSON> chắc chắn rằng tập tin được tải lên có chứa nội dung:", "test1": "1. <PERSON><PERSON><PERSON> sao đ<PERSON>ng ký giám đốc", "test2": "2. <PERSON><PERSON><PERSON> minh địa chỉ của giám đốc (sao kê ngân hàng hoặc hóa đơn tiện ích, ng<PERSON><PERSON> phát hành trong vòng 3 tháng, địa chỉ và tên của bạn)"}, "load3": {"title1": "3. <PERSON> tiết cổ đông", "title2": "<PERSON><PERSON><PERSON> chắc chắn rằng tập tin được tải lên có chứa nội dung:", "test1": "1. <PERSON><PERSON><PERSON> sao sổ đăng ký cổ đông (nế<PERSON> có)", "test2": "2. <PERSON><PERSON><PERSON> minh địa chỉ cổ đông (nếu có)"}}, "test3": "<PERSON><PERSON><PERSON> tập tin vào đây, hoặc", "click_upload": "Click vào Upload file", "file_list": "<PERSON><PERSON> s<PERSON>ch tập tin", "update_button": "<PERSON><PERSON><PERSON>", "table": {"title": "<PERSON><PERSON><PERSON> lên tài liệu", "comment": "<PERSON><PERSON><PERSON>", "create_time": "<PERSON><PERSON><PERSON><PERSON> gian tải lên", "file_name": "<PERSON><PERSON><PERSON> tập tin", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusList": {"Approved": "<PERSON><PERSON><PERSON> to<PERSON> thông qua", "Pending": "<PERSON><PERSON> đ<PERSON> xem xét", "Approval Failed": "<PERSON><PERSON><PERSON> giá không đ<PERSON><PERSON><PERSON> thông qua", "Removed": "Xoá"}, "typeList": {"1": "<PERSON><PERSON><PERSON> minh danh t<PERSON>h", "2": "<PERSON><PERSON><PERSON> minh địa chỉ", "3": "<PERSON> tiết công ty", "4": "<PERSON><PERSON><PERSON><PERSON> đốc <PERSON> tiết", "5": "<PERSON> tiết cổ đông"}, "uploadSuccess": "Upload thành công"}, "deleteDesc": {"operation": "<PERSON><PERSON><PERSON> đ<PERSON>", "buttonText": "Xóa", "confirmTip": "Hành động này sẽ xóa vĩnh viễn tệp, bạn có muốn tiếp tục không?", "confirmTitle": "G<PERSON><PERSON> ý", "confirmButtonText": "OK", "cancelButtonText": "Hủy bỏ", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công"}}, "reset_password": {"title": "thay đ<PERSON>i mật kh<PERSON>u <PERSON>", "step1": "Bước 1", "step2": "Bước 2", "step3": "Bước 3", "description1": "Bước 1：<PERSON><PERSON> lòng nhập mật khẩu hiện tại", "description2": "Bước 2：<PERSON><PERSON> lòng nhập câu trả lời b<PERSON>o mật", "description3": "Bước 3：<PERSON><PERSON> lòng nhập mật khẩu mới", "label1": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "label2": "<PERSON><PERSON><PERSON> đ<PERSON> bảo mật", "label3": "<PERSON><PERSON><PERSON> trả lời", "label4": "<PERSON><PERSON><PERSON> mới", "label5": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "label6": "mã x<PERSON>c n<PERSON>n", "eight_character": "Chiều dài tối thiểu 8 bit", "one_number": "m<PERSON>t s<PERSON>", "one_lowerCase": "một chữ cái viết hoa", "one_upperCase": "một chữ cái viết thường"}, "vault": {"title1": "h<PERSON>m t<PERSON>", "title2": "Thông tin về Vault", "test": "<PERSON>ho của bạn dựa trên tiền tệ của tài khoản lưu ký MT4 của bạn.", "button1": "<PERSON><PERSON><PERSON>", "button2": "<PERSON><PERSON><PERSON><PERSON> tiền", "table": {"title": "<PERSON><PERSON><PERSON> sử số dư kho", "date": "th<PERSON>i gian", "currency": "ti<PERSON><PERSON> tệ", "amount": "s<PERSON> tiền", "type": "<PERSON><PERSON><PERSON>", "comment": "<PERSON><PERSON><PERSON>"}, "tips": "ch<PERSON> <PERSON>", "tips_test1": "<PERSON> l<PERSON>t AML/CTF, MYFX Markets không thể gửi tiền cho bên thứ ba. Tất cả số tiền rút từ tài khoản của bạn phải được chuyển đến tài khoản có cùng tên với tài khoản giao dịch MyFX Markets của bạn.", "tips_test2": " <PERSON>u<PERSON> trình rút tiền mất 1-3 ngày và đối với các yêu cầu được thực hiện vào cuối tuần, việc xử lý có thể bị trì hoãn đến ngày làm việc tiếp theo.", "tips_test3": "<PERSON><PERSON><PERSON> công ty bên thứ ba có thể tính phí riêng", "tips_test4": "<PERSON><PERSON><PERSON> tiền tối đa qua bitwallet là 20.000 đô la", "tips_test5": "", "tips_test6": "", "typeList": {"TRANSFER_FROM_MAMPAMM_SLAVE": "<PERSON><PERSON><PERSON><PERSON> tiền từ tài khoản khách hàng", "WITHDRAWAL": "<PERSON><PERSON><PERSON>", "TRANSFER": "chuy<PERSON>n đổi", "manual_withdrawal_b_07122020": "<PERSON><PERSON><PERSON> tiền bằng tay", "ADJUSTMENT": "điều chỉnh", "REFUND BACK FROM BANK": "hoàn tiền từ ngân hàng", "Transfer Back": "Quay lại", "Bank transfer failed": "Chuyển khoản ngân hàng không thành công", "Bank Refund": "hoàn tiền của ngân hàng", "TRANSFER_BACK": "Quay lại", "TRANSFER_BACK_FROM_BANK": "Chuy<PERSON>n kho<PERSON>n ngân hàng", "FUNDS BACK FROM BANK": "<PERSON><PERSON><PERSON> tiền từ ngân hàng"}}, "oata": {"title": "Mở tài kho<PERSON>n giao dịch mới", "label1": "m<PERSON><PERSON> chủ", "label2": "<PERSON><PERSON><PERSON> tà<PERSON>", "label3": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "label4": "IB Code", "label5": "<PERSON><PERSON><PERSON>", "label6": "Trang chủ", "instruction": "*Cài đặt đòn bẩy sẽ giống như cài đặt đòn bẩy cho tài khoản hiện tại của bạn.", "redioLabel1": "<PERSON><PERSON><PERSON><PERSON>", "redioLabel2": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "n<PERSON><PERSON> c<PERSON>", "currency_check": "ti<PERSON>n tệ không hợp lệ", "title2": "<PERSON><PERSON><PERSON><PERSON> công", "title3": "<PERSON><PERSON><PERSON> nhận rằng quá trình phê duyệt tài liệu không đầy đủ", "openSuccessTitle": "<PERSON><PERSON><PERSON><PERSON> công", "sub_title": "<PERSON>ui lòng quay lại trang đầu tiên", "test1": "bằng cách đánh dấu vào ô này, tôi đồng ý với các điều khoản và điều kiện của MYFX Markets", "test2": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n và Điều kiện Thị trường MYFX", "test_2_2": "<PERSON><PERSON><PERSON><PERSON> bố tiết lộ MYFX", "test3": "<PERSON>ui lòng tiếp tục quá trình đăng ký bằng cách tải lên các tệp cần thiết bằng cách sử dụng liên kết sau:", "test4": "<PERSON><PERSON><PERSON> lên tài liệu của bạn", "test5": "<PERSON><PERSON>u bạn đã tải tệp lên, hã<PERSON> đợi một chút cho đến khi quá trình hoàn tất. Kh<PERSON>ch hàng cần gửi lại tài liệu sẽ nhận được từ <EMAIL> Thông báo qua email.", "test6": "<EMAIL>", "ibCodeCheck": {"noData": "<PERSON><PERSON><PERSON> là một mục bắt buộc", "isNumber": "<PERSON><PERSON> lòng nhập chữ cái hoặc số"}}, "platform": {"button_test1": "<PERSON><PERSON><PERSON> xuống 下载", "button_test2": "MT5 Tải xu<PERSON>ng", "button_test3": "<PERSON><PERSON><PERSON> m<PERSON>ng", "wd_button_test1": "MT4 ", "wd_button_test2": "MT5 ", "title1": "<PERSON><PERSON> đi<PERSON>u hành", "title2": "kết th<PERSON><PERSON> di ch<PERSON>n", "trader_test": " <PERSON><PERSON><PERSON> trình du<PERSON>t không ph<PERSON>n hồi, h<PERSON><PERSON> nh<PERSON><PERSON> vào", "trader_test2": "", "desktop_laptop": "phía PC", "smartphone_tablet": "đi<PERSON>n tho<PERSON>i và thiết bị di động", "for_windows": "Windows", "for_mac": "<PERSON>", "for_android": "Android", "for_IOS": "iOS", "type": {"MT4 Download": "MT4 Tải xu<PERSON>ng", "MT5 Download": "MT5 Tải xu<PERSON>ng"}, "hint_01": "Thi<PERSON>t bị đầu cuối MT4/MT5 đư<PERSON><PERSON> cập nhật tự động sau khi cài đặt.", "hint_02": "<PERSON><PERSON><PERSON><PERSON> cần cài đặt lại.。", "desktop": "máy tính để bàn / máy tính xách tay", "mobile": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> thông <PERSON>h / <PERSON><PERSON><PERSON> t<PERSON> bảng", "download": "<PERSON><PERSON><PERSON>", "open": "mở", "goto": "ở đây"}, "emailus": {"title": "<PERSON><PERSON> lòng sử dụng mẫu dưới đây để gửi cho chúng tôi câu hỏi, mối quan tâm hoặc ý kiến của bạn", "label4": "Số tài <PERSON>n", "label5": "Ý kiến của bạn", "test1": "<PERSON><PERSON>g tôi đánh giá cao ý kiến của bạn và đánh giá cao phản hồi của bạn.", "test2": "<PERSON><PERSON>g tôi sẽ trả lời tin nhắn của bạn kịp thời.", "tips": "g<PERSON>i <PERSON>", "tips_test": "Phản hồi của bạn rất quan trọng đối với chúng tôi và chúng tôi đánh giá cao ý kiến của bạn. Hãy yên tâm rằng chúng tôi sẽ trả lời tin nhắn của bạn kịp thời.", "success": "Cảm ơn bạn đã hỏi. <PERSON><PERSON><PERSON> khi xác nhận nội dung, người phụ trách sẽ hồi âm, xin chờ một chút.", "qrcodeTitle": "<PERSON><PERSON>n có thể truy vấn trên LINE.", "topic": {"select": ["Hỗ trợ chung", "<PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON> hàng", "<PERSON><PERSON><PERSON> v<PERSON> tà<PERSON>", "<PERSON><PERSON><PERSON><PERSON> trình đối tác", "<PERSON><PERSON><PERSON> c<PERSON>u của ph<PERSON><PERSON>ng tiện truyền thông", "<PERSON><PERSON> tuân thủ"]}, "vuePhoneNumberInput": {"countrySelectorLabel": "Mã quốc gia", "countrySelectorError": "<PERSON><PERSON><PERSON> quốc gia", "phoneNumberLabel": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "<PERSON><PERSON>"}}, "transaction": {"button_test1": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "button_test2": "<PERSON><PERSON><PERSON>", "button_test3": "<PERSON><PERSON><PERSON><PERSON> tiền nội bộ", "button_test4": "<PERSON><PERSON> sơ giao d<PERSON>ch", "button_test5": "<PERSON><PERSON><PERSON> tr<PERSON>", "Archive": {"account": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "search": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "handle": "<PERSON><PERSON><PERSON> đ<PERSON>", "download": "<PERSON><PERSON><PERSON> về", "hint_account": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "hint_date": "<PERSON><PERSON> lòng chọn ng<PERSON>y", "hint_no_data": "Th<PERSON>i gian này không có dữ liệu！"}, "deposit": {"newYearTips": "<p style=\"margin: 4px;\">&lt;Trong kỳ nghỉ cuối năm và năm mới&gt;</p><p style=\"margin: 4px;\">\n・Đối với các giao dịch chuyển khoản ngân hàng quốc tế và gửi tiền điện tử được thực hiện vào ngày 25 tháng 12 (<PERSON><PERSON><PERSON>), ngày 26 tháng 12 (<PERSON><PERSON><PERSON>) và ngày 1 tháng 1 (<PERSON><PERSON><PERSON>), tiền sẽ được phản ánh vào tài khoản của bạn vào ngày làm việc tiếp theo.</p><p style=\"margin: 4px;\">・Các khoản tiền gửi được thực hiện thông qua các phương thức khác cũng có thể được xử lý vào ngày làm việc tiếp theo hoặc sau đó. Cảm ơn sự thông cảm của bạn.</p>", "status": {"available": "Sẵn có", "maintenance": "<PERSON><PERSON><PERSON> trì"}, "AllForm": {"account": "<PERSON><PERSON><PERSON>", "accountPlaceholder": "<PERSON><PERSON> lòng chọn một tài k<PERSON>n", "accountError_01": "<PERSON><PERSON> lòng chọn một tài k<PERSON>n", "amount": "Số lượng", "amountPlaceholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "amountError_01": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "amountError_02": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> ít hơn số tiền gửi tối thiểu", "amountError_03": "<PERSON><PERSON> tiền gửi tối đa không thể vư<PERSON><PERSON> quá", "currency": "ti<PERSON><PERSON> tệ", "payType": "<PERSON><PERSON><PERSON> thức thanh toán", "payTypePlaceholder": "<PERSON><PERSON> lòng chọn lo<PERSON>i thanh toán", "payTypeError_01": "<PERSON><PERSON> lòng chọn lo<PERSON>i thanh toán", "cryptoCompanyName": "công ty tiền <PERSON>o", "cryptoCompanyNamePlaceholder": "<PERSON><PERSON> lòng nhập công ty tiền <PERSON>o", "cryptoCompanyNameError_01": "<PERSON><PERSON> lòng nhập công ty tiền <PERSON>o", "cryptoCompanyAddress": "Địa chỉ ví", "cryptoCompanyAddressPlaceholder": "<PERSON><PERSON> lòng nhập địa chỉ ví", "cryptoCompanyAddressError_01": "<PERSON><PERSON> lòng nhập địa chỉ ví", "cryptoAmountHint": "Nếu bạn muốn gửi một số tiền vượt quá giới hạn tối đa, chúng tôi sẽ cung cấp cho bạn một địa chỉ ví riêng. Vui lòng liên hệ với đội ngũ Hỗ trợ Khách hàng của chúng tôi qua email để được hỗ trợ.", "chain": "Chuỗi khối", "chainPlaceholder": "<PERSON><PERSON> lòng chọn chuỗi khối", "chainError_01": "<PERSON><PERSON> lòng chọn chuỗi khối", "fullKatakana": "<PERSON><PERSON><PERSON> người gửi tiền (katakana toàn chiều rộng)", "fullKatakanaPlaceholder": "<PERSON><PERSON> lòng nhập tên người gửi tiền", "fullKatakanaError_01": "<PERSON><PERSON> lòng nhập tên người gửi tiền (katakana toàn chiều rộng)", "name": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON> lòng nhập tên tiếng Trung của bạn", "namePlaceholder1": "<PERSON><PERSON> lòng nhập tên", "nameError_01": "<PERSON><PERSON> lòng nhập tên tiếng Trung của bạn", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phonePlaceholder": "<PERSON><PERSON> lòng nhập số điện thoại", "phoneError_01": "<PERSON><PERSON> lòng nhập số điện thoại", "depositCurrency": "<PERSON><PERSON><PERSON><PERSON>", "depositCurrencyPlaceholder": "<PERSON><PERSON> lòng chọn loại tiền gửi", "depositCurrencyError_01": "<PERSON><PERSON> lòng chọn loại tiền gửi", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "paymentMethodPlaceholder": "<PERSON><PERSON> lòng chọn ph<PERSON><PERSON><PERSON> thức thanh toán", "paymentMethodError_01": "<PERSON><PERSON> lòng chọn ph<PERSON><PERSON><PERSON> thức thanh toán", "1": "Thẻ ngân hàng", "5": "<PERSON><PERSON>t mã"}, "title": "<PERSON><PERSON><PERSON> sử nạp tiền", "table": {"processed_date_time": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "transaction_type": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "tra_type": {"Deposit": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "Withdraw": "<PERSON><PERSON><PERSON>"}, "amount": "Số lượng", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "account_number": "Số tài <PERSON>n", "payment_type": "Loại Input", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "sta_type": {"CANCELLED": "<PERSON><PERSON> hủy", "COMPLETED": "<PERSON><PERSON> hoàn thành", "PENDING": "<PERSON><PERSON> chờ thanh toán", "PROCESSING": "<PERSON><PERSON> lý", "MAMPAMM": "MAMPAMM", "PROCESSED": "Đã xử lý"}, "account_type_text": "Ví MYFX"}, "tips": {"title": "Mẹo", "test1": "<PERSON><PERSON> tăng tốc quá trình, h<PERSON><PERSON> đả<PERSON> bảo rằng tất cả thông tin là chính xác và đầy đủ. Chấp nhận tiền gửi AUD, EUR, GBP, JPY và USD.", "test2": "<PERSON><PERSON> tuân thủ các quy định AML/CTF, MyFX Markets chỉ có thể chấp nhận tiền trong tài khoản ngân hàng hoặc thẻ tín dụng của bạn phù hợp với tên tài khoản giao dịch MyFX Markets của bạn.", "test3": "<PERSON><PERSON><PERSON> bê<PERSON> thứ ba không đ<PERSON><PERSON><PERSON> phép gửi tiền.", "test4": "Phương pháp xuất kim về nguyên tắc là phương pháp giống nh<PERSON> khi nhập sổ.", "test5": "Hướng dẫn về đóng góp và đóng góp Chúng tôi đã chuẩn bị hướng dẫn về đóng góp và đóng góp. Download tại đây", "test6": "<PERSON><PERSON><PERSON> xuống <PERSON>ổ tay thu tiền", "test7": "Những ai có tài k<PERSON>n MAM/PAMM vui lòng download tại đây.", "test8": "<PERSON><PERSON><PERSON> xuống <PERSON>ổ tay thu tiền (cho MAM/PAMM).", "test9": "Trong cấu trúc của ti<PERSON>, ph<PERSON><PERSON> mất đến 24 giờ để hoàn thành giao dịch, vì vậy bạn nên dành nhiều thời gian khi thanh toán.", "test10": "<PERSON><PERSON><PERSON><PERSON> nhập và lợi nhuận thông qua tiền ảo đư<PERSON>c thực hiện trong cùng một ví.", "test11": "Bởi vì dịch vụ ở đây có thể phản ánh sự trễ do các nguyên nhân như sự cố hệ thống, v. v., cho nên đề nghị bạn dự phòng đầy đủ thời gian thanh toán.", "test12": "", "test_12_01": "Nếu bạn đang sử dụng thẻ VISA hoặc MASTER, bạn cần chuyển từ thanh toán bằng thẻ tín dụng sang tài khoản bitwallet trong bitwallet trước khi chuyển từ văn phòng khách hàng sang tài khoản giao dịch.", "test_12_02": "<PERSON><PERSON> <PERSON>hi thực hiện thủ tục chuyển tiền vào tài khoản giao dịch, bạn có thể thực hiện thủ tục thanh toán bằng cách gửi ảnh chụp màn hình ghi lại phí xử lý cho chúng tôi.", "test_12_03": "Các thẻ khác ngoài VISA MASTER có thể chọn bitwallet từ văn phòng khách hàng để nhận tiền như thường lệ và làm thủ tục bằng thẻ. <PERSON><PERSON> thủ tục ở đây là tự động gánh vác.", "test13": "<PERSON><PERSON> <PERSON>hi nhấp vào OK, đừng nhấn nút Return của trình duyệt nữa. Tự động chuyển đến trang chương trình thanh toán của bitwallet và quay trở lại trang văn phòng khách hàng khi chương trình hoàn tất.", "test14": "Bởi vì dịch vụ bê<PERSON>, căn cứ vào thời cơ gửi tiền giữa các ngân hàng có phản ánh tình huống tr<PERSON> ho<PERSON>, cho nên đề nghị để lại đường sống gửi tiền.", "test15": "<PERSON><PERSON><PERSON> khoản chuyển tiền chi tiết được phát hành tại mỗi lần yêu cầu chuyển tiền và không thể được sử dụng trong lần chuyển tiền tiếp theo. <PERSON><PERSON> lòng thực hiện yêu cầu đóng góp mỗi lần.", "test16": "Trong trườ<PERSON> hợp số tiền chuyển tiền trên 500.000 yên, khi làm thủ tục chuyển tiền, vui lòng nhập số yêu cầu ghi trong email nhận đơn xin chuyển tiền trước tên người yêu cầu chuyển tiền.", "test17": "<PERSON><PERSON> chuyển tiền thu tại ngân hàng của khách hàng sẽ do khách hàng chi trả.", "test18": "Trong trườ<PERSON> hợp tiền tệ của tài khoản giao dịch không phải là JPY, số tiền chuyển đổi sang JPY sẽ được hiển thị trên trang tiếp theo.", "test19": "Nếu tiền gửi khác với tiền trong tài khoản MT4/MT5 của bạn, tiền sẽ được chuyển đổi sau khi nhận và sau đó được ghi có vào tài khoản MT4/MT5 của bạn.", "test20": "<PERSON><PERSON> lòng đảm bảo bao gồm tài khoản MT4/MT5 của bạn trong trường <PERSON> chú.", "test21": "<PERSON>n lưu ý rằng phí chuyển khoản ngân hàng sẽ do bạn chịu.", "test22": "Xin lưu ý rằng chuyển khoản quốc tế có thể phải chịu thêm phí nếu sử dụng ngân hàng trung gian. Chúng tôi khuyên bạn nên liên hệ với ngân hàng của bạn về bất kỳ khoản phí ngân hàng trung gian tiềm năng nào."}, "step1": "Bước 1", "step2": "Bước 2", "step3": "Bước 3", "description1": "Bước 1: <PERSON><PERSON> lòng chọn lối vào", "description2": "Bước 2: <PERSON><PERSON> lòng nhập thông tin nạp tiền", "description3": "Bước 3: <PERSON><PERSON><PERSON><PERSON> tin nạp tiền của người yêu cầu", "depositWayColumns": {"label1": "Lối vào vàng", "label2": "<PERSON><PERSON><PERSON><PERSON> thái", "label3": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "label4": "Chi phí", "label5": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "jpbank": {"depositMethods": "Chuy<PERSON><PERSON> k<PERSON>n ngân hàng <PERSON>", "status": "<PERSON><PERSON> sẵn", "timeToFund": "30 phút", "fee": "Chỉ phí ngân hàng", "label1": "选择账户", "label2": "<PERSON><PERSON><PERSON> tà<PERSON>", "label3": "Sender Name(Full width KATAKANA)", "label4": "<PERSON><PERSON><PERSON><PERSON>", "label5": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "title": "", "title2": "Chú ý：", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:", "test1": "", "test2": "", "test3": "Số tiền được hiển thị dựa trên tỷ giá hối đoái hiện tại. Xin lưu ý rằng số tiền này có thể được tính toán lại trong quá trình và có thể khác với số tiền gửi thực tế.", "test4": "", "test5": "", "test6": "", "test7": "", "test13": "", "test14": "", "test8": "<PERSON><PERSON> lưu ý, do hệ thống bận rộn, v. v. c<PERSON> thể gây ra sự chậm trễ trong vi<PERSON><PERSON> nạ<PERSON> tiền, đ<PERSON> tr<PERSON>h sự chậm tr<PERSON> không c<PERSON><PERSON> thiế<PERSON>, xin vui lòng nhập thông tin chính xác và đầy đủ.", "test9": "", "test10": "", "test11": "", "test12": "", "hitTitle": "Important Notice", "hitDesc": "Due to a current system issue with our bank, confirmation of incoming payments and the reflection of deposits may take longer than usual.\nIn particular, deposits made during late-night hours may not be reflected until after 9:00 AM the following day. We apologize for the inconvenience and kindly ask for your understanding that there may be a delay before your deposit is reflected."}, "bitwallet": {"depositMethods": "Bitwallet", "status": "<PERSON><PERSON> sẵn", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label1_placeholder": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label1_error": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label2": "Số lượng", "label2_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label2_error": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label3": "<PERSON><PERSON><PERSON><PERSON>", "label3_placeholder": "<PERSON><PERSON> lòng chọn tiền gửi", "label3_error": "<PERSON><PERSON> lòng chọn tiền gửi", "label4": "bitwallet <PERSON><PERSON> tiền nạp", "label5": "<PERSON><PERSON> tiền nạp vào tài khoản giao dịch", "label6": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "test1": "<PERSON><PERSON><PERSON><PERSON> tệ:", "test2": "<PERSON>u đ<PERSON>y là thông tin xác nhận của bạn về giao dịch này：", "test3": "<PERSON><PERSON> tiền được hiển thị dựa trên tỷ giá hối đoái hiện tại.", "test4": "Xin lưu ý rằng số tiền này có thể được tính toán lại trong quá trình và có thể khác với số tiền gửi thực tế.", "title": "Chú ý： ", "test5": "Hãy chú ý đến thời gian khi gửi tiền và để giảm thiểu sự chậm trễ, hãy đảm bảo rằng tất cả các chi tiết đều đầy đủ và chính xác.", "test6": "", "test7": "", "test8": "Không sử dụng nút quay lại trình duyệt sau khi nhấn Xác nhận. Bạn sẽ được chuyển hướng đến trang thanh toán mybitwallet để hoàn tất giao dịch của mình. Sau khi hoàn thành, bạn sẽ được đưa trở lại văn phòng khách hàng MyFx.", "bitWalletTooltip": "<PERSON>hi không có giao dịch nào được thực hiện sau khi thẻ tín dụng được gửi vào Bitwallet, phí rút tiền là 9%. Sau khi gửi tiền vào Bitwallet bằng thẻ tín dụng, không có phí rút tiền khi có nhiều hơn 5 giao dịch trong tài khoản chủ đề."}, "bank_wire": {"depositMethods": "<PERSON><PERSON><PERSON><PERSON> tiền ngân hàng quốc tế", "status": "<PERSON><PERSON> sẵn", "timeToFund": "1~3 ng<PERSON><PERSON> làm vi<PERSON>c", "fee": "Chỉ phí ngân hàng", "title1": "<PERSON><PERSON> lòng chọn chi tiết tài khoản của bạn dựa trên loại tiền tệ cơ bản của bạn.", "title_01": "Đối với khách hàng sống ở nước ngoài tại Nhật Bản và không thể làm thủ tục chuyển tiền từ ngân hàng nội địa <PERSON>.", "title_02": "<PERSON><PERSON> tài khoản nhận tiền khác nhau cho các loại tiền tệ gửi tiền cũng khác nhau, xin lưu ý.", "title_03": "Trong trường hợp sử dụng tổ chức tài chính không thể nhập số đăng nhập tà<PERSON>, vui lòng gửi bằng chứng chuyển tiền.", "title2": "<PERSON><PERSON><PERSON><PERSON>", "test1": "Tiền tệ chuyển tiền khác với tiền tệ của tài khoản MT4/MT5 và việc trao đổi được phản ánh trong tài khoản MT4/MT5 sau khi chúng tôi thu tiền.", "test2": "<PERSON><PERSON> lòng điền số tài khoản MT4/MT5 vào thanh ghi chú.", "test3": "<PERSON><PERSON> chuyển tiền tại ngân hàng của khách hàng do khách hàng chịu", "test4": "<PERSON>hi đi qua ngân hàng quá cảnh, có thể có phí xử lý ngân hàng quá cảnh. Phí xử lý khi xảy ra do kh<PERSON>ch hàng chịu, xin lưu ý", "label1": "JPY", "label2": "USD", "label3": "AUD", "label4": "GBP", "label5": "EUR"}, "crpto": {"bigTitle": "Tiền gửi của bạn đã đư<PERSON><PERSON> xử lý", "hint": "<PERSON><PERSON><PERSON> bạn không nhận được tiền trên tài khoản giao dịch của mình sau 24 giờ, vui lòng liên hệ với bộ phận hỗ trợ để cung cấp số tài khoản giao dịch và TXID.", "depositMethods": "<PERSON><PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON> sẵn", "timeToFund": "Trong vòng 24 giờ trong ngày làm việc", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON> thanh toán", "label4": "<PERSON><PERSON>ng ty tiền <PERSON>o Name", "label5": "<PERSON><PERSON><PERSON> ty tiền <PERSON>o", "label6": "Trang chủ", "label7": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "label8": "<PERSON><PERSON> tiền n<PERSON>p", "label9": "<PERSON><PERSON><PERSON> lo<PERSON> tiền <PERSON>o", "label10": "<PERSON><PERSON><PERSON><PERSON> gì bạn sử dụng để thanh toán", "label11": "Địa chỉ Cryptocurrency", "label12": "Số lượng", "label13": "<PERSON><PERSON><PERSON><PERSON> điện tử", "label14": "Địa chỉ", "label15": "<PERSON><PERSON><PERSON><PERSON> gian", "label16": "Địa chỉ ví", "label17": "Số lượng", "label18": "<PERSON>ông ty{type}Ví tiền", "label19": "Số gia<PERSON>(TXID)", "title": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> ch<PERSON>a hoàn tất！", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:", "test1": "1. <PERSON><PERSON><PERSON> tiền từ ví của bạn đến ví của chúng tôi như sau.", "test2": "2. <PERSON><PERSON><PERSON><PERSON> TXID và nó hiển thị sau khi bạn gửi tiền và gửi nó.", "test3": "Nếu TXID không đ<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> tài trợ sẽ bị trì hoãn.", "test4": "", "test5": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hiển thị sau khi bạn gửi tiền.", "test6": "TXID là gì?", "test7": "Số giao dịch (TXID: Transaction Identification) là số duy nhất gồm 64 ký tự được sử dụng trong giao dịch tiền điện tử.", "test8": "Do cấu trúc giao dịch củ<PERSON>, giao dịch nạp tiền của bạn sẽ mất 24 giờ để hoàn thành, vì vậy hãy dành nhiều thời gian để nạp tiền trước để tránh sự chậm trễ bất ngờ.", "test9": "<PERSON>ạn có thể nhấp vào \"<PERSON><PERSON>ch sử giao dịch\" để xem tình trạng cập nhật giao dịch của bạn. <PERSON><PERSON><PERSON> thắc mắc xin vui lòng liên hệ với chúng tôi."}, "credit_card": {"depositMethods": "Thẻ tín dụng", "status": "<PERSON><PERSON> đ<PERSON> ki<PERSON>n", "creditCardToolTip": "Tiền gửi thẻ tín dụng chỉ dành cho khách hàng có hơn 5 lần tiền gửi và có cách khác để tích lũy 100.000 yên (hoặc tương đương) hoặc nhiều hơn.", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label1_placeholder": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label1_error": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label2": "Số lượng", "label2_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label2_error": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label3": "Chọn thẻ tín dụng đã đăng ký", "label3_placeholder": "<PERSON><PERSON> lòng chọn thẻ tín dụng đã đăng ký", "label3_error": "<PERSON><PERSON> lòng chọn thẻ tín dụng đã đăng ký", "label4": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "label5": "Thẻ tín dụng đã đăng ký", "label6": "Số thẻ tín dụng", "label7": "Tên tài k<PERSON>n thẻ tín dụng", "label8": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "label9": "CVV", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:", "test1": "Số tiền nạp:", "test2": "Dưới đây là các chi tiết ngân hàng chỉ bằng đồng <PERSON>ê<PERSON>.", "test3": "<PERSON><PERSON> lòng gửi số tiền ch<PERSON>h xác trư<PERSON>c ngày đáo hạn.", "test4": "Mỗi tài khoản có thể khác <PERSON>hau, vì vậy hãy xác nhận mỗi tài khoản.", "test5": "Số tiền được hiển thị dựa trên tỷ giá hối đoái hiện tại. Xin lưu ý rằng số tiền này có thể được tính toán lại trong quá trình và có thể khác với số tiền gửi thực tế.", "title": "Tiền gửi của bạn đã đư<PERSON><PERSON> xử lý", "test6": "<PERSON><PERSON><PERSON><PERSON> thái", "test7": "<PERSON><PERSON><PERSON><PERSON> công", "test8": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "test9": "Mã lỗi", "test10": "Trans No", "test11": "", "test12": "", "test13": "", "test14": "", "test15": "", "test16": "", "test17": "<PERSON><PERSON> lòng nhấn nút OK chỉ một lần", "remakes": "(<PERSON><PERSON> thể bắt đầu với khoản tiền gửi thứ sáu của bạn.)"}, "awe_pay": {"depositMethods": "<PERSON><PERSON><PERSON><PERSON>", "myr": "Awepay-MYR", "thb": "Awepay-THB", "vnd": "Awepay-VND", "status": "<PERSON><PERSON> sẵn", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON>u đ<PERSON>y là thông tin xác nhận của bạn về giao dịch này：", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:", "test1": "MT4", "test2": "<PERSON><PERSON><PERSON><PERSON> thái", "test3": "<PERSON><PERSON><PERSON><PERSON> gian", "from_label1": "<PERSON><PERSON><PERSON>", "from_label2": "<PERSON><PERSON><PERSON>", "from_label3": "Quốc gia", "from_label4": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "from_label5": "<PERSON><PERSON><PERSON><PERSON> công url", "from_label6": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>i url"}, "rmb_funding": {"depositMethods": "<PERSON><PERSON><PERSON> tiền CNY", "status": "<PERSON><PERSON> sẵn", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON>", "label4": "<PERSON><PERSON> điện tho<PERSON>i +86", "label5": "Số ID", "label6": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "label7": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "label8": "<PERSON><PERSON><PERSON>", "label9": "<PERSON><PERSON><PERSON><PERSON> gian", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:"}, "rmb_funding2": {"depositMethods1": "MyPay-CNY", "depositMethods2": "MyPay-THB", "depositMethods3": "MyPay-VND", "depositMethods4": "MyPay-INR", "depositMethods5": "MyPay-IDR", "depositMethods6": "MyPay-KRW", "status": "<PERSON><PERSON> sẵn", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON>", "label4": "Số lượng <PERSON>ng dụng", "label5": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "label6": "<PERSON><PERSON> tiền n<PERSON>p", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:", "tips": "1. <PERSON><PERSON><PERSON> khoản ngân hàng của bạn phải thuộc sở hữu của bạn.<br/> 2. <PERSON><PERSON> <PERSON>hi thanh toán hoàn tất, tiền sẽ tự động được nạp vào MT4 của bạn.<br/> 3. Tr<PERSON><PERSON><PERSON> khi thanh toán hoàn tất, bạn không thể gửi yêu cầu thanh toán mới. Nếu cần gửi đơn đặt hàng mới, vui lòng đợi ít nhất 12 phút.<br/> Nếu bạn gặp bất kỳ vấn đề nào khi sử dụng, xin vui lòng liên hệ với bộ phận hỗ trợ khách hàng."}, "xCoins": {"depositMethods1": "Credit Card", "depositMethods2": "Apple Pay / Google Pay", "depositMethods3": "Neteller / Skrill", "status": "<PERSON><PERSON> sẵn", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON>", "label1_placeholder": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label2_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label3_placeholder": "<PERSON><PERSON> lòng chọn ngà<PERSON> sinh", "co交易账号nfirmInfo": "<PERSON>u đ<PERSON>y là thông tin xác nhận của bạn về giao dịch này：", "check": {"format": "<PERSON><PERSON> lòng nhập đúng định dạng", "limit": "<PERSON><PERSON> tiền trong {number} giữa"}, "confirmTable": {"label1": "Card/Mobile/Instant Payment Số tiền nạp", "label2": "<PERSON><PERSON> tiền nạp vào tài khoản giao dịch", "label3": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "label4": "<PERSON><PERSON><PERSON><PERSON> gian"}, "tips": {"tip1": "Hướng dẫn đặc biệt dành cho người dùng Apple Pay và Google Pay:", "tip2": "• Apple Pay: <PERSON><PERSON><PERSON> nhập vào Văn phòng khách hàng từ thiết bị Apple của bạn và gửi yêu cầu gửi tiền.<br>• Google Pay: Đăng nhập vào Văn phòng khách hàng từ thiết bị Google của bạn và gửi yêu cầu gửi tiền."}}, "MybanQ": {"depositMethods": "Chuy<PERSON><PERSON> k<PERSON>n ngân hàng <PERSON>", "status": "<PERSON><PERSON> sẵn", "timeToFund": "30 phút", "fee": "Chỉ phí ngân hàng"}, "grandPay": {"depositMethods": "Thẻ tín dụng", "status": "<PERSON><PERSON> đ<PERSON> ki<PERSON>n", "creditCardToolTip": "Tiền gửi thẻ tín dụng chỉ dành cho khách hàng có hơn 5 lần tiền gửi và có cách khác để tích lũy 300.000 yên (hoặc tương đương) hoặc nhiều hơn.", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label1_placeholder": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label1_error": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label2": "Số lượng", "label2_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label2_error": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label3": "Chọn thẻ tín dụng đã đăng ký", "label3_placeholder": "<PERSON><PERSON> lòng chọn thẻ tín dụng đã đăng ký", "label3_error": "<PERSON><PERSON> lòng chọn thẻ tín dụng đã đăng ký", "label4": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "label5": "Thẻ tín dụng đã đăng ký", "label6": "Số thẻ tín dụng", "label7": "Tên tài k<PERSON>n thẻ tín dụng", "label8": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "label9": "CVV", "test": "<PERSON><PERSON><PERSON><PERSON> tệ:", "test1": "Số tiền nạp:", "test2": "Dưới đây là các chi tiết ngân hàng chỉ bằng đồng <PERSON>ê<PERSON>.", "test3": "<PERSON><PERSON> lòng gửi số tiền ch<PERSON>h xác trư<PERSON>c ngày đáo hạn.", "test4": "Mỗi tài khoản có thể khác <PERSON>hau, vì vậy hãy xác nhận mỗi tài khoản.", "test5": "Số tiền được hiển thị dựa trên tỷ giá hối đoái hiện tại. Xin lưu ý rằng số tiền này có thể được tính toán lại trong quá trình và có thể khác với số tiền gửi thực tế.", "title": "Tiền gửi của bạn đã đư<PERSON><PERSON> xử lý", "test6": "<PERSON><PERSON><PERSON><PERSON> thái", "test7": "<PERSON><PERSON><PERSON><PERSON> công", "test8": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "test9": "Mã lỗi", "test10": "Trans No", "test11": "", "test12": "", "test13": "", "test14": "", "test15": "", "test16": "", "test17": "<PERSON><PERSON> lòng nhấn nút OK chỉ một lần", "remakes": "(<PERSON><PERSON> thể bắt đầu với khoản tiền gửi thứ sáu của bạn.)"}, "monetix": {"depositMethods": "THB QR", "Philippines": "PHP QR Payment", "MYR": "MYR Payment", "status": "<PERSON><PERSON> sẵn", "timeToFund": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee": "<PERSON><PERSON><PERSON> phí thủ tục", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label1_placeholder": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label2_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "co交易账号nfirmInfo": "<PERSON>u đ<PERSON>y là thông tin xác nhận của bạn về giao dịch này：", "check": {"format": "<PERSON><PERSON> lòng nhập đúng định dạng", "limit": "<PERSON><PERSON> tiền trong {number} giữa"}, "confirmTable": {"label1": "<PERSON><PERSON> tiền n<PERSON>p", "label2": "<PERSON><PERSON> tiền nạp vào tài khoản giao dịch", "label3": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch", "label4": "<PERSON><PERSON><PERSON><PERSON> gian"}}, "mbwSuccess": {"title": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "error": "Lỗi", "payStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "order": "<PERSON><PERSON> thứ tự", "mt4Login": "Mt4 Login", "fundingAmount": "Số tiền tài trợ", "amount": "Số lượng", "time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "desc": "Mybitwallet sẽ gửi kết quả đến hệ thống và một khi hệ thống nhận được kết quả, số tiền sẽ được gửi vào tài khoản của bạn, hãy kiên nhẫn.", "back": "Quay lại trang chủ"}, "xcoinsSuccess": {"title": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "payStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "fundingAmount": "<PERSON><PERSON> tiền n<PERSON>p", "account": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "order": "<PERSON><PERSON> thứ tự", "time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "desc": "Thẻ/Điện thoại di động/<PERSON><PERSON> toán tức thì sẽ gửi kết quả đến hệ thống và một khi hệ thống nhận được kết quả, số tiền sẽ được gửi vào tài khoản của bạn, vì vậy hãy kiên nhẫn.", "back": "Quay lại trang chủ"}, "grandPaySuccess": {"title": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "payStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "fundingAmount": "<PERSON><PERSON> tiền n<PERSON>p", "account": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "order": "<PERSON><PERSON> thứ tự", "time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "desc": "GrandPay sẽ gửi kết quả đến hệ thống và một khi hệ thống nhận được kết quả, số tiền sẽ được gửi vào tài khoản của bạn, vì vậy hãy kiên nhẫn.", "back": "Quay lại trang chủ"}, "myPaySuccess": {"title": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "payStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "fundingAmount": "<PERSON><PERSON> tiền n<PERSON>p", "account": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "order": "<PERSON><PERSON> thứ tự", "time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "desc": "MyPay sẽ gửi kết quả đến hệ thống và một khi hệ thống nhận được kết quả, số tiền sẽ được gửi vào tài khoản của bạn, vì vậy hãy kiên nhẫn.", "back": "Quay lại trang chủ"}, "monetixSuccess": {"title": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "payStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "fundingAmount": "<PERSON><PERSON> tiền n<PERSON>p", "account": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "order": "<PERSON><PERSON> thứ tự", "time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "desc": "Monetix sẽ gửi kết quả đến hệ thống và một khi hệ thống nhận được kết quả, số tiền sẽ được gửi vào tài khoản của bạn, hãy kiên nhẫn.", "back": "Quay lại trang chủ"}, "depositCommonResult": {"title": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "payStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "fundingAmount": "<PERSON><PERSON> tiền n<PERSON>p", "account": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "order": "<PERSON><PERSON> thứ tự", "time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "desc": "Awepay sẽ gửi kết quả đến hệ thống và một khi hệ thống nhận được kết quả, số tiền sẽ được gửi vào tài khoản của bạn, hãy kiên nhẫn.", "back": "Quay lại trang chủ"}}, "withdrawal": {"newYearTips": "<p style=\"margin: 4px;\">&lt;Trong kỳ nghỉ cuối năm và năm mới&gt;</p><p style=\"margin: 4px;\">\n・Hoạt động rút tiền sẽ bị tạm dừng vào ngày 25 tháng 12 (th<PERSON> tư), ngày 26 tháng 12 (th<PERSON> năm) và ngày 1 tháng 1 (th<PERSON> tư).</p><p style=\"margin: 4px;\">・Chuyển khoản ngân hàng trong nước bằng JPY (Yên Nhật) sẽ bị tạm dừng từ ngày 31 tháng 12 (th<PERSON>) đến ngày 5 tháng 1 (<PERSON><PERSON>).</p><p style=\"margin: 4px;\">・Các yêu cầu rút tiền trong thời gian này sẽ được xử lý bắt đầu từ ngày làm việc tiếp theo; tuy nhi<PERSON>, xin lưu ý rằng thời gian xử lý có thể lâu hơn bình thường.</p>", "hint_01": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi làm thủ tục rút tiền, n<PERSON><PERSON> có giao dịch trong giao dịch, h<PERSON><PERSON> xác nhận rằng bạn có đủ số tiền ký quỹ còn lại hay không.", "hint_02": "<PERSON><PERSON><PERSON> không để lại đủ số tiền ký quỹ, có thể sẽ bắt buộc thỏa thuận giảm mức chênh lệch sau khi hoàn thành việc rút tiền, xin lưu ý.", "hint_03": "Tr<PERSON><PERSON><PERSON> khi làm thủ tục xuất tiền, tr<PERSON><PERSON><PERSON> khi kim ngạch xuất tiền đến tài khoản khách hàng, sau khi công ty bắt đầu xử lý xuất tiền phải mất khoảng 10 ngày.", "hint_04": "Trong trường hợp có ngày của công ty thẻ tín dụng trong thời gian này, một khi yêu cầu tăng khấu trừ được tiến hành, t<PERSON> <PERSON>, bởi vì từ lần sau công ty thẻ tín dụng đượ<PERSON> trả lạ<PERSON>, số tiền trả lại một phần cho đến khi hạn mức tín dụng sống lại mới có thời gian khoảng 40 ngày.", "title": "<PERSON><PERSON><PERSON> sử rút tiền", "Vault": "Ví MYFX", "table": {"processed_date_time": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "transaction_type": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "amount": "Số lượng", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "account_number": "Số tài <PERSON>n", "withdrawal_type": "<PERSON><PERSON><PERSON> rút ti<PERSON>n", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusType": {"PROCESSED": "Đã xử lý", "DELETED": "Đã xoá", "FAILED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "MAMPAMM": "MAMPAMM", "CANCELLED": "<PERSON><PERSON> hủy", "PROCESSING": "<PERSON><PERSON> chờ thanh toán", "FALLED": "Hủy bỏ"}}, "submit-table": {"mt4Login": "Số tài <PERSON>n", "server": "<PERSON><PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON> tiền tệ", "amount": "Số lượng", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createTime": "<PERSON><PERSON><PERSON><PERSON> gian", "type-default": "<PERSON><PERSON><PERSON>", "type-watell": "<PERSON><PERSON><PERSON> tiền từ ví", "walletLogin": "<PERSON><PERSON>"}, "title2": "<PERSON><PERSON><PERSON> tiền từ tài khoản giao dịch", "title3": "MAM/PAMM R<PERSON><PERSON> tiền tài k<PERSON>n", "wd-title2": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "wd-title3": "MAM/PAMM", "tipsTableColumns": {"currency": "Số lượng", "amount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "tipsTableColumnsCrypto": {"currency": "Trang chủ", "amount": "USDT", "tips": "<PERSON><PERSON> rút tiền có thể khác nhau tùy thuộc vào từng tỷ giá hối đoái và phí xăng đư<PERSON><PERSON> t<PERSON>."}, "tips": {"title": "Hướng dẫn", "test1": "<PERSON> c<PERSON> quy định AML/CTF, MYFX Markets không thể gửi tiền cho bên thứ ba. Tất cả các khoản rút tiền từ tài khoản của bạn phải được chuyển đến tài khoản có cùng tên với tài khoản giao dịch MYFX Markets của bạn.", "test2": "<PERSON>u<PERSON> trình rút tiền thường mất 1-3 ngày làm việc. Đ<PERSON><PERSON> với các yêu cầu rút tiền đư<PERSON>c thực hiện vào cuố<PERSON> tu<PERSON>, việc xử lý có thể bị trì hoãn cho đến ngày làm việc tiếp theo.", "test3": "<PERSON>n lưu ý rằng các khoản phí có thể được tính riêng bởi các công ty vận chuyển rút tiền.", "test4": "<PERSON>ố tiền rút tối đa thông qua bitwallet là 20.000 đô la. <PERSON><PERSON><PERSON> mức ký quỹ hiện tại dưới 250%, yêu cầu rút tiền sẽ không được xử lý.", "test5": "<PERSON><PERSON> tuân thủ các quy định chống rửa tiền (AML), chúng tôi có nghĩa vụ chỉ gửi tiền đến các tài khoản có cùng tên với tài khoản giao dịch của bạn. Cảm ơn bạn rất nhiều vì sự hợp tác của bạn trong vấn đề này.", "test6": "<PERSON>n lưu ý rằng phí ngân hàng trung gian không được xác định bởi thị trường MYFX.", "test_06_01": "<PERSON><PERSON><PERSON> k<PERSON>n phí khác do ngân hàng trung gian phát sinh không phải là phí do MYFX Markets quy định.", "test_06_02": "<PERSON><PERSON><PERSON><PERSON> có phí xử lý để rút tiền vào tài khoản ngân hàng ở Úc.", "test7": "Hướng dẫn nạp tiền và rút tiền", "test8": "Sổ tay hướng dẫn về đóng góp và rút tiền đã được chuẩn bị.", "test9": "Download tại đây", "test10": "<PERSON><PERSON><PERSON> xuống <PERSON>ổ tay thu tiền", "test11": "Những ai có tài k<PERSON>n MAM/PAMM vui lòng download tại đây.", "test12": "<PERSON><PERSON><PERSON><PERSON> dẫn thu tiền (MAM/PAMM)", "test13": "Bởi vì dịch vụ bê<PERSON>, căn cứ vào thời cơ gửi tiền giữa các ngân hàng có phản ánh tình huống tr<PERSON> ho<PERSON>, cho nên đề nghị để lại đường sống gửi tiền.", "test13_01": "Tỷ lệ duy trì ký quỹ trên tài khoản gốc <span style=\"color: red; font-weight: bold;\">250%</span> 以下的情况不能申请。请确认头寸和有效保证金的余额后再申请。", "test13_02": "<PERSON> chống <PERSON><PERSON><PERSON> (AML), chỉ gửi tiền đến các tài khoản có cùng tên với tài khoản giao dịch.", "test13_03": "<PERSON><PERSON> đó là tiền gửi từ JPY, vui lòng sử dụng tài khoản JPY. <PERSON><PERSON><PERSON><PERSON> ra, trong trườ<PERSON> hợp tiền tệ của tài khoản giao dịch không phải là JPY, vui lòng hiểu rằng chuyển tiền sau khi công ty gian lận đổi sang JPY.", "test13_04": "<PERSON>hi gửi tiền trong nước phải có số tổ chức tài chính và số chi nhánh của tổ chức tài chính mục tiêu. Số tổ chức tài chính và số chi nhánh vui lòng đăng nhập vào trang web của mỗi tổ chức tài chính để xác nhận.", "test13_05": "<PERSON> phí thủ tục trích xuất đư<PERSON><PERSON> khấu trừ từ kim ngạch trích xuất để làm thủ tục.", "test14": "<PERSON><PERSON><PERSON> khoản chuyển tiền chi tiết được phát hành tại mỗi lần yêu cầu chuyển tiền và không thể được sử dụng trong lần chuyển tiền tiếp theo. <PERSON><PERSON> lòng thực hiện yêu cầu đóng góp mỗi lần.", "test15": "Trong trườ<PERSON> hợp số tiền chuyển tiền trên 500.000 yên, khi làm thủ tục chuyển tiền, vui lòng nhập số yêu cầu ghi trong email nhận đơn xin chuyển tiền trước tên người yêu cầu chuyển tiền.", "test16": "<PERSON><PERSON> chuyển tiền thu tại ngân hàng của khách hàng sẽ do khách hàng chi trả.", "test17": "Trong trườ<PERSON> hợp tiền tệ của tài khoản giao dịch không phải là JPY, số tiền chuyển đổi sang JPY sẽ được hiển thị trên trang tiếp theo.", "test18": "Tỷ lệ duy trì ký quỹ cho tài khoản rút tiền không thể được áp dụng nếu nó dưới 250%. Vui lòng xác nhận số dư của vị trí và ký quỹ hợp lệ trước khi áp dụng.", "test19": "<PERSON> chống <PERSON><PERSON><PERSON> (AML), chỉ gửi tiền đến các tài khoản có cùng tên với tài khoản giao dịch.", "test20": "Trong trườ<PERSON> hợp tiền tệ tài khoản giao dịch và tiền tệ tài khoản ngân hàng khác nhau, xin vui lòng ghi lại tiền tệ tài khoản ngân hàng trong thanh bình luận.", "test21": "<PERSON><PERSON><PERSON> ngân hàng muốn gửi hơn 1 tri<PERSON><PERSON> yên, xin chia làm vài lần. <PERSON><PERSON> thủ tục là thu mỗi lần xin.", "test22": "<PERSON> phí thủ tục trích xuất đư<PERSON><PERSON> khấu trừ từ kim ngạch trích xuất để làm thủ tục.", "test23": "<PERSON><PERSON><PERSON> k<PERSON>n phí khác phát sinh tại các ngân hàng trung gian không phải là phí do MYFX MARKETS đặt ra.", "test24": "<PERSON><PERSON><PERSON><PERSON> có phí xử lý để rút tiền vào tài khoản ngân hàng nội địa của <PERSON>.", "test25": "<PERSON> phí thủ tục trích xuất đư<PERSON><PERSON> khấu trừ từ kim ngạch trích xuất để làm thủ tục.", "test26": "<PERSON><PERSON> rút tiền bằng tiền ảo, sẽ có phí xử lý và phí xử lý blockchain để đổi tiền tệ từ tài khoản MT4/MT5 sang tiền ảo.", "test27": "<PERSON><PERSON> rút tiền khác nhau tùy thuộc vào phí trao đổi cho mỗi loại tiền ảo và phí cho blockchain.", "test28": "<PERSON>í rút tiền cho thẻ tín dụng là 2.000 yên, vì giao dịch nhận đượ<PERSON> x<PERSON>y ra vui lòng thông cảm. Ví dụ: gửi 20.000 yên, 30.000 yên, 40.000 yên (tổng cộng 90.000 yên), khi rút 50.000 yên, bạn sẽ cần rút 20.000 yên và 30.000 yên tương ứng vì phí xử lý tương <PERSON>ng, tổng cộng là 4.000 yên.", "test29": "<PERSON><PERSON> tiền rút ra từ thẻ tín dụng cần phải giống như số tiền nạp vào. <PERSON>, ph<PERSON><PERSON> lợ<PERSON>, cũng như trong trường hợp không đủ số tiền gửi, các ngân hàng trong nước chuyển tiền ra.", "test30": "<PERSON>uy trình cho đến khi xuất tiền hoàn thành mới thôi. Số tiền rút vào tài khoản khách hàng mới thôi, sau khi công ty bắt đầu xử lý tiền rút phải mất khoảng 10 ngày. Trong trường hợp có ngày của công ty thẻ tín dụng trong thời gian này, một khi yêu cầu tăng khấu trừ được tiến hành, tuy <PERSON><PERSON><PERSON><PERSON>, bởi vì từ lần sau công ty thẻ tín dụng được trả lại, số tiền trả lại một phần cho đến khi hạn mức tín dụng sống lại mới có thời gian khoảng 40 ngày.", "test31": "Trong trườ<PERSON> hợp muốn trích xuất số tiền trích xuất cao nhất trên, xin chia làm nhi<PERSON> lần.", "test32": "<PERSON>í rút tiền 9%~(t<PERSON><PERSON> thu<PERSON><PERSON> vào thương hiệu của thẻ) được áp dụng trong trường hợp thanh toán bitwallet trên 5 lần bằng thẻ tín dụng mà không có giao dịch. Tài khoản đối tượng có từ 5 đợt giao dịch trở lên không thu phí thủ tục.", "test33": "Tỷ lệ duy trì ký quỹ cho tài khoản rút tiền không thể được áp dụng nếu nó dưới 250%. Vui lòng xác nhận số dư của vị trí và ký quỹ hợp lệ trước khi áp dụng.", "test34": "<PERSON>í rút tiền 9%~(t<PERSON><PERSON> thu<PERSON><PERSON> vào thương hiệu của thẻ) được áp dụng trong trường hợp thanh toán bitwallet trên 5 lần bằng thẻ tín dụng mà không có giao dịch. Tài khoản đối tượng có từ 5 đợt giao dịch trở lên không thu phí thủ tục.", "title2": "<PERSON><PERSON> tiền rút tối thiểu：", "title3": "Số tiền rút tối đa：", "title4": "<PERSON><PERSON><PERSON><PERSON> phí ngân hàng：", "title5": "<PERSON><PERSON>ng chi phí rút tiền：", "title6": "<PERSON><PERSON>ng chi phí rút tiền："}, "step1": "Bước 1", "step2": "Bước 2", "step3": "Bước 3", "description1": "Bước 1: <PERSON><PERSON> lòng chọn cách bạn muốn rút tiền.", "description2": "Bước 2: <PERSON><PERSON> lòng nhập câu trả lời bảo mật của bạn", "description3": "Bước 3: <PERSON><PERSON> lòng nhập thông tin rút tiền của bạn", "description1-1": "Bước 1: <PERSON><PERSON> lòng nhập câu trả lời bảo mật của bạn", "description2-2": "Bước 2: <PERSON><PERSON> lòng nhập thông tin rút tiền của bạn", "withdrawal_way_columns": {"payment_methods": "<PERSON><PERSON><PERSON> ti<PERSON>n", "time_to_withdraw": "<PERSON><PERSON><PERSON><PERSON> gian thanh toán", "fee": "Chi phí", "operation": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "withdrawalWay": {"credit_card": "Thẻ tín dụng", "international_bank_wire": "<PERSON><PERSON><PERSON><PERSON> tiền ngân hàng quốc tế", "bitwallet": "Bitwallet", "crypto_currency": "<PERSON><PERSON><PERSON><PERSON>", "jp_domestic_bank_wire": "Chuy<PERSON><PERSON> k<PERSON>n ngân hàng <PERSON>", "cny_Withdrawal": "<PERSON><PERSON><PERSON> t<PERSON> CN<PERSON>", "cny_direct_transfer": "<PERSON><PERSON><PERSON><PERSON> tiền CNY", "awe_pay": "AWE Pay", "monetix": "Rút THB", "10days": "10 ngày", "1Day": "1 ngày", "1_2Days": "1-2 ng<PERSON>y", "1_3Days": "1-3 ng<PERSON>y", "instant": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "fee1": "$25", "fee2": "<PERSON><PERSON><PERSON><PERSON> có phí xử lý", "min": "<PERSON><PERSON> tiền rút tối thiểu"}, "mam_pam_form": {"label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "<PERSON><PERSON><PERSON>", "label3": "Số lượng", "label4": "<PERSON><PERSON><PERSON>", "label2_placeholder": "<PERSON><PERSON> lòng chọn lo<PERSON>i"}, "credit_card": {"label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON>"}, "bank_wire": {"add_account": "<PERSON><PERSON><PERSON> ký", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng", "label4": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label5": "<PERSON><PERSON><PERSON> tà<PERSON>", "label6": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label7": "S<PERSON> thẻ ngân hàng", "label8": "Mã SWIFT", "label9": "<PERSON><PERSON><PERSON>", "dialog_title": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "addAccount": "<PERSON><PERSON><PERSON> ký tài khoản ngân hàng", "hint_01": "Đối với khách hàng sống ở nước ngoài tại Nhật Bản và không thể làm thủ tục chuyển tiền từ ngân hàng nội địa <PERSON>.", "hint_02": "Người sử dụng ngân hàng nội địa <PERSON>", "hint_03": "Ở đây", "hint_04": "<PERSON><PERSON> lòng bắt đầu chuyển tiền từ ngân hàng nội địa.", "hint_05": "1 lần rút tối đa: tư<PERSON><PERSON> đương 1 triệ<PERSON> yên (1 ngày không giới hạn)", "hint_06": "<PERSON><PERSON><PERSON> bạn muốn rút số tiền tương đương hơn 1 tri<PERSON><PERSON> yên, vui lòng chia thành nhiều ứng dụng.", "dialogClose": "Việc đăng ký tài khoản ngân hàng đã hoàn tất."}, "bit_wallet": {"label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "MyBitWallet Đă<PERSON> nhập v<PERSON><PERSON> hộp thư", "label4": "<PERSON><PERSON><PERSON>", "email_placeholder": "<PERSON><PERSON> lòng nh<PERSON><PERSON> hộp thư", "title1": "<PERSON><PERSON> tiền chi tiêu tối đa trong 1 ngày: tư<PERSON><PERSON> đư<PERSON> 2,5 triệu yên.", "title2": "<PERSON><PERSON><PERSON> bạn muốn trả một khoản phí tương đương hơn 2,5 tri<PERSON><PERSON> yên, h<PERSON><PERSON> chia thành nhiều <PERSON>ng dụng."}, "crpto_currency": {"tip": "<PERSON>ố tiền chi tiêu tối đa trong 1 ngày: <PERSON><PERSON><PERSON> bạn muốn trả 20.000 đô la tương đương với 20.000 đô la tương đương với 20.000 đô la, vui lòng đăng ký nhiều lần.", "tip_01": "Chi phí tối đa mỗi ngày: $20,000", "tip_02": "<PERSON><PERSON><PERSON> bạn muốn trả một khoản tiền tương đương 20.000 USDT, vui lòng nộp đơn nhiều lần.", "label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON> lo<PERSON>i tiền <PERSON>o", "label4": "Chọn Blockchain", "label5": "<PERSON><PERSON>ng ty tiền <PERSON>o Name", "label6": "Đ<PERSON>a chỉ công ty tiền ảo", "label7": "<PERSON><PERSON><PERSON>", "checked_test1": "<PERSON> bản chất vốn có của tiền ảo, khô<PERSON> thể theo dõi hoặc đảo ngược giao dịch sau đó.", "checked_test2": "<PERSON><PERSON><PERSON> chắc chắn rằng tất cả các thông tin được cung cấp là chính xác trước khi xử lý rút tiền của bạn.", "company_name_placeholder": "<PERSON><PERSON> lòng nhập tên", "address_placeholder": "<PERSON><PERSON> lòng nhập địa chỉ"}, "awe_pay": {"label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON> đồng tiền vàng", "label4": "<PERSON><PERSON><PERSON> tài kho<PERSON>n ngân hàng", "label5": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label6": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label7": "Địa chỉ chi nh<PERSON>h", "label8": "Mã chi nh<PERSON>h", "label9": "<PERSON><PERSON><PERSON> tà<PERSON>", "label10": "S<PERSON> thẻ ngân hàng", "label11": "<PERSON><PERSON><PERSON>", "label12": "Mã chi nh<PERSON>h", "label13": "Mã SWIFT", "dialog_title": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "placeholder": {"currency": "<PERSON><PERSON> lòng nhập tiền vàng", "bankAccount": "<PERSON><PERSON> lòng chọn tài khoản ngân hàng", "bankName": "<PERSON><PERSON> lòng nhập tên ngân hàng", "branchName": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "branchCity": "<PERSON><PERSON> lòng nhập địa chỉ chi nhánh", "bankCode": "<PERSON><PERSON> lòng nhập mã số", "nameOnCard": "<PERSON><PERSON> lòng nhập tên tài <PERSON>n", "cardNumber": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "comment": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú"}}, "cny_withdrawal": {"label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "Chuyển đổi CNY", "label4": "<PERSON><PERSON><PERSON> tài kho<PERSON>n ngân hàng", "label5": "S<PERSON> thẻ ngân hàng", "label6": "<PERSON><PERSON><PERSON> tà<PERSON>", "label7": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label8": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label9": "Mã SWIFT", "label10": "Đ<PERSON>a chỉ ngân hàng", "label11": "<PERSON><PERSON><PERSON>", "label12": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "dialog_title": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "dialog_success": "<PERSON><PERSON><PERSON><PERSON> tài kho<PERSON>n ngân hàng thành công", "test": "<PERSON><PERSON>n CNY rút tiền."}, "jp_dbw": {"label1": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2": "Số lượng", "label3": "<PERSON><PERSON><PERSON> tài kho<PERSON>n ngân hàng", "label4": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label5": "<PERSON><PERSON> ngân hàng", "label6": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label7": "Mã chi nh<PERSON>h", "label8": "<PERSON><PERSON><PERSON> tà<PERSON>", "label_08_01": "<PERSON><PERSON><PERSON>(", "label_08_02": "<PERSON><PERSON><PERSON>", "label_08_03": "<PERSON><PERSON> lòng nh<PERSON>p)", "label9": "S<PERSON> thẻ ngân hàng", "label10": "<PERSON><PERSON><PERSON> tà<PERSON>", "label11": "<PERSON><PERSON><PERSON>", "dialog_title": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "dialog_success": "<PERSON><PERSON><PERSON><PERSON> tài kho<PERSON>n ngân hàng thành công", "placeholder": "<PERSON><PERSON> lòng ch<PERSON>n", "confirmTitle": "<PERSON><PERSON><PERSON> k<PERSON>n có bị xóa không?", "tip": "Mẹo", "sure": "<PERSON><PERSON><PERSON>", "cancel": "Hủy bỏ", "message": "<PERSON><PERSON><PERSON> thành công<PERSON>"}, "rest_security": "<PERSON><PERSON><PERSON> bạn quên câu trả lời, hã<PERSON> nhấp vào đây để đặt lại câu hỏi bảo mật", "rest_security2": "", "balance_message": "<PERSON><PERSON> dư không đáp ứng yêu cầu tiền gửi tối thiểu", "agreement": "<PERSON><PERSON> lòng xem Thỏa thuận", "securityTitle": "<PERSON><PERSON> lòng xem Thỏa thuận", "securityTest1": "<PERSON><PERSON>g tôi sẽ gửi liên kết đặt lại câu hỏi bảo mật đến hộp thư đăng ký của bạn.", "securityTest2": "<PERSON><PERSON> lòng nhấp vào liên kết bên dưới và xem tin nhắn của bạn.", "button_rest_security": "Đặt lại câu hỏi bảo mật của bạn", "successTest1": "<PERSON>âu hỏi bảo mật Đặt lại tin nhắn đã được gửi thành công!", "CreditCard": {"form": {"label1": "<PERSON><PERSON><PERSON>", "label1_placeholder": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label1_error1": "<PERSON><PERSON> lòng chọn tài <PERSON>n", "label1_balance": "Số dư có sẵn", "label2": "Số lượng", "label2_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label2_error1": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "label2_error2": "<PERSON><PERSON> lòng nh<PERSON>p số hợp lệ", "label2_error3": "<PERSON><PERSON><PERSON><PERSON> đủ số dư tài khoản hiện tại", "label2_error4": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> thấp hơn giới hạn rút tiền tối thiểu", "label2_error5": "<PERSON><PERSON><PERSON><PERSON> vư<PERSON>t quá giới hạn rút tiền tối đa", "label3": "<PERSON><PERSON><PERSON>", "label3_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú"}}, "BankWire": {"form": {"label3": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label3_error1": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label3_placeholder": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label4": "<PERSON><PERSON> tà<PERSON>n", "label4_error1": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label4_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label5": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label5_error1": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label5_placeholder": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label6": "S<PERSON> thẻ ngân hàng", "label6_error1": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label6_placeholder": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label7": "Mã SWIFT", "label7_error1": "<PERSON><PERSON> lòng nhập mã SWIFT", "label7_placeholder": "<PERSON><PERSON> lòng nhập mã SWIFT", "label8": "<PERSON><PERSON><PERSON>", "label8_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú"}}, "JPDomesticBankWire": {"accountType": {"label1": "<PERSON><PERSON><PERSON><PERSON> thường", "label2": "<PERSON><PERSON><PERSON> t<PERSON>i", "label3": "<PERSON><PERSON><PERSON><PERSON>", "label4": "K<PERSON><PERSON><PERSON>"}, "tooltip": {"tip1": "【<PERSON><PERSON><PERSON> nhập Half Katakana】", "tip2": "Đối với Windows", "tip3": "<PERSON><PERSON> <PERSON>hi nhậ<PERSON> (trước khi xác nhận), nhấn phím cách để tìm nó trong chuyển đổi dự đoán hoặc nhấn F8.", "tip4": "Đ<PERSON>i <PERSON><PERSON><PERSON>", "tip5": "<PERSON><PERSON> <PERSON>hi nhậ<PERSON> (trước khi xác nhận), nhấn phím cách để tìm nó trong biến đổi dự đoán hoặc \"fn+F8\" hoặc \"Control+;\". <PERSON><PERSON><PERSON> phím tắt không hoạt động, bạn cần thêm \"Half Katakana\" vào chế độ nhập của nguồn nhập trong cài đặt bàn phím."}, "form": {"label3": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label3_error1": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label3_placeholder": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label4": "<PERSON><PERSON> ngân hàng", "label4_error1": "<PERSON><PERSON> lòng nhập mã ngân hàng", "label4_error2": "<PERSON><PERSON> lòng nh<PERSON>p số hợp lệ", "label4_error3": "<PERSON><PERSON> ngân hàng có 4 chữ số", "label4_placeholder": "<PERSON><PERSON> lòng nhập mã ngân hàng", "label5": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label5_error1": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label5_placeholder": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label6": "Mã chi nh<PERSON>h", "label6_error1": "<PERSON><PERSON> lòng nhập mã chi nh<PERSON>h", "label6_error2": "<PERSON><PERSON> lòng nh<PERSON>p số hợp lệ", "label6_error3": "Mã chi nh<PERSON>h là 3 chữ số", "label6_placeholder": "<PERSON><PERSON> lòng nhập mã chi nh<PERSON>h", "label7": "<PERSON><PERSON> tà<PERSON>n", "label7_error1": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label7_error2": "<PERSON><PERSON> lòng nhập ký tự katakana nửa góc", "label7_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label8": "S<PERSON> thẻ ngân hàng", "label8_error1": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label8_error2": "<PERSON><PERSON> lòng nh<PERSON>p số hợp lệ", "label8_error3": "Số thẻ ngân hàng 5-7 chữ số", "label8_placeholder": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label9": "Mã SWIFT", "label9_error1": "<PERSON><PERSON> lòng nhập mã SWIFT", "label9_placeholder": "<PERSON><PERSON> lòng nhập mã SWIFT", "label10": "<PERSON><PERSON><PERSON> tà<PERSON>", "label10_error1": "<PERSON><PERSON> lòng chọn loại tài k<PERSON>n", "label10_placeholder": "<PERSON><PERSON> lòng chọn loại tài k<PERSON>n", "label11": "<PERSON><PERSON><PERSON>", "label11_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú"}}, "CNYWithdrawal": {"form": {"label3": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label3_error1": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label3_placeholder": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label4": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label4_error1": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label4_placeholder": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label5": "S<PERSON> thẻ ngân hàng", "label5_error1": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label5_placeholder": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label6": "<PERSON><PERSON> tà<PERSON>n", "label6_error1": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label6_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label7": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "label7_error1": "<PERSON><PERSON> lòng nhập số điện thoại", "label7_error2": "<PERSON><PERSON> lòng nhập đúng số điện thoại", "label7_placeholder": "<PERSON><PERSON> lòng nhập số điện thoại", "label8": "Mã SWIFT", "label8_error1": "<PERSON><PERSON> lòng nhập mã SWIFT", "label8_placeholder": "<PERSON><PERSON> lòng nhập mã SWIFT", "label9": "<PERSON><PERSON><PERSON>", "label9_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú"}}, "CNYDirectTransfer": {"form": {"label3": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label3_error1": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label3_placeholder": "<PERSON><PERSON> lòng nhập tên ngân hàng", "label4": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label4_error1": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label4_placeholder": "<PERSON><PERSON> lòng nhập tên chi nh<PERSON>h", "label5": "Đ<PERSON>a chỉ ngân hàng", "label5_error1": "<PERSON><PERSON> lòng nhập địa chỉ ngân hàng", "label5_placeholder": "<PERSON><PERSON> lòng nhập địa chỉ ngân hàng", "label6": "S<PERSON> thẻ ngân hàng", "label6_error1": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label6_placeholder": "<PERSON><PERSON> lòng nhập số thẻ ngân hàng", "label7": "<PERSON><PERSON> tà<PERSON>n", "label7_error1": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label7_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p chủ tài k<PERSON>n", "label8": "Mã SWIFT", "label8_error1": "<PERSON><PERSON> lòng nhập mã SWIFT", "label8_placeholder": "<PERSON><PERSON> lòng nhập mã SWIFT", "label9": "<PERSON><PERSON><PERSON>", "label9_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú"}}}, "InternalFundTransfer": {"apply": "Ứng dụng", "select_01": "<PERSON><PERSON> lòng chọn tài k<PERSON>n <PERSON>ly", "select_02": "<PERSON><PERSON> lòng chọn tài k<PERSON>n <PERSON>ly", "sourceAccount": "<PERSON><PERSON><PERSON>", "targetAccount": "<PERSON><PERSON><PERSON>n nhận tiền", "checkAmount": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "checkBalance": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá số dư", "wallet": "MYFX Ví tiền", "error": {"error1": "<PERSON><PERSON><PERSON> khoản nguồn phải hợp lệ!", "error2": "<PERSON><PERSON><PERSON> k<PERSON>n mục tiêu phải hợp lệ!", "error3": "<PERSON><PERSON><PERSON> k<PERSON>n mục tiêu phải hợp lệ!"}}, "transaction": {"deposits": {"paymentType": {"Crypto": "<PERSON><PERSON><PERSON><PERSON> điện tử", "BitWallet": "Ví Bitcoin", "JPYBankWire": "Chuy<PERSON><PERSON> k<PERSON>n ngân hàng <PERSON>", "JPDomesticBankWire": "Chuy<PERSON><PERSON> k<PERSON>n ngân hàng <PERSON>", "InternationalBankWire": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n ngân hàng quốc tế", "CreditCard": "Thẻ tín dụng", "BankWire": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n ngân hàng quốc tế", "UNION_PAY": "UNION_PAY", "Crypto365": "Ti<PERSON><PERSON> điện tử 365", "MyPay": "My Pay", "MAMPAMMWithdraw": "MAM PAMM Rú<PERSON> t<PERSON>n", "MultiLevelRebate": "<PERSON><PERSON><PERSON><PERSON> giá đa cấp", "CNYBankWire": "Chuyển k<PERSON>n ngân hàng CNY", "Wallet": "<PERSON><PERSON> ti<PERSON>n", "Xcoins": "Xcoins", "Monetix": "THB Bank Wire", "THBBankWire": "THB Bank Wire", "internationalbank": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n ngân hàng quốc tế"}}, "title": "<PERSON><PERSON><PERSON> sử giao dịch", "table": {"processed_date_time": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "source_account": "<PERSON><PERSON><PERSON> nguồn", "target_account": "<PERSON><PERSON><PERSON> đ<PERSON>ch", "amount": "Số lượng", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "label1": "<PERSON><PERSON><PERSON> nguồn", "label2": "<PERSON><PERSON><PERSON> ký quỹ:", "label3": "Số dư có sẵn:", "label4": "Số lượng", "label5": "<PERSON><PERSON><PERSON>", "test": "<PERSON><PERSON><PERSON> cầu chuyển tiền nội bộ của bạn đã đư<PERSON><PERSON> gửi thành công", "title1": "<PERSON><PERSON><PERSON><PERSON> tiền thành công", "tips": {"title": "Chú ý：", "test1": "<PERSON><PERSON><PERSON> mức ký quỹ hiện tại thấp hơn <span style=\"color: red;font-weight: bold;\">250%</span> <PERSON><PERSON><PERSON> trường hợp sau đây không thể áp dụng.", "test2": "<PERSON><PERSON> <PERSON><PERSON>, yêu cầu không thể được xử lý.", "test3": "<PERSON><PERSON><PERSON> cần đổi tiền, chúng tôi sẽ áp dụng tỷ giá hối đoái tốt nhất.", "test4": "Tỷ giá hối đoái tối ưu của Công ty được áp dụng trong trường hợp chuyển tiền giữa các tài khoản cần được trao đổi."}}, "check": {"amount": {"test1": "<PERSON><PERSON> tiền không được để trống", "test2": "<PERSON><PERSON> lòng nh<PERSON>p đúng nội dung", "test3": "<PERSON><PERSON><PERSON><PERSON> đủ số dư tài khoản hiện tại", "test4": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> thấp hơn giới hạn rút tiền tối thiểu", "test5": "<PERSON><PERSON><PERSON><PERSON> vư<PERSON>t quá giới hạn rút tiền tối đa"}, "bank_name": {"test1": "<PERSON>ê<PERSON> ngân hàng không đư<PERSON><PERSON> để trống"}, "psp_currency": {"test1": "<PERSON><PERSON> lòng chọn đồng tiền vàng"}, "name_on_card": {"test1": "Tên tài khoản không được để trống", "jpbanck_test1": "<PERSON><PERSON> lòng nhập tên tài khoản dưới dạng katakana"}, "branch_name": {"test1": "<PERSON>ê<PERSON> chi nh<PERSON>h không được để trống"}, "card_number": {"test1": "Số thẻ không được để trống", "test2": "Số thẻ Tối thiểu 5 chữ số Tối đa 7 chữ số", "type": "<PERSON><PERSON> lòng chọn lo<PERSON>i"}, "swift": {"test1": "Mã SWIFT không được để trống"}, "email": {"test1": "<PERSON><PERSON> lòng nhập địa chỉ hộp thư đúng định dạng", "test2": "<PERSON><PERSON><PERSON> hiện tại không phù hợp với hộp thư trong thông tin tài khoản", "test3": "<PERSON><PERSON><PERSON> thư không đ<PERSON><PERSON><PERSON> để trống"}, "company_name": {"test1": "<PERSON><PERSON> lòng nhập tên công ty tiền ảo"}, "address": {"test1": "<PERSON><PERSON> lòng nhập địa chỉ công ty tiền ảo", "test2": "Địa chỉ ngân hàng không được để trống"}, "branch_city": {"test1": "Địa chỉ không được để trống "}, "branch_code": {"test1": "<PERSON><PERSON> liên kết không được để trống", "test2": "Mã chi nh<PERSON>h là 3 chữ số"}, "bank_code": {"test1": "<PERSON>ã ngân hàng không đư<PERSON>c để trống", "test2": "<PERSON><PERSON> ngân hàng có 4 chữ số"}, "phone": {"test1": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "test2": "<PERSON><PERSON> lòng nhập số điện thoại 11 chữ số"}, "crypto": {"test1": "<PERSON><PERSON><PERSON> lo<PERSON>i tiền ảo không thể để trống"}, "chainType": {"test1": "Blockchain không thể rỗng"}}, "available_balance": "Số dư có sẵn:", "account": "<PERSON><PERSON><PERSON>", "registeredCreditCard": "Thẻ tín dụng đã đăng ký"}, "news": {"button_test1": "<PERSON> tức", "button_test2": "<PERSON><PERSON><PERSON> đ<PERSON>", "button_test3": "Ex-Dividend"}, "nav": {"allNotifications": "<PERSON><PERSON><PERSON> cả thông báo", "allMessages": "<PERSON><PERSON>t cả tin nh<PERSON>n", "allCarts": "<PERSON><PERSON><PERSON> cả xe", "viewCarts": "Xem xe", "hello": "<PERSON><PERSON> ch<PERSON>o", "user": {"available": "Nhưng", "profileTitle": "<PERSON><PERSON> sơ của tôi", "profileSub": "<PERSON>em chi tiết tư liệu cá nhân.", "profileEditTitle": "<PERSON><PERSON><PERSON> trữ biên tập", "profileEditSub": "<PERSON>hay đổi thông tin cá nhân.", "accountSettingTitle": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> tà<PERSON>", "accountSettingSub": "<PERSON><PERSON><PERSON><PERSON> lý các thông số của tài k<PERSON>n.", "privacySettingTitle": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> quyền riêng tư", "privacySettingSub": "<PERSON><PERSON><PERSON> thông số để kiểm soát sự riêng tư của bạn.", "signout": "<PERSON><PERSON><PERSON> xu<PERSON>", "resetCoPassword": "Đặt lại mật khẩu đăng nhập"}}, "dashboard2": {"card1": "Ấn tượng trung bình", "card2": "Tỷ lệ tham gia trung bình", "card3": "Trung bình đạt", "card4": "<PERSON><PERSON><PERSON> chuyển trung bình", "featureTableTitle": "<PERSON><PERSON> s<PERSON>ch thư<PERSON><PERSON> xuyên sau", "summary": "<PERSON><PERSON><PERSON> tắt bài viết", "totalLikeComment": "Luôn thích $ý kiến", "bestPost": "<PERSON><PERSON><PERSON> viên giỏi nhất"}, "dashboard1": {"card1": "<PERSON><PERSON><PERSON> sử giao dịch", "card2": "Đóng vị trí và lỗ", "card3": "<PERSON> tức/<PERSON>hông báo", "card4": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "invoice": "<PERSON><PERSON><PERSON>", "invoiceState": "<PERSON><PERSON><PERSON><PERSON> kê hóa đơn", "openInvoice": "Mở hóa đơn", "monthlyInvoice": "<PERSON><PERSON><PERSON> đơn hàng tháng", "balance": "Số dư", "paymentHistory": "<PERSON><PERSON><PERSON> sử thanh toán", "lastCosts": "Chi phí cuối cùng", "efficiency": "<PERSON><PERSON><PERSON> quả", "winningOrders": "<PERSON><PERSON><PERSON> lợ<PERSON>", "depositAndWithdrawal": "<PERSON><PERSON> sơ tiền xuất nh<PERSON><PERSON> cảnh", "marketSentiment": "Ý định thị trường", "deposit_withdrawal_detail": "<PERSON><PERSON><PERSON> nh<PERSON>p chi tiết", "account_details": "<PERSON> tiết tài k<PERSON>n", "position": "<PERSON>h<PERSON>ng tin lệnh giữ", "history": "<PERSON>h<PERSON>ng tin lệnh đóng", "account": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountBalance": "Số dư tài <PERSON>", "placeholder": "<PERSON><PERSON> lòng ch<PERSON>n", "dropdown_cancel": "Hủy bỏ", "dropdown_done": "<PERSON><PERSON><PERSON> th<PERSON>", "type": {"Deposit": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "Withdrawal": "<PERSON><PERSON><PERSON>"}, "winningOrdersEmpty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "tableColumnField": {"Ticket": "<PERSON><PERSON> thứ tự", "Symbol": "Cặp tiền tệ", "CMD": "<PERSON><PERSON><PERSON>", "OpenDateTime": "Thời gian mở vị trí", "OpenPrice": "Giá mở", "Swaps": "<PERSON><PERSON><PERSON> suất qua đêm", "Commission": "<PERSON><PERSON> lý", "Profit": "<PERSON><PERSON><PERSON>", "Comment": "<PERSON><PERSON><PERSON>", "CloseDateTime": "<PERSON>hờ<PERSON> gian đóng vị trí", "ClosePrice": "<PERSON><PERSON><PERSON>", "Time": "<PERSON><PERSON><PERSON><PERSON> gian", "Lots": "Số tay", "Price": "<PERSON><PERSON><PERSON> c<PERSON>"}, "Status": {"title": "trạng thái", "status_01": "<PERSON><PERSON><PERSON>", "status_02": "<PERSON><PERSON> lưu trữ", "hint": "Vì tài khoản giao dịch của bạn đã được chuyển đến kho lưu trữ, vui lòng liên hệ với bộ phận hỗ trợ khách hàng của chúng tôi để biết số dư tài khoản của bạn."}}, "rightSide": {"customerDistribution": "<PERSON><PERSON> ph<PERSON>i kh<PERSON>ch hàng", "projectStatistic": "<PERSON>h<PERSON>ng kê dự án", "countries": "Quốc gia"}, "dropdown": {"view": "<PERSON> ti<PERSON>", "delete": "Xoá", "edit": "Chỉnh sửa", "print": "Ấn Độ", "download": "<PERSON><PERSON><PERSON> về", "unfollow": "<PERSON><PERSON><PERSON> theo dõi", "follow": "<PERSON>", "block": "<PERSON><PERSON><PERSON><PERSON>"}, "customizer": {"colorCustomizer": "Tùy chỉnh chủ đề", "themeColor": "<PERSON><PERSON><PERSON> sắc chủ đề", "colorMode": "<PERSON><PERSON> độ màu", "sidebar": "<PERSON><PERSON> b<PERSON>n", "sidebarMini": "Thanh Mini", "routeAnimation": "<PERSON><PERSON><PERSON> hình tuyến đường", "rtlMode": "Chế độ RTL", "rtl": "RTL", "reset": "Đặt lại"}, "login": {"ClientOffice": "<PERSON>ăn phòng khách hàng", "PartnerPortal": "<PERSON><PERSON><PERSON> thông tin đối tác", "sigin": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON> thư", "password": "<PERSON><PERSON><PERSON>", "check_code": "<PERSON><PERSON> x<PERSON>n", "enter_email": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> thư", "enter_password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "enter_check_code": "Nhập CAPTCHA", "title": "<PERSON><PERSON><PERSON><PERSON> lý tài khoản của bạn", "forgot_password": "<PERSON><PERSON><PERSON><PERSON> mật khẩu？", "text": "Nhập email và mật khẩu của bạn để truy nhập Client Office.", "reset_password": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "reset_password_confirm": "Gửi CAPTCHA", "reset_password_text": "", "reset_password_text_code": "<PERSON><PERSON> lòng nhập mã xác minh đư<PERSON><PERSON> gửi đến hộp thư của bạn", "reset_password_resend": "<PERSON><PERSON><PERSON> l<PERSON>i", "reset_password_send": "<PERSON><PERSON><PERSON>", "reset_password_submit": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "resendCode": "CAPTCHA đã đư<PERSON><PERSON> g<PERSON>i lại", "tips1": "<PERSON><PERSON><PERSON> kh<PERSON>u của bạn đã đư<PERSON><PERSON> thay đổi thành công", "tips2": "<PERSON><PERSON> lòng đăng nhập lại bằng mật khẩu mới.", "Enter_the_new_password": "<PERSON><PERSON> lòng đặt mật khẩu mới", "New_Password": "<PERSON><PERSON><PERSON> mới", "New_Password_Confirm": "<PERSON><PERSON><PERSON> k<PERSON>u mới (x<PERSON><PERSON>n)", "Password_inconsistency": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "Insufficient_password_complexity": "<PERSON><PERSON><PERSON> có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số", "reset_password_active2": {"desc": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u mới", "new_password": "<PERSON><PERSON><PERSON> mới", "re_password": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "inconsistency": "<PERSON><PERSON><PERSON> kh<PERSON>u không phù hợp", "validateSelection": {"Eight Character": "<PERSON><PERSON><PERSON> ký tự", "One Number": "<PERSON><PERSON>t con số", "One LowerCase": "<PERSON><PERSON><PERSON> chữ thường", "One UpperCase": "<PERSON><PERSON><PERSON> chữ hoa"}, "submit": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "submitSuccess": "Thay đổi mật khẩu đã hoàn tất"}}, "message": {"email": {"error": "<PERSON><PERSON><PERSON> hộp thư thất bại"}}, "accountRegister": {"promotionCheckbox": "Tôi đang tham gia <a target=\"__blank\" href=\"https://myfxmarkets.com/promotion/website-********************/\">khuyến mãi hoàn tiền</a> hiện tại và đã đọc và đồng ý với <a target=\"__blank\" href=\"https://docs.google.com/document/d/1F77_VYR2u7JukYC9sM5TsHscU3tr4wxYsuBukXr80WE/edit\">điều khoản và điều kiện</a>.", "pdfSubmitButton": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "pdfAttention": "<PERSON><PERSON> <PERSON>hi gửi LPOA, bạn sẽ nhận được email", "pdfSubmitHint": "Bạn có thể phải đợi 1-2 ph<PERSON><PERSON>.", "title": "MYFX Markets Mở tài khoản giao dịch mới", "titleSlice01": "MYFX Markets", "titleSlice02": "<PERSON><PERSON><PERSON> cầu mở tài khoản giao dịch mới", "title1": "<PERSON><PERSON><PERSON>n giao dịch cá nhân", "title2": "<PERSON><PERSON><PERSON> k<PERSON>n giao dịch công ty", "step1": "step1", "step2": "step2", "step3": "step3", "description1": "Bước 1：Thông tin cơ bản", "description2": "Bước 2：Vấn đề an ninh", "description3": "Bước 3：Thông tin tài khoản giao dịch", "label1": "City ", "label2": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i ", "label3": "IB 代码 ", "label4": "Bạn đã nghe nói về MyFX Markets ở đâu? ", "label5": "Địa chỉ ", "label6": "<PERSON><PERSON><PERSON> ", "label7": "<PERSON><PERSON> ng<PERSON> giao d<PERSON>ch ", "label8": "<PERSON><PERSON><PERSON> mật k<PERSON>u ", "label9": "<PERSON><PERSON><PERSON> khẩu bạn đã tạo phải chứa ít nhất", "label10": "Số ID ", "label11": "Name ", "label12": "Bạn đã nghe nói về MyFX Markets ở đâu đó.", "test1": "<PERSON><PERSON> lòng nhập thành phố", "test2": "<PERSON><PERSON> lòng nhập số điện thoại di động", "test3": "<PERSON><PERSON> lòng nhập mã IB", "test4": "<PERSON><PERSON> lòng ch<PERSON>n", "test5": "<PERSON><PERSON> lòng nhập địa chỉ", "test6": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "test7": "<PERSON><PERSON> lòng nhập tên <PERSON> (kanji) của bạn", "test8": "<PERSON><PERSON> lòng nhập số ID", "dataTime": "<PERSON><PERSON> lòng chọn ngà<PERSON> sinh", "checkbox1": "<PERSON><PERSON><PERSON> chữ hoa", "checkbox2": "<PERSON><PERSON><PERSON> chữ thường", "checkbox3": "<PERSON><PERSON>t con số", "checkbox4": "Chiều dài tối thiểu 8 bit", "checkbox_5": "*<PERSON>ăm ký hiệu có thể sử dụng là <span style=\"font-weight: bold;\">!@#._</span>", "checkbox_5_no_html": "*N<PERSON>m ký hiệu có thể sử dụng là !@#._", "checkbox5": "Bằng cách đánh dấu vào ô này, bạn đồng ý rằng bạn đã đọc, hiểu và hoàn toàn đồng ý với Thỏa thuận khách hàng của <a href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/MYFX_Markets_Mauritius_Execution_Policy_Ver_3_August_2024.pdf\" target=\"_blank\">MYFX Markets Mauritius kèm theo.</a>", "checkbox6": "Bằng cách đánh dấu vào ô này, bạn đồng ý rằng bạn đã đọc, hiểu và hoàn toàn đồng ý với <PERSON>h sách Thực thi của <a href=\\\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/MYFX_Markets_Mauritius_Complaints_Policy_Ver_3_August_2024.pdf\\\" target=\\\"_blank\\\">MYFX Markets Mauritius kèm theo.</a>", "checkbox7": "Bằng cách đánh dấu vào ô này, bạn đồng ý rằng bạn đã đọc, hiểu và hoàn toàn đồng ý với <PERSON>h sách Khiếu nại của <a href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/MYFX_Markets_Mauritius_Client_Agreement_Ver_3_August_2024.pdf\" target=\"_blank\">MYFX Markets Mauritius kèm theo.</a>", "select_marketsList": {"select1": {"item": "<PERSON><PERSON><PERSON> cụ tìm kiếm", "value": "search"}, "select2": {"item": "Sự kiện quảng cáo", "value": "promotion"}, "select3": {"item": "Trang web giao dịch Forex", "value": "fxwebsite"}, "select4": {"item": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "value": "ib"}, "select5": {"item": "Khác - <PERSON><PERSON> lòng điền vào bằng cách sử dụng hộp nhập bên dưới", "value": "other"}}, "experienceList": {"select1": {"item": "Trang chủ", "value": "<PERSON><PERSON><PERSON>"}, "select2": {"item": "Một số kinh nghiệm", "value": "Intermediate"}, "select3": {"item": "<PERSON><PERSON><PERSON><PERSON> kinh nghiệm", "value": "Advanced"}}, "mt4AccountTypeList": {"select1": {"item": "MT4 Tài khoản Standard", "value": "Standard"}, "select2": {"item": "MT4 T<PERSON><PERSON> chuy<PERSON>n ng<PERSON>", "value": "Pro"}, "select3": {"item": "MT5 Tài khoản Standard", "value": "st_mt5"}, "select4": {"item": "MT5 T<PERSON><PERSON> chuy<PERSON> ng<PERSON>", "value": "pro_mt5"}, "select5": {"item": "MAM Tà<PERSON>", "value": "MAM"}, "select6": {"item": "PAMM Tài k<PERSON>n", "value": "PAMM"}, "select7": {"item": "MT4 Tài k<PERSON>n", "value": "mt4_micro_standard"}, "select8": {"item": "Copy Trade", "value": "Copy Trade"}}, "tradeNumber": {"select1": {"item": "Nhỏ hơn 10", "value": "Less than 10"}, "select2": {"item": "10 - 100", "value": "10 - 100"}, "select3": {"item": "101 - 1000", "value": "101 - 1000"}, "select4": {"item": "hơn 1000", "value": "More than 1000"}}, "tradeVolume": {"select1": {"item": "Ít hơn 10 bàn tay", "value": "Less than 10 lots"}, "select2": {"item": "10-50 bàn tay", "value": "10 - 50 lots"}, "select3": {"item": "200-1000 lô", "value": "200 - 1000 lots"}, "select4": {"item": "Hơn 1000 bàn tay", "value": "More than 1000 lots"}}, "tradeProducts": {"select1": {"item": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>i", "value": "Forex"}, "select2": {"item": "<PERSON>", "value": "Metals"}, "select3": {"item": "<PERSON><PERSON><PERSON>", "value": "Oil"}, "select4": {"item": "Chỉ số", "value": "Indices"}, "select5": {"item": "<PERSON><PERSON><PERSON><PERSON> điện tử", "value": "Crypto"}, "select6": {"item": "K<PERSON><PERSON><PERSON>", "value": "Other"}}, "initialDeposit": {"select1": {"item": "Dưới 1.000 USD (hoặc tương đương)", "value": "Less than 1,000 USD (Or equvalent)"}, "select2": {"item": "$1.000 - $5.000 (hoặc tương đương)", "value": "1,000 - 5,000 USD (Or equvalent)"}, "select3": {"item": "$5.000 - $20.000 (hoặc tương đương)", "value": "5,000 - 20,000 USD (Or equvalent)"}, "select4": {"item": "$20,000 trở lên (hoặc tương đương)", "value": "More than 20,000 USD (Or equvalent)"}}, "expectedTradeVolume": {"select1": {"item": "Ít hơn 10 bàn tay", "value": "Less than 10 lots"}, "select2": {"item": "10-50 bàn tay", "value": "10 - 50 lots"}, "select3": {"item": "50-200 lô", "value": "50 - 200 lots"}, "select4": {"item": "Hơn 1000 bàn tay", "value": "More than 1000 lots"}}, "IB_test": "Tài khoản IB là số mà công ty chúng tôi sử dụng để xác định ai đã giới thiệu bạn để mở tài khoản. Nếu bạn nhận được bất kỳ hướng dẫn nào từ bên giới thiệu (cá nhân), vui lòng nhập số tài khoản được chỉ định. Xin lưu ý rằng khi mở tài khoản MAMIPAMM, bạn phải nhập số tài khoản IB:", "bottom_test1": "Warning: ", "bottom_test2": "FX market involves significant risks, including complete possible loss of funds. Consequently trading is not suitable for all investors and traders. By increasing leverage risk increases as well.", "bottom_test3": "Legal: ", "bottom_test4": "MYFX Markets, the trading name of AXIS INC., is incorporated under registered number L15835/MYFX by the Registrar of International Business Companies, and registered by the Financial Services Authority.", "bottom_test5": "Myfx Markets, the trading name of Milson’s Fintech, provides you with educational resources to help you become familiar with all the trading features and tools in the trading platform. With the demo account you can test any trading strategies you wish in a risk-free environment. Please bear in mind that the results of the transactions of the practice account are virtual, and do not reflect any real profit or loss or a real trading environment, whereas market conditions may affect both the quotation and execution. FX products are leveraged products and trading FX therefore involves a high level of risk that may not be suitable for everyone. Myfx Markets recommends that you ensure that you fully understand the risks involved before making any decision concerning Myfx Markets’ products. Independent advice should be sought if necessary.", "bottom_test6": "Current Disclaimer: The content of this website does not constitute a recommendation and, consequently, you should consider the information in light of your objectives, financial situation and needs before making any decision about whether to acquire any MYFX Markets’ financial products. A Disclosure Document is available here or sending <NAME_EMAIL> or by calling +64 9 889 4022, which should be considered prior to acquiring or continuing to hold CFDs. Information about our services, including our fees and charges is also available at those sources.", "check": {"phone": {"test1": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ: <PERSON><PERSON> điện thoại phải là số.", "test2": "7-14 chữ số", "test3": "<PERSON><PERSON><PERSON> vực này là bắt buộc."}, "address": {"test1": "<PERSON><PERSON><PERSON> vực này là bắt buộc.", "test2": "Tr<PERSON><PERSON><PERSON> hiện tại yêu cầu tối thiểu hai ký tự Trung Quốc.", "test3": "<PERSON><PERSON> lòng nhập địa chỉ cư trú hợp lệ."}, "date": {"test1": "<PERSON><PERSON><PERSON> vực này là bắt buộc.", "test2": "Dưới 18 tuổi cấm mở tài khoản."}, "security": {"test1": "<PERSON><PERSON> lòng chọn câu hỏi bảo mật khác.", "test2": "<PERSON><PERSON><PERSON> vực này là bắt buộc.", "test3": "<PERSON><PERSON> lòng chọn câu hỏi bảo mật.", "test4": "<PERSON><PERSON> lòng nhập tiếng Anh hoặc phiên âm.", "placeholder": "<PERSON><PERSON> lòng chọn câu hỏi"}, "specification": {"test1": "<PERSON><PERSON><PERSON> vực này là bắt buộc."}, "nameCN": {"test1": "<PERSON><PERSON><PERSON> vực này là bắt buộc.", "test2": "<PERSON><PERSON> lòng <PERSON><PERSON>.", "test3": "<PERSON><PERSON><PERSON> thi<PERSON>u 2 ch<PERSON> Kanji"}, "cid": {"test1": "<PERSON><PERSON><PERSON> vực này là bắt buộc.", "test2": "<PERSON><PERSON> lòng nhập đúng định dạng số ID."}}, "security_querstion": {"title": "Đặt câu hỏi bảo mật", "test": "<PERSON><PERSON> lòng chọn câu hỏi bảo mật. <PERSON>hi bạn quên mật khẩu đăng nhập Khu vực khách hàng hoặc gửi yêu cầu thanh toán, các câu hỏi bảo mật có thể giúp chúng tôi xác minh danh tính của bạn.", "label1": "QUESTION 1 *", "label2": "QUESTION 2 *", "label3": "QUESTION 3 *", "test1": "<PERSON><PERSON> lòng nhập câu trả lời"}, "account_specification": {"label1": "<PERSON><PERSON><PERSON> tiền tệ tài k<PERSON>n", "label2": "<PERSON><PERSON><PERSON> tà<PERSON>", "label3": "Trang chủ ", "label4": "<PERSON><PERSON><PERSON>", "label5": "Thỏa thuận sử dụng dịch vụ", "test1": "MYFX Markets Thỏa thuận khách hàng", "test2": "MYFX Markets Tuyên bố công bố", "test3": "<PERSON>hi bạn đánh dấu vào đây, bạn đã đọc và đồng ý với MYFX Markets và Tuyên bố Tiết lộ. Bạn cũng hiểu được rủi ro cao của chính sản phẩm ký quỹ."}, "radioText": "Đ<PERSON>n bẩy 500: 1 khi số dư tài khoản dưới 50.000 USD. Khi số dư vượt quá 50.000 USD, đòn bẩy sẽ tự động được điều chỉnh thành 400:1.", "radioText1000": "Đ<PERSON>n bẩy 1000: 1 khi số dư tài khoản dưới 7.000 USD. Khi số dư vượt quá 7.000 USD, đòn bẩy sẽ tự động được điều chỉnh thành 500:1.", "next": "Next", "submit": "Submit", "successMsg": "Bạn đã đặt câu hỏi bảo mật", "month": "<PERSON><PERSON><PERSON><PERSON>", "year": "Năm", "day": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "complete": "<PERSON><PERSON><PERSON>", "StepOneForm": {"label1": "<PERSON><PERSON><PERSON>", "label1_error1": "<PERSON><PERSON> lòng nhập tên <PERSON>", "label1_error2": "<PERSON><PERSON> lòng nhập ký tự Trung Quốc", "label1_error3": "Ít nhất 2 ký tự Trung Quốc", "label1_error4": "<PERSON><PERSON> lòng nhập tên", "label1_error5": "Ít nhất 2 ký tự", "label1_placeholder": "<PERSON><PERSON> lòng nhập tên <PERSON>", "label2": "Số ID", "label2_error1": "<PERSON><PERSON> lòng nhập số ID", "label2_error2": "<PERSON><PERSON> lòng nh<PERSON>p đúng số <PERSON>", "label2_placeholder": "<PERSON><PERSON> lòng nhập số ID", "label3": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "label3_error1": "<PERSON><PERSON> lòng nhập số điện thoại", "label3_error2": "<PERSON><PERSON> lòng nh<PERSON>p đúng số", "label3_error3": "<PERSON><PERSON> l<PERSON> 7-14 chữ số", "label3_placeholder": "<PERSON><PERSON> lòng nhập số điện thoại", "label4": "IB Code", "label4_error1": "<PERSON><PERSON> lòng nhập chữ cái hoặc số", "label4_placeholder": "<PERSON><PERSON> lòng nhập IB Code", "label5": "Ông nghe từ đâu MYFX MARKETS", "label5_error1": "<PERSON><PERSON> lòng chọn nơi bạn nghe từ MYFX MARKETS", "label5_placeholder": "<PERSON><PERSON> lòng chọn nơi bạn nghe từ MYFX MARKETS", "label6": "<PERSON><PERSON> lòng nhập nơi bạn nghe nói MYFX MARKETS", "label6_error1": "<PERSON><PERSON> lòng nhập nơi bạn nghe nói MYFX MARKETS", "label6_placeholder": "<PERSON><PERSON> lòng nhập nơi bạn nghe nói MYFX MARKETS", "label7": "Địa chỉ", "label7_error1": "<PERSON><PERSON> lòng nhập địa chỉ", "label7_placeholder": "<PERSON><PERSON> lòng nhập địa chỉ", "label8": "<PERSON><PERSON><PERSON>", "label8_error1": "<PERSON><PERSON> lòng chọn ngà<PERSON> sinh", "label8_error2": "Dưới 18 tuổi Không đư<PERSON><PERSON> phép đăng ký", "label8_placeholder": "<PERSON><PERSON> lòng chọn ngà<PERSON> sinh", "label9": "ên công ty", "label9_error1": "<PERSON><PERSON> lòng nhập tên công ty", "label9_error2": "<PERSON><PERSON> lòng nhập ký tự tiếng <PERSON>h", "label9_placeholder": "<PERSON><PERSON> lòng nhập tên công ty", "label10": "Số đăng ký công ty", "label10_error1": "<PERSON><PERSON> lòng nhập số đăng ký công ty", "label10_placeholder": "<PERSON><PERSON> lòng nhập số đăng ký công ty", "label11": "<PERSON><PERSON> ng<PERSON> giao d<PERSON>ch", "label11_error1": "<PERSON><PERSON> lòng chọn kinh nghiệm giao dịch", "label11_placeholder": "<PERSON><PERSON> lòng chọn kinh nghiệm giao dịch", "label12": "<PERSON><PERSON><PERSON>", "label12_error1": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "label12_error2": "<PERSON><PERSON> nh<PERSON>t một chữ in hoa", "label12_error3": "<PERSON><PERSON> nhất một chữ cái thường", "label12_error4": "<PERSON><PERSON> n<PERSON><PERSON>t một số", "label12_error5": "Ít nhất 8 ký tự", "label12_placeholder": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "label13": "S<PERSON> lượng giao dịch trung bình hàng tháng", "label14": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> giao dịch trung bình hàng tháng", "label15": "<PERSON><PERSON><PERSON><PERSON> sản phẩm bạn giao dịch với", "label16": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i ban đầu dự kiến", "label17": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> giao dịch dự kiến hàng tháng", "label18": "<PERSON><PERSON><PERSON> thận", "label18_placeholder": "<PERSON><PERSON> lòng nh<PERSON><PERSON> cảnh báo", "label19": "MYFX Markets Thỏa thuận khách hàng Mauritius:", "label20": "MYFX Markets Việt Nam:", "label21": "<PERSON><PERSON><PERSON> s<PERSON>ch khi<PERSON>u nại của MYFX Markets Mauritius:", "label19_error": "<PERSON>ui lòng đánh dấu vào <PERSON>"}, "StepTwoForm": {"label1": "Câu hỏi 1", "label1_error1": "<PERSON><PERSON> lòng chọn câu hỏi", "label1_error2": "<PERSON><PERSON><PERSON><PERSON> chọn câu hỏi lặp lại", "label1_error3": "<PERSON><PERSON> lòng nhập câu trả lời", "label1_placeholder1": "<PERSON><PERSON> lòng chọn câu hỏi 1", "label1_placeholder2": "<PERSON><PERSON> lòng nhập câu hỏi 1 câu trả lời", "label2": "Câu hỏi 2", "label2_error1": "<PERSON><PERSON> lòng chọn câu hỏi", "label2_error2": "<PERSON><PERSON><PERSON><PERSON> chọn câu hỏi lặp lại", "label2_error3": "<PERSON><PERSON> lòng nhập câu trả lời", "label2_placeholder1": "<PERSON><PERSON> lòng chọn câu hỏi 2", "label2_placeholder2": "<PERSON><PERSON> lòng nhập câu hỏi 2 câu trả lời", "label3": "Câu hỏi 3", "label3_error1": "<PERSON><PERSON> lòng chọn câu hỏi", "label3_error2": "<PERSON><PERSON><PERSON><PERSON> chọn câu hỏi lặp lại", "label3_error3": "<PERSON><PERSON> lòng nhập câu trả lời", "label3_placeholder1": "<PERSON><PERSON> lòng chọn câu hỏi 3", "label3_placeholder2": "<PERSON><PERSON> lòng nhập câu hỏi 3 câu trả lời"}, "StepThreeForm": {"label1": "<PERSON><PERSON><PERSON> tiền tệ tài k<PERSON>n", "label1_error1": "<PERSON><PERSON> lòng chọn loại tiền tệ tài khoản", "label1_placeholder": "<PERSON><PERSON> lòng chọn loại tiền tệ tài khoản", "label2": "<PERSON><PERSON><PERSON> tà<PERSON>", "label2_error1": "<PERSON><PERSON> lòng chọn loại tài k<PERSON>n", "label2_placeholder": "<PERSON><PERSON> lòng chọn loại tài k<PERSON>n", "label3": "Trang chủ", "label3_error1": "<PERSON><PERSON> lòng chọn <PERSON> b<PERSON>y", "label3_error2": "Đồng ý và đánh dấu thỏa thuận này", "label3_placeholder": "<PERSON><PERSON> lòng chọn <PERSON> b<PERSON>y", "label4": "<PERSON><PERSON><PERSON>", "label4_error1": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú", "label4_placeholder": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú", "label5": "Thỏa thuận khách hàng", "label5_error1": "<PERSON>ui lòng đọc và đồng ý với Thỏa thuận", "label5_error2": "<PERSON><PERSON> lòng xác nhận thỏa thuận"}}, "successPage": {"content": "Bạn đã đặt câu hỏi bảo mật."}, "errorPage": {"back": "Quay lại trang chủ"}, "questionnaire": {"title": "Tham gia như một đối tác giới thiệu MYFX Markets", "label1": "<PERSON><PERSON><PERSON> khu vực nguồn chính của khách hàng mà bạn giới thiệu là gì?", "label2": "Bạn sử dụng phương pháp nào để có được khách hàng mà bạn giới thiệu? (Xin lưu ý rằng trang web và phương tiện truyền thông xã hội của bạn yêu cầu từ chối trách nhiệm về rủi ro.)", "label3": "Bạn có phải là IB của IB hoặc bất kỳ nhà môi giới nào khác không? Nếu có, hãy cung cấp tên của nhà môi giới", "label4": "Mỗi tháng, bạn sẽ giới thiệu bao nhiêu khách hàng?", "label5": "Trung bình, bạn muốn khách hàng của mình tiết kiệm được bao nhiêu tiền (bằng USD)?", "label6": "Loại tiền tệ nào bạn muốn nhận lại tiền?", "agreeTitle": "Tôi thừa nhận và đồng ý：", "agree1": "<PERSON><PERSON><PERSON> điều khoản và điều kiện mà tôi hiểu và đồng ý", "agree2": "Tôi đồng ý và hiểu rằng với tư cách là một đối tác, tôi sẽ được trả tiền cho các giao dịch ngoại hối và vàng với 2 đô la cho mỗi giao dịch đón<PERSON> (<PERSON>h<PERSON> hồi) trên tài khoản Pro-USD và 0,5 pip cho mỗi giao dịch đón<PERSON> (khứ hồi) trên tài khoản Standard. Tài khoản không phải USD với mức giá khác nhau", "agree3": "<PERSON><PERSON><PERSON> xác nhận rằng tất cả thông tin được cung cấp trong mẫu này là đúng, ch<PERSON><PERSON> xác và đầy đủ.", "agree4": "Tôi cam kết thông báo kịp thời cho MyFX Marketplace về bất kỳ cập nhật hoặc sửa đổi nào đối với thông tin trên.", "agree5": "Bằng cách đăng ký làm Đ<PERSON><PERSON> tá<PERSON>, tôi thừa nhận và xác nhận rằng tôi đã tuân thủ tất cả các luật hiện hành trong khu vực pháp lý của mình và sẽ không tham gia vào bất kỳ hoạt động mời chào nào trong khu vực pháp lý bị cấm.", "agree6": "Tôi tuyên bố rằng bằng cách đăng ký làm đối tác, tôi đồng ý rằng tôi không vi phạm bất kỳ luật nào trong thẩm quyền tư pháp của tôi và sẽ không mời chào trong thẩm quyền tư pháp bị cấm.", "send": "<PERSON><PERSON><PERSON>", "validate": "<PERSON><PERSON><PERSON> vực này là bắt buộc."}, "InternationalBankWire": {"BankName": "<PERSON><PERSON> h<PERSON>", "BankAddress": "Đ<PERSON>a chỉ ngân hàng", "AccountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "AccountNumber": "Số tài <PERSON>n", "BeneficiaryBankAddress": "<PERSON><PERSON><PERSON> chỉ ngân hàng thu tiền", "BICSWIFT": "BICSWIFT", "BSBNumber": "Số BSB", "Remarks": "<PERSON><PERSON><PERSON>", "MT4_MT5_Login": "MT4・MT5 Vui lòng nhập số đăng nhập", "mauritiusTableData": {"AccountName": "tên tà<PERSON>n", "Currency": "ti<PERSON><PERSON> tệ", "SwiftCode": "Mã Swift", "IBAN": "tà<PERSON> k<PERSON><PERSON>n ngân hàng quốc tế", "AccountHolderAddress": "Địa chỉ chủ tài k<PERSON>n", "BankOneAddress": "Địa chỉ BankOne"}}, "JPDeposit": {"hint1": "Đơn xin chuyển tiền ngân hàng JPY nội địa của bạn đã đư<PERSON><PERSON> chấp nhận.", "hint1_1": "<PERSON><PERSON> lòng truy cập vào tài khoản ngân hàng bên dưới. <PERSON> tiết có thể xem email \"Thủ tục chuyển khoản ngân hàng đã hoàn thành\".", "hint2": "※ <PERSON>hi yêu cầu gửi tiền, <span style=\"color: red; font-weight: bold\">hãy chắc chắn thêm số hóa đơn của bạn trước tên của người yêu cầu chuyển tiền.</span>", "hint3": "※ Thông tin chi tiết về tài khoản ngân hàng được đưa ra mỗi lần yêu cầu gửi tiền, vì vậy nó không thể được sử dụng cho lần chuyển tiếp tiếp theo.", "hint4": "<PERSON><PERSON> lòng gửi yêu cầu gửi tiền mỗi lần.", "hint5": "<PERSON><PERSON><PERSON> bạn quên ghi rõ số hóa đơn trước tên người gửi tiền, vui lòng liên hệ với bộ phận hỗ trợ khách hàng của chúng tôi.", "hint6": "※ Việc xác nhận tiền gửi có thể mất khoảng 30 phút sau khi hoàn tất thủ tục chuyển khoản. Thời gian phản ánh có thể lâu hơn do thời điểm chuyển tiền giữa các ngân hàng.", "hint7": "※ Từ 21 giờ Chủ nhật đến sáng hôm sau (theo giờ Nhật Bản), sẽ có bảo trì ngân hàng. <PERSON><PERSON><PERSON> khoản tiền gửi thực hiện trong thời gian này sẽ được phản ánh sau 9 giờ sáng thứ <PERSON> (theo giờ Nhật Bản).", "BankName": "<PERSON><PERSON> h<PERSON>", "BranchName": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "BranchServiceNumber": "Số dịch vụ chi n<PERSON>h", "AccountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "AccountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "AccountNumber": "Số tài <PERSON>n", "BillingNumber": "<PERSON><PERSON><PERSON> c<PERSON>u s<PERSON>", "BillingNumberHint": "<PERSON><PERSON> lòng điền vào trước tên yêu cầu chuyển tiền.", "Amount": "<PERSON><PERSON> tiền g<PERSON>i", "TradingAccountNumber": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "BillingNumberDes": "<span style=\"color: red; font-size: 12px\">Vui lòng ghi tên người chuyển khoản ở phía trước (ví dụ) 12345 Tanaka Taro. Nếu bạn quên ghi, vui lòng liên hệ với bộ phận hỗ trợ khách hàng của chúng tôi.</span>"}, "bank": {"label1": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label2": "<PERSON><PERSON> ngân hàng", "label3": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "label4": "Số chi nhánh/BSB/Mã sắp xếp", "label5": "Tên tài k<PERSON>n thụ hưởng (tên gi<PERSON> nửa rộng)", "label6": "Số tài <PERSON>n", "label7": "<PERSON><PERSON><PERSON> tà<PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>"}, "loginBtn": {"btn1": "Quay lại phiên bản cũ", "btn2": "<PERSON><PERSON><PERSON> ký tài k<PERSON>n"}, "DepositsSuccess": "<PERSON><PERSON><PERSON> động thành công", "MAM_PAMM": {"type1": "<PERSON><PERSON><PERSON> r<PERSON>t tiền", "type2": "<PERSON><PERSON><PERSON> cả tiền", "submitSuccess": "Đơn xin rút tiền đã hoàn tất.", "hint_01": "<PERSON><PERSON><PERSON> tiền từ tài khoản MAM/PAMM vào ví MYFX, do nhà điều hành thực hiện thủ tục phê duyệt.", "hint_02": "Bởi vì căn cứ vào tình trạng giao dịch cũng có tình huống b<PERSON><PERSON> lư<PERSON>, xin thông cảm.", "hint_03": "Trong trường hợp có một vị trí trong giao d<PERSON>, vi<PERSON><PERSON> rút tiền đư<PERSON><PERSON> thực hiện sau khi giải quyết vị trí đó. Trong trườ<PERSON> hợp đó, số dư sẽ thay đổi sau khi thanh toán, vì vậy số tiền ra có thể khác với số tiền nhập sau đây."}, "lpoa": {"label1": "<PERSON><PERSON> ký", "label3": "Signature", "placeholder1": "trùng với tên tiếng <PERSON>h của mình.", "label2": "<PERSON>ê<PERSON> đ<PERSON> đủ (<PERSON>ê<PERSON> <PERSON><PERSON><PERSON>)", "label4": "Full name", "tips": "Bạn có thể phải đợi 1-2 ph<PERSON><PERSON>.", "button1": "<PERSON><PERSON><PERSON> đồng <PERSON>", "error1": "Chữ ký không phù hợp, yêu cầu ký lại", "signSuccess": {"title": "Thank you for signing up a new LPOA contract", "desc1": "Your new LPOA contact wille be sent to you by email shortly.", "desc2": "If you have any questions, please contact our Customer Support at"}}, "systemError": {"err_01": "Đã xảy ra lỗi！", "err_02": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang này.", "err_03": "<PERSON><PERSON> không tồn tại."}, "securityDialog": {"title": "Thông tin an ninh quan trọng!", "content": "<PERSON>ì lý do bả<PERSON> mật, chúng tôi muốn tất cả khách hàng thiết lập một bộ câu hỏi bảo mật ngay lập tức khi sử dụng yêu cầu rút tiền và thay đổi mật khẩu. Vui lòng truy cập trang Cài đặt để xem câu hỏi bảo mật. Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với dịch vụ khách hàng qua email hoặc trò chuyện trực tiếp. Cám ơn!", "button": "Chuyển đến trang <PERSON>i đặt"}, "login_param": {"resetCOPassWdSuccess": {"01": "<PERSON><PERSON><PERSON><PERSON> thay đổi mật khẩu đã hoàn tất.", "02": "<PERSON><PERSON> lòng đăng nhập lại bằng mật khẩu mới."}, "locked": {"01": "<PERSON><PERSON><PERSON> kho<PERSON>n đã bị khóa. Phục vụ khách hàng xin liên hệ mở khóa. Địa chỉ hộp thư:<EMAIL>"}, "sqLocked": {"01": "{email} Your account has been locked because you failed to answer your secret question multiple times. Please contact customer support to unlock your account."}}, "DRIVER": {"start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "prev": "<PERSON><PERSON><PERSON><PERSON> trước", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "stepShowTitle": "Chào mừng！", "stepShowContent": "Nếu đây là lần đầu tiên bạn ở lại với chúng tôi, chúng tôi khuyên bạn nên thực hiện một hướng dẫn ngắn sẽ giúp bạn điều hướng nhanh hơn xung quanh trung tâm.", "step1Title": "Trung tâm dữ liệu", "step1Content": "B<PERSON>n có thể xem hiệu suất tà<PERSON>, lịch sử giao dịch và bất kỳ tin tức hoặc khuyến mãi gần đây nào.", "step2Title": "Tài khoản của tôi - Thông tin tài khoản", "step2Content": "<PERSON>u<PERSON>n lý tài khoản giao dịch trực tiếp của bạn tại đây. Bạn có thể xem thông tin tài khoản của bạn, thay đổi mật khẩu tài khoản giao dịch của bạn và nhiều hơn nữa.", "step3Title": "<PERSON><PERSON><PERSON> k<PERSON>n của tôi - <PERSON><PERSON><PERSON><PERSON> lý tệp", "step3Content": "Bạn có thể tải lên các tài liệu nhận dạng của bạn để xác minh.", "step4Title": "<PERSON><PERSON><PERSON> kho<PERSON>n của tôi - Đặt lại mật khẩu đăng nhập", "step4Content": "<PERSON>hay đ<PERSON>i mật khẩu cho văn phòng khách hàng.", "step5Title": "<PERSON><PERSON><PERSON>n của tôi - <PERSON><PERSON><PERSON> trữ lịch sử thư<PERSON>ng mại", "step5Content": "Bạn có thể tải xuống lịch sử giao dịch của tài khoản đã được chuyển đổi thành trạng thái lưu trữ.", "step6Title": "<PERSON><PERSON><PERSON><PERSON> xuất nhập cảnh và chuyển khoản nội bộ", "step6Content": "<PERSON><PERSON><PERSON> tiền vào tài khoản của bạn, chuyển tiền giữa các tài khoản giao dịch của bạn hoặc gửi yêu cầu rút tiền.", "step7Title": "<PERSON><PERSON><PERSON><PERSON>", "step7Content": "Đối với kh<PERSON>ch hàng sử dụng tài khoản MAM/PAMM, tiền sẽ được rút và chuyển tiền nội bộ thông qua kênh này.", "step8Title": "Mở tài kho<PERSON>n giao dịch mới", "step8Content": "<PERSON><PERSON><PERSON> bạn muốn mở một tài k<PERSON> k<PERSON>, vui lòng gửi yêu cầu qua trang này.", "step9Title": "<PERSON><PERSON>n giao d<PERSON>ch", "step9Content": "T<PERSON>i xuống nền tảng MT4/MT5 của bạn tại đây.", "step10Title": "<PERSON> tức", "step10Content": "<PERSON><PERSON> tin tức, thông tin khu<PERSON>ến mãi và thông tin chia cổ tức của chúng tôi.", "step11Title": "<PERSON><PERSON><PERSON> qua mail", "step11Content": "<PERSON><PERSON><PERSON> câu hỏi, mối quan tâm hoặc bình luận của bạn tại đây.", "step12Title": "Trang <PERSON> ch<PERSON>h thức", "step12Content": "<PERSON><PERSON> đến trang web ch<PERSON>h thức của chúng tôi.", "step13Title": "Trở lại phiên bản cũ", "step13Content": "<PERSON><PERSON>n có thể sử dụng phiên bản cũ của văn phòng khách hàng của chúng tôi trong một thời gian."}, "Campaign": {"title_01": "Trang web ch<PERSON>h thức mới và văn phòng khách hàng ra mắt!", "more": "<PERSON><PERSON><PERSON><PERSON>"}, "InviteFriends": {"inviteBtn": "<PERSON><PERSON><PERSON> bạn bè", "link": "Chương trình giới thiệu bạn bè là gì?", "title": "<PERSON><PERSON><PERSON> bạn bè của bạn", "desc": {"desc1": "<PERSON><PERSON> lòng nhập chi tiết khách hàng mà bạn muốn giới thiệu.", "desc2": "Gửi URL chuyên dụng để mở tài khoản đến địa chỉ hộp thư bạn đã nhập.", "desc3": "Thông tin của bạn chỉ được sử dụng để gửi URL chuyên dụng để mở tài khoản."}, "form": {"name": "<PERSON><PERSON><PERSON> c<PERSON>a bạn bạn", "email": "<PERSON><PERSON><PERSON> thư", "message": "Thông tin", "language": {"title": "<PERSON><PERSON><PERSON>", "english": "Tiếng <PERSON>", "japanese": "<PERSON><PERSON><PERSON><PERSON>"}, "placeholder1": "<PERSON><PERSON> lòng nh<PERSON>p", "placeholder2": "<PERSON><PERSON> lòng ch<PERSON>n"}, "validate": {"name": "<PERSON><PERSON> lòng nhập tên của bạn", "email": "<PERSON><PERSON> lòng nhập địa chỉ email của bạn", "emailFormat": "<PERSON><PERSON> lòng nhập đúng định dạng", "emailSame": "Địa chỉ email này đã được đăng ký"}, "confirm": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "success": "<PERSON><PERSON><PERSON><PERSON> công", "submitSuccess": "<PERSON><PERSON><PERSON> thành công", "back": "Quay lại"}, "Version": {"title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> cập nh<PERSON>t", "button": "<PERSON><PERSON><PERSON> nh<PERSON>t ngay", "hint": "Click vào Update Now. <PERSON><PERSON><PERSON> thông báo này xuất hiện trở lại sau khi nhấp vào, hãy thử khởi động lại trình duyệt hoặc đăng nhập lại vào văn phòng khách hàng."}, "LPOAUpdateDialog": {"title": "<PERSON><PERSON><PERSON> b<PERSON> nhi<PERSON> có g<PERSON> hạn (LPOA)", "alert": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> quyền có gi<PERSON> hạn (LPOA) đã được cập nhật. <PERSON><PERSON> lòng xác nhận và nhấn “Đồng <PERSON>” để tiếp tục sử dụng dịch vụ.", "hint": "MYFX Markets đã thêm mục 12 vào \"<PERSON><PERSON><PERSON><PERSON> <PERSON>y quyền có giới hạn (LPOA)\" mà khách hàng mở tài khoản MAM/PAMM đồng ý. Mặc dù nó không ảnh hưởng đến cách bạn sử dụng dịch vụ MAM/PAMM nhưng nó nhằm mục đích giúp khách hàng hiểu những gì họ có thể mong đợi khi sử dụng dịch vụ.", "warn": "Tên của các chiến lược MAM/PAMM tham gia và phí thành công sẽ không thay đổi so với những gì đã được thỏa thuận.", "subHint_01": "<PERSON><PERSON><PERSON> bạn không đồng ý với G<PERSON>ấy ủy quyền có giới hạn (LPOA),", "subHint_02": "Dịch vụ MAM/PAMM sẽ không còn khả dụng nữa. Trong trường hợ<PERSON> nà<PERSON>, vui lòng thông báo cho chúng tôi bằng cách gửi email tới <EMAIL> từ địa chỉ email đã đăng ký của bạn rằng bạn không đồng ý với những thay đổi. Đóng tài khoản MAM/PAMM của bạn và rút tiền của bạn.", "subHint_03": "<PERSON><PERSON>g tôi sẽ chuyển nó vào ví MYFX của bạn.", "button": "<PERSON><PERSON>ng <PERSON>"}, "promotion": {"tab": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "inProcess": "<PERSON><PERSON> lý", "expired": "<PERSON><PERSON><PERSON>"}, "notHave": "có A thường xuyên không có sự kiện nào", "activityList": {"mainTitle": "<PERSON><PERSON><PERSON> dịch quảng c<PERSON>o của tôi", "title1": "Cashback lên đến 300.000 yên!<br>Hỗ trợ giao dịch của bạn!", "content1": "Tất cả mọi người đủ điều kiện đều có thể nhận lại tiền mặt! Nhận cashback chỉ với 3 lots", "title2": "Thu thập vé may mắn và nhận quà bí ẩn!", "content2": "Tham gia chương trình khuyến mãi của chúng tôi để có cơ hội giành được những món quà bí ẩn", "title3": "Nhận tiền thưởng lên đến $500 cho khoản tiền gửi đầu tiên của bạn!", "content3": "Cashback cho tất cả mọi người!<br>Get cashback starting from just 3 lots!", "title4": "Get up to $400 Bonus on Your First Deposit at MYFX Markets!!", "content4": "• <span style=\"font-weight: bold\">Sign Up</span>: <PERSON><PERSON><PERSON> tà<PERSON>n của bạn.<br>\n • <span style=\"font-weight: bold\">Deposit</span>:Tiền gửi đầu tiên.<br>\n • <span style=\"font-weight: bold\">Get Bonus</span>: 50% tiền thưởng, lên đến $400!", "content4_100": "• <span style=\"font-weight: bold\">Sign Up</span>: <PERSON><PERSON><PERSON> tà<PERSON>n của bạn.<br>\n • <span style=\"font-weight: bold\">Deposit</span>:Tiền gửi đầu tiên.<br>\n • <span style=\"font-weight: bold\">Get Bonus</span>: 100% tiền thưởng, lên đến $400!", "permission": "chỉ khách hàng mới", "viewBtn": "<PERSON>em chi tiết", "endDay": "<PERSON><PERSON><PERSON> động kết thúc với<span style=\"color: red; font-weight: 700\">{day} Ngày</span>", "buttonText1": "<PERSON>em chi tiết", "buttonText2": "<PERSON><PERSON><PERSON> t<PERSON>n", "buttonText3": "<PERSON><PERSON>"}, "verify": {"title": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c minh tài k<PERSON>n", "desc": "<PERSON><PERSON> tham gia chương trình khu<PERSON>ến mãi, h<PERSON><PERSON> xác minh tài khoản của bạn. <PERSON>ung cấp bằng chứng nhận dạng và địa chỉ cần thiết để hoàn tất quá trình này", "button": "<PERSON><PERSON><PERSON> minh ngay bây giờ!"}, "up600": {"hint": {"content": "Tôi đã đọc và đồng ý với các <a target=\"_blank\" href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/Welcome+Bonus+Promotion-Term+of+Use.pdf\">đ<PERSON><PERSON><PERSON>n sử dụng</a>.", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "isok_no": "<PERSON><PERSON><PERSON> tất KYC ngay để mở khóa quyền truy cập.", "isok_no_title": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> minh tài k<PERSON>n", "isok_no_button": "<PERSON><PERSON><PERSON> minh ngay!", "button": "<PERSON>ham gia ngay", "button_ok": "Đ<PERSON> tham gia", "account": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "title": "<PERSON><PERSON><PERSON> nhận thưởng chào mừng", "list": ["<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> \"Tham gia ngay\" để tham gia chương trình khu<PERSON>ến mãi.", "<PERSON><PERSON>n số tài khoản bạn muốn tham gia.", "<PERSON><PERSON><PERSON> dấu vào ô để đồng ý với các điều khoản và điều kiện.", "<PERSON><PERSON><PERSON> t<PERSON>.", "Tiền thưởng sẽ được ghi có vào tài khoản của bạn trong vòng 1-2 ngày làm việc kể từ khi gửi tiền", "<span style=\"color: red;\",>*</span><PERSON><PERSON><PERSON> chắc chắn nhấp vào nút \"Tham gia ngay!\" để tham gia chương trình khuyến mãi trư<PERSON><PERSON> khi thực hiện bất kỳ khoản tiền gửi nào. Sau khi bạn tham gia thành công, nút sẽ đổi thành \"Đã tham gia\"."], "footer": "<PERSON><PERSON> thêm chi tiết trên <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/welcome-bonus\"><u>trang k<PERSON><PERSON>ến mãi</u></a>", "note": "Lưu ý: Thưởng 50% chỉ áp dụng cho lần gửi tiền đầu tiên", "note_100": "Lưu ý: Thưởng 100% chỉ áp dụng cho lần gửi tiền đầu tiên", "grid": ["<PERSON><PERSON> tiền g<PERSON>i", "Thưởng"]}, "halloween": {"viewBtn": "<PERSON><PERSON> m<PERSON>i", "title": "Mùa Halloween với chương trình khuyến mãi đặc biệt có thời hạn!", "tag": "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại", "desc": " • <span style=\"font-weight: bold\">Theo：</span><PERSON> dõ<PERSON> tài kho<PERSON>n X ch<PERSON>h thức của MYFX Markets<br/>\n • <span style=\"font-weight: bold\">Chia sẻ：</span>Trích dẫn bài đăng với ý tưởng trang phục của bạn<br/> • <span style=\"font-weight: bold\">Nhận giải thưởng：</span><PERSON><PERSON> cơ hội trúng thưởng 50 đô la!", "info": {"title": "<PERSON> tiết k<PERSON>n mãi🎃", "list": ["<PERSON> t<PERSON> X <PERSON><PERSON>h thức của MYFX Markets (trước đây là Twitter) <a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\" style=\"text-decoration: underline\">\"@MYFXMarketsEN\"</a>.", "Trích dẫn bài đăng quảng cáo có ý tưởng trang phục của bạn và hashtag \"#MYFXMarkets\".", "<PERSON><PERSON> hội trúng thưởng 50 đô la <PERSON> (hoặc loại tiền tệ tương đương)! Mười người chiến thắng may mắn sẽ được chọn!"], "desc": ["📅 Thời gian khu<PERSON>ến mại", "Từ ngày 10 tháng 10 năm 2024 (<PERSON><PERSON><PERSON>) đế<PERSON> ngày 31 tháng 10 năm 2024 (<PERSON><PERSON><PERSON> <PERSON>), 23:59 (<PERSON><PERSON><PERSON> MT4/MT5).", "⏳ Lịch trình phân phối giải thưởng", "Ngày 5 tháng 11 năm 2024 (<PERSON><PERSON><PERSON>): <PERSON><PERSON><PERSON><PERSON> chiến thắng sẽ được thông báo qua tin nhắn trực tiếp.", "Ngày 10 tháng 11 năm 2024 (<PERSON><PERSON>): <PERSON><PERSON><PERSON> thông tin chi tiết của bạn trước 23:59 (Giờ MT4/MT5).", "Ngày 12 tháng 11 năm 2024 (<PERSON><PERSON><PERSON>): G<PERSON><PERSON>i thưởng sẽ được phản ánh vào tài khoản MYFX Markets của bạn.", "<PERSON>ui lòng xem thêm thông tin trên <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/halloween-2024/\"><PERSON><PERSON> <PERSON>ến mãi</a>."], "button": "<a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\"><PERSON> X ngay!</a>"}}, "blackFriday": {"viewBtn": "<PERSON><PERSON> m<PERSON>i", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi Black Friday đặc biệt có giới hạn!", "tag": "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại", "desc": " • <span style=\"font-weight: bold\">Theo：</span><PERSON> tà<PERSON> X ch<PERSON>h thức của MYFX Markets<br/>\n • <span style=\"font-weight: bold\">Chia sẻ：</span><PERSON><PERSON>ng báo giá cho những gì bạn muốn mua nhất<br/> • <span style=\"font-weight: bold\">Nhận giải thưởng：</span><PERSON><PERSON> cơ hội trúng thưởng 50 đô la!", "info": {"title": "<PERSON> tiết khu<PERSON>ến mãi<span style=\"font-family: 'Segoe UI Emoji'\">🛍</span>", "list": ["<PERSON> t<PERSON> X <PERSON><PERSON>h thức của MYFX Markets (trước đây là Twitter) <a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\" style=\"text-decoration: underline\">\"@MYFXMarketsEN\"</a>.", "Trích dẫn bài đăng quảng cáo có ý tưởng trang phục của bạn và hashtag \"#MYFXMarkets\".", "<PERSON><PERSON> hội trúng thưởng 50 đô la <PERSON> (hoặc loại tiền tệ tương đương)! Mười người chiến thắng may mắn sẽ được chọn!"], "desc": ["📅 Thời gian khu<PERSON>ến mại", "<PERSON><PERSON> ngày 25 tháng 11 năm 2024 (<PERSON><PERSON>) đến 23:59 (g<PERSON><PERSON> MT4/MT5) ngày 29 tháng 12 năm 2024 (<PERSON><PERSON>).", "⏳ Lịch trình phân phối giải thưởng", "<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON> 6 tháng 12 năm 2024: <PERSON><PERSON><PERSON><PERSON> chiến thắng sẽ được thông báo qua DM.", "<PERSON><PERSON>, ng<PERSON><PERSON> 10 tháng 12 năm 2024: <PERSON><PERSON><PERSON> thông tin của bạn trước 23:59 (giờ MT4/MT5).", "<PERSON><PERSON><PERSON> <PERSON>, ng<PERSON>y 12 tháng 12 năm 2024: <PERSON><PERSON><PERSON>i thưởng sẽ được phản ánh trong tài khoản MYFX Markets của bạn.", "<PERSON>ui lòng xem thêm thông tin trên <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/black-friday-2024/\"><PERSON><PERSON> <PERSON>ến mãi</a>."], "button": "<a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\"><PERSON> X ngay!</a>"}}, "cyberMonday": {"viewBtn": "<PERSON><PERSON> m<PERSON>i", "title": "Cyber Monday với chương trình khuyến mãi đặc biệt giới hạn thời gian!", "tag": "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại", "desc": " • <span style=\"font-weight: bold\">Theo：</span><PERSON> tà<PERSON> X ch<PERSON>h thức của MYFX Markets<br/>\n • <span style=\"font-weight: bold\">Chia sẻ：</span><PERSON><PERSON>ng báo giá cho những gì bạn muốn mua nhất<br/> • <span style=\"font-weight: bold\">Nhận giải thưởng：</span><PERSON><PERSON> cơ hội trúng thưởng 50 đô la!", "info": {"title": "<PERSON> tiết khu<PERSON>ến mãi <span style=\"font-family: 'Segoe UI Emoji'\">🛍</span>", "list": ["<PERSON> t<PERSON> X <PERSON><PERSON>h thức của MYFX Markets (trước đây là Twitter) <a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\" style=\"text-decoration: underline\">\"@MYFXMarketsEN\"</a>.", "Trích dẫn bài đăng quảng cáo có ý tưởng trang phục của bạn và hashtag \"#MYFXMarkets\".", "<PERSON><PERSON> hội trúng thưởng 50 đô la <PERSON> (hoặc loại tiền tệ tương đương)! Mười người chiến thắng may mắn sẽ được chọn!"], "desc": ["📅 Thời gian khu<PERSON>ến mại", "<PERSON><PERSON> ngày 30 tháng 11 năm 2024 (th<PERSON> bả<PERSON>) đến 23:59 (g<PERSON><PERSON> MT4/MT5) ngày 3 tháng 12 năm 2024 (thứ ba).", "⏳ Lịch trình phân phối giải thưởng", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 6 tháng 12 năm 2024: <PERSON><PERSON><PERSON><PERSON> chiến thắng sẽ được thông báo qua DM.", "<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON> 10 tháng 12 năm 2024: <PERSON><PERSON><PERSON> thông tin chi tiết củ<PERSON> bạn (giờ MT4/MT5) trước 23:59.", "<PERSON><PERSON><PERSON>, ng<PERSON><PERSON> 12 tháng 12 năm 2024: <PERSON><PERSON><PERSON><PERSON> thưởng sẽ được phản ánh trong tài khoản MYFX Markets của bạn.", "<PERSON>ui lòng xem thêm thông tin trên <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/cyber-monday-2024/\"><PERSON><PERSON> <PERSON>ến mãi</a>."], "button": "<a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\"><PERSON> X ngay!</a>"}}, "christmas": {"viewBtn": "<PERSON><PERSON> m<PERSON>i", "title": "<PERSON><PERSON><PERSON><PERSON>, tổ chức các hoạt động ưu đãi giới hạn thời gian!", "tag": "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại", "desc": " • <span style=\"font-weight: bold\">Đ<PERSON><PERSON> đăng ký：</span><PERSON><PERSON><PERSON><PERSON> các thông tin cần thiết vào mẫu đơn đăng ký trên trang sự kiện và gửi đi<br/>\n • <span style=\"font-weight: bold\">Nhận&Giao <PERSON>：</span>Nhận hơn 30.000 yên trong khoảng thời gian và giao dịch trên 5 lô<br/> • <span style=\"font-weight: bold\">Nhận quà：</span>Du lịch trong nước, v. v. rút thăm quà <PERSON>, 23 người tương đương!", "info": {"title": "🎄 N<PERSON>i dung sự kiện 🎁", "list": ["<PERSON><PERSON>ng ký sau khi nhập các vấn đề cần thiết vào mẫu đơn đăng ký \"Bước 1\" trên trang hoạt động.", "G<PERSON>i tối thiểu 30.000 yên trở lên trong thời hạn và giao dịch tối thiểu 5 lô trở lên.", "<PERSON><PERSON><PERSON> cứ vào số lần giao d<PERSON>, r<PERSON><PERSON> thăm quà <PERSON>h sang trọng tổng cộng 23 người!"], "desc": ["📅 Thời gian khu<PERSON>ến mại", "23:59, ng<PERSON><PERSON> 9 tháng 12 năm 2024 (<PERSON><PERSON><PERSON>) ～ ngày 27 tháng 12 năm 2024 (<PERSON><PERSON><PERSON>) ※ giờ MT4/MT5 áp dụng.", "⏳ Draw&Gift Send Schedule\"(bằng tiếng <PERSON>h)", "Thô<PERSON> báo cho những người được bầu bằng thư vào khoảng giữa tháng 1 năm 2025, xu<PERSON>t bản những người được bầu bằng tên viết tắt trên trang web", "<PERSON><PERSON><PERSON> quà cho những ng<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> bầu vào khoảng cuối tháng 1 năm 2025", "<PERSON>ui lòng xem thêm thông tin trên <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/christmas2024/\"><PERSON><PERSON> k<PERSON>ến mãi</a>."], "button": "<a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/christmas2024/\">Áp dụng ngay bây giờ!</a>"}}, "newYear2025": {"viewBtn": "<PERSON><PERSON> m<PERSON>i", "title": "Chào đón năm mới, chúng tôi đang tổ chức chương trình khuyến mãi đặc biệt trong thời gian có hạn!", "tag": "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại", "desc": " • <span style=\"font-weight: bold\"><PERSON><PERSON><PERSON> ký:</span> <PERSON><PERSON><PERSON><PERSON> thông tin cần thiết vào mẫu đăng ký trên trang khuyến mãi và gửi đi.<br/>\n • <span style=\"font-weight: bold\"><PERSON><PERSON>p tiền & Giao dịch:</span> Nạp tiền trên 5000円 yên và giao dịch trên 0.5 lot trong thời gian quy định.<br/> • <span style=\"font-weight: bold\">Nhận lì xì!</span> Nhận tiền hoàn lại theo số lot giao dịch!", "info": {"title": "<PERSON><PERSON>i dung chương trình khu<PERSON>ến mãi 🎉", "list": ["Điền thông tin cần thiết vào mẫu đăng ký trên trang «Bước 1» của chương trình khuyến mãi.", "<PERSON><PERSON><PERSON> tối thiểu 5000 yên hoặc 50 đô la và giao dịch tối thiểu 0.5 lot trong thời gian quy định.", "<PERSON>h<PERSON>n lì xì (tiền hoàn lại) cho <span style=\"font-weight: bold\">tất cả những người đủ điều kiện</span> dựa trên số lot giao dịch!"], "desc": ["📅 Thời gian chương trình khu<PERSON>ến mãi", "2 tháng 1 – 31 tháng 1 năm 2025 23:59 ※ Sử dụng đúng giờ MT4/MT5", "⏳ <PERSON><PERSON><PERSON> trình hoàn tiền", "<PERSON><PERSON><PERSON>ng gi<PERSON><PERSON> tháng 2 năm 2025 sẽ có tiền hoàn lại cho tài khoản tham gia.", "<PERSON> tiết có thể được xác nhận tại<a target=\"_blank\" href=\"https://myfxmarkets.com/ja/promotion/otoshidama2025/\">trang khuyến mãi</a>."], "button": "<a target=\"_blank\" href=\"https://myfxmarkets.com/ja/promotion/otoshidama2025/\"><PERSON><PERSON><PERSON> ký ngay!</a>"}}, "line1000": {"viewBtn": "<PERSON><PERSON> m<PERSON>i", "title": "Kỷ niệm vượt qua 1.000 bạn bè trên LINE, chúng tôi đang tổ chức một chiến dịch thưởng đặc biệt!", "tag": "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại", "desc": "<PERSON><PERSON> kỷ niệm số lượng bạn bè trên tài khoản LINE chính thức của chúng tôi vượt qua 1.000, chúng tôi sẽ tặng thưởng 3.000 yên cho khách hàng nạp tiền từ 10.000 yên trở lên trong thời gian diễn ra chiến dịch.", "info": {"title": "<PERSON><PERSON><PERSON> dung chiến dịch/<PERSON><PERSON><PERSON> tham gia", "list": ["<PERSON><PERSON> kỷ niệm số lượng bạn bè trên tài khoản LINE chính thức của chúng tôi vượt qua 1.000, chúng tôi sẽ tặng thưởng 3.000 yên cho khách hàng nạp tiền từ 10.000 yên trở lên trong thời gian diễn ra chiến dịch.", "【<PERSON><PERSON>ch tham gia】", "Đ<PERSON>ng nhập vào Client Office và gửi 10.000 yên trở lên.", "<PERSON><PERSON>i số đăng nhập của bạn qua LINE", "⚠️Lưu ý:", "Mỗi người chỉ được sử dụng một lần.", "Tiền thưởng sẽ hết hạn sau 30 ngày.", "<PERSON><PERSON> rút số tiền nhiều hơn số tiền đã gửi, bạn sẽ cần phải giữ tiền trong ít nhất 10 phút và giao dịch ít nhất 5 lần với tổng cộng 2 lô.", "Tiền thưởng giao dịch 3.000 yên sẽ không có hiệu lực khi chuyển hoặc rút tiền.", "Tà<PERSON> kho<PERSON><PERSON>, tà<PERSON> kho<PERSON>n MAM/PAMM và tài khoản Micro không đủ điều kiện tham gia chiến dịch này.", "<PERSON><PERSON>u số dư tài khoản của bạn trở thành số âm, tiền thưởng sẽ bị hủy khi hệ thống cắt giảm số không được áp dụng.", "Tr<PERSON><PERSON><PERSON> khi tham gia chiến dịch, vui lòng đảm bảo bạn đã đọc<a target=\"_blank\" href=\"https://myfxmarkets.com/wp-content/uploads/2025/03/%E3%80%90%E5%88%A9%E7%94%A8%E8%A6%8F%E7%B4%84%E3%80%91LINE%E3%81%8A%E5%8F%8B%E9%81%94%E7%99%BB%E9%8C%B21000%E4%BA%BA%E7%AA%81%E7%A0%B4%E8%A8%98%E5%BF%B5%E3%83%9C%E3%83%BC%E3%83%8A%E3%82%B9.pdf\"> Điều khoản sử dụng </a>của chúng tôi và xác nhận nội dung."], "desc": ["📅 <PERSON>h<PERSON>i gian chiến dịch", "<PERSON><PERSON> ngày 11 tháng 3 năm 2025 (<PERSON><PERSON><PERSON>) đế<PERSON> ngày 13 tháng 3 năm 2025 (<PERSON><PERSON><PERSON>) 23:59 (gi<PERSON> MT4/MT5)", "🔍 <PERSON><PERSON><PERSON> tượng", "<PERSON><PERSON><PERSON><PERSON> hàng đáp <PERSON>ng các điều kiện sau trong thời gian chiến dịch, dù là khách hàng mới hay hiện tại, đều đủ điều kiện.", "Phải có tài khoản giao dịch với MYFX Markets", "<PERSON>ải đượ<PERSON> đăng ký là bạn bè trên tài khoản LINE ch<PERSON>h thức của MYFX Markets", "【Thông tin tài khoản LINE ch<PERSON>h thức của MYFX Markets】", "LINE ID: @myfxmarkets", "<PERSON><PERSON><PERSON> kết thêm bạn bè: <a href=\"https://lin.ee/TMuvgP5X\" target=\"_blank\">https://lin.ee/TMuvgP5X</a>", "※ Bạn cũng có thể đăng ký làm bạn qua mã QR."], "button": "<a target=\"_blank\" href=\"https://myfxmarkets.com\">Đ<PERSON>ng ký ngay!</a>"}}, "cashback2025May": {"title": "<PERSON>ến dịch T<PERSON>n Mặt <PERSON><PERSON><PERSON> - <PERSON>hận lên đến $3000!", "desc": "Chúng tôi rất vui mừng thông báo về chiến dịch Tiền Mặt Mùa Hè!<br>Trong thời gian diễn ra chiến dịch, bạn sẽ nhận được phần thưởng khi giao dịch (không bao gồm cặp USD/JPY và Vàng) trên tài khoản MT5 Standard hoặc Pro của chúng tôi.<br>Nhận phần thưởng hoàn tiền lên đến $3,000, dựa trên khối lượng giao dịch của bạn.", "info": {"desc": ["📅 <PERSON>h<PERSON>i gian chiến dịch", "Ngày 12 tháng 5 năm 2025 lúc 9:00 sáng – Ngày 4 tháng 7 năm 2025 lúc 11:59 tối", "*Thời gian máy chủ MT4/MT5 áp dụng", "🔍 Tài khoản đủ điều kiện", "Tà<PERSON> k<PERSON>n MT5 Ti<PERSON><PERSON> chuẩn và <PERSON>yên nghiệp", "<span style=\"color: red\">*<PERSON><PERSON><PERSON>ho<PERSON>n MT4, <PERSON><PERSON><PERSON> khoản Micro, <PERSON>à<PERSON> khoản Do<PERSON>h nghiệp và Tài khoản MAM/PAMM không đủ điều kiện.</span>", "📈 <PERSON><PERSON><PERSON> dịch đủ điều kiện", "FX không bao gồm USD/JPY và kim loại quý không bao gồm Vàng.", "*Chỉ các giao dịch duy trì vị thế mở trong <span style=\"color: red\">ít nhất 10 phút</span> sẽ được coi là đủ điều kiện.", "💵 Số tiền gửi tối thiểu", "<span style=\"color: red\">Cần có số tiền gửi tối thiểu là $300 USD/$300 AUD để tham gia chiến dịch này.</span>", "*<PERSON>ác giao dịch chỉ đủ điều kiện cho chiến dịch này khi số tiền gửi đã được ghi có thành công.", "👩 Người tham gia đủ điều kiện 🧑", "<PERSON><PERSON><PERSON><PERSON> hàng hiện tại gửi tiền và giao dịch bằng tài khoản MT5 Tiêu chuẩn hoặc <PERSON><PERSON>ên nghiệp trong thời gian chiến dịch.", "<PERSON><PERSON><PERSON><PERSON> hàng mới mở tài khoản MT5 Ti<PERSON><PERSON> chuẩn hoặc <PERSON><PERSON><PERSON><PERSON> nghiệ<PERSON>, gửi tiền và thực hiện giao dịch trong thời gian chiến dịch.", "✍️ <PERSON><PERSON>ch tham gia", "1) <PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> \"Tham gia ngay\" bên dưới để tham gia chiến dịch.", "2) <PERSON><PERSON><PERSON> tiền ít nhất $300 USD/$300 AUD.", "3) <PERSON><PERSON>t đầu giao dịch!", "*Xin lưu ý rằng chiến dịch này chỉ áp dụng cho tài khoản MT5 Tiêu chuẩn và MT5 Chuyên nghiệp. Ngay cả khi bạn đã có tài khoản với chúng tôi, bạn sẽ cần mở một tài khoản MT5 bổ sung nếu bạn hiện không có một tài khoản nào.", "⚠️Các quy tắc và điều khoản chiến dịch", "Chỉ những tài khoản đáp ứng tất cả các điều kiện sau trong thời gian chiến dịch mới được coi là đủ điều kiện:", "<PERSON><PERSON><PERSON> đơn đ<PERSON>ng ký ch<PERSON>h thức.", "Số tiền gửi ít nhất là $300 USD/$300 AUD.", "Thực hiện các giao dịch mới tổng cộng ít nhất 1 lot trong các cặp tiền tệ đủ điều kiện sau khi số tiền gửi đã được ghi có.", "Chuyển khoản nội bộ từ các tài khoản khác sẽ không được coi là số tiền gửi đủ điều kiện.", "Tổng khối lượng giao dịch sẽ được tính trên cơ sở từng tài kho<PERSON>n, không phải theo từng cá nhân.", "Tiền hoàn lại nhận được theo chiến dịch này không thể được chuyển giữa các tài khoản hoặc rút trong vòng 30 ngày kể từ ngày nhận.", "<PERSON><PERSON><PERSON> vị thế mở vào cuối thời gian chiến dịch sẽ không được tính vào tổng khối lượng giao dịch.", "<PERSON><PERSON>n dịch này không thể kết hợp với bất kỳ chương trình khuyến mãi nào khác đang diễn ra.", "<PERSON>n vui lòng đọc và xác nh<PERSON> <a style=\"text-decoration: underline;\" href=\"https://myfxmarkets.com/wp-content/uploads/2025/05/Terms-and-Conditions-MYFX-Markets-Summer-Cash-Frenzy-Campaign_2.pdf\" target=\"_blank\"><PERSON><PERSON><PERSON><PERSON> sử dụng của MYFX Markets</a> trư<PERSON>c khi tham gia.", "※<PERSON><PERSON> biết thêm chi tiết bao gồm danh sách số tiền hoàn lại, vui lòng truy cập <a style=\"text-decoration: underline;\" href=\"https://myfxmarkets.com/promotion/summer-cash-frenzy2025/\" target=\"_blank\">trang khuyến mãi</a> trên trang web ch<PERSON>h thức của chúng tôi."], "joinBtn": "Tham gia ngay!", "joinHit": "<PERSON><PERSON><PERSON> k<PERSON>n này đã tham gia", "agreeContent": "T<PERSON><PERSON> đồng ý với<a target=\"_blank\" href=\"https://myfxmarkets.com/wp-content/uploads/2025/05/Terms-and-Conditions-MYFX-Markets-Summer-Cash-Frenzy-Campaign.pdf\">các điều khoản và điều kiện</a>.", "hint1": "*Xin lưu ý rằng chiến dịch này chỉ áp dụng cho tài khoản MT5 Standard và MT5 Pro. Ngay cả khi bạn đã có tài khoản với chúng tôi, bạn sẽ cần mở một tài khoản MT5 bổ sung nếu bạn hiện không có tài khoản nào.", "hint2": "*Khối lượng giao dịch tổng cộng sẽ được tính trên cơ sở từng tài k<PERSON>, không phải từng cá nhân.", "hint3": "*<PERSON><PERSON><PERSON> tài khoản hiện đang có tiền thưởng không đủ điều kiện tham gia chiến dịch này.", "account": "S<PERSON> tài k<PERSON>n giao d<PERSON>ch"}, "selectAll": "<PERSON><PERSON><PERSON> tất cả", "selectLabel": "<PERSON><PERSON><PERSON> khoản đủ điều kiện (Eligible Accounts for the campaign)", "accountEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> có tài khoản nào để chọn"}}, "PromotionInfo": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi của tôi", "activityTime": "Ưu đãi sẽ kết thúc sau <span style=\"color: red;\">{day} ngày</span>", "totalLots": "Tổng số lô", "promotionPeriod": "<PERSON><PERSON><PERSON><PERSON> gian k<PERSON>n mãi", "today": "<PERSON><PERSON><PERSON> nay", "howToGetCashback": "<PERSON><PERSON><PERSON> nh<PERSON>n hoàn tiền", "rule01": "Ti<PERSON>n gửi tối thiểu $300", "rule02": "<PERSON><PERSON><PERSON><PERSON> 3 lô", "rule03": "<PERSON><PERSON><PERSON> hoặc kim loại quý", "ruleMore": "<PERSON><PERSON> biết thêm chi tiết, vui lòng truy cập {0}", "ruleMoreLink": "trang k<PERSON><PERSON>n mãi", "lots": "<PERSON><PERSON><PERSON>", "cashback": "<PERSON><PERSON><PERSON> lại tiền", "lostList": "<PERSON><PERSON><PERSON> {count} lô", "cashbackList": "${count}(hoặc đơn vị tiền tệ tương đương)", "tradingAccount": "<PERSON><PERSON><PERSON>n giao dịch của bạn:"}, "promotionInfo2": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi của tôi", "tips": {"title": "<PERSON><PERSON><PERSON> thế nào để có được vé may mắn", "content1": "Chia sẻ và thích bài đăng quảng cáo trên Instagram Stories của bạn", "content2": "Chia sẻ và thích bài đăng quảng cáo trên Facebook", "content3": "<PERSON><PERSON> sẻ trên X (Twi<PERSON>)", "content4": "Chia sẻ và like bài viết quảng cáo trên X", "content5": "Giao dịch 2 lô", "content6": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i $100", "more": "<PERSON><PERSON> biết thêm chi tiết, vui lòng truy cập ", "link": "<PERSON><PERSON> qu<PERSON>ng cáo"}, "endDay": "Ưu đãi sẽ kết thúc sau <span style=\"color: red; font-weight: 700\">{day} ngày</span>", "tickets": "B<PERSON>y giờ bạn có <span style=\"color: red\">{tickets}</span> Vé vào cửa!", "totalLots": "Tổng số lô", "period": "<PERSON><PERSON><PERSON><PERSON> gian k<PERSON>n mãi", "today": "<PERSON><PERSON><PERSON> nay", "shareInstagam": "<PERSON>a sẻ trên In<PERSON>gram", "shareFacebook": "Chia sẻ trên Facebook", "shareTwiter": "Chia sẻ trên X", "deposit": "<PERSON><PERSON><PERSON> t<PERSON>n"}, "becomeIBOnline": {"title": "Tham gia MYFX Markets với tư cách là đối tác giới thiệu", "desc1": "Quy trình đăng ký liền mạch của chúng tôi sẽ cung cấp cho bạn một liên kết giới thiệu duy nhất và hệ thống báo cáo toàn diện của chúng tôi sẽ giúp bạn chuyển đổi nhiều khách hàng hơn và theo dõi doanh thu của bạn.", "desc2": "<PERSON><PERSON><PERSON><PERSON>, vui lòng liên hệ với <a href=\"https://static.zdassets.com/web_widget/latest/liveChat.html?v=10#key=myfxmarkets.zendesk.com/&lang=en\" target=\"_blank\" style=\"color: #495eeb\">24/7 </a>Hỗ trợ khách hàng", "desc3": "<PERSON>ây dựng mối quan hệ đối tác hữu ích với MYFX Markets ngay bây giờ!", "checkBtn": "<PERSON><PERSON><PERSON> tra email", "placeholder": "Email CO của chúng tôi", "validate1": "<PERSON><PERSON> không thể để trống", "validate2": "<PERSON><PERSON> lòng nhập đúng định dạng email"}, "profile": {"accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "director": "<PERSON><PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>ng ty", "individual": "Cá nhân", "personalInformation": "Thông tin cá nhân", "dateOfBirth": "<PERSON><PERSON><PERSON>", "countryOfResidence": "<PERSON><PERSON><PERSON>c gia cư trú", "residentialAddress": "Địa chỉ cư trú", "contactInformation": "Thông tin liên lạc", "email": "E-mail", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "identification": "<PERSON><PERSON><PERSON><PERSON> dạng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "verified": "Đ<PERSON> x<PERSON>c <PERSON>h", "pendingVerification": "<PERSON><PERSON> chờ x<PERSON>c <PERSON>h", "hint": "*<PERSON><PERSON> lòng liên hệ với chúng tôi qua địa chỉ email đã đăng ký của bạn để yêu cầu thay đổi thông tin đăng ký.<br/><a href=\"mailto:<EMAIL>\"><EMAIL></a>"}, "tradingTools": {"analysisIQ": {"subscribe": "<PERSON><PERSON><PERSON> ký"}, "download": {"socialLinks": "<PERSON><PERSON><PERSON> kết xã hội", "userGuide": "Hướng dẫn sử dụng", "downloadText": "<PERSON><PERSON>i xuống PDF để xem", "notice": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>y", "riskWarning": {"title": "<PERSON><PERSON><PERSON> báo rủi ro：", "list": ["<PERSON><PERSON><PERSON> tín hiệu thị trường trên trang này chỉ được cung cấp cho mục đích thông tin và không nên được coi là lời khuyên hoặc khuyến nghị từ MYFX Markets.", "<PERSON><PERSON><PERSON> ý tưởng giao dịch được cung cấp bởi Acuity Research Limited, một công ty được FCA ủy quyền và quản lý (Số công ty: 07428345).", "Bất kỳ ai chọn sử dụng Tín hiệu Giao dịch Acuity đều phải chịu hoàn toàn trách nhiệm về hành động của mình. Chúng tôi khuyến nghị mạnh mẽ việc thực hiện thẩm định kỹ lưỡng đối với bất kỳ thông tin nào thu được từ trang này trước khi tham gia vào các hoạt động giao dịch."]}}, "webinars": {"title": "<PERSON><PERSON><PERSON> thảo trên web", "desc1": "Mỗi nhà bình luận đối tác của chúng tôi đều có hồ sơ công khai đã được chứng minh trong ngành giao dịch thị trường.", "desc2": "Thông qua quá trình lựa chọn kỹ lưỡng, chúng tôi tự hào giới thiệu một nhóm các chuyên gia thị trường để hỗ trợ hành trình giao dịch của bạn, làm sâu sắc thêm kiến thức thị trường của bạn và cung cấp giá trị và thông tin chi tiết không có ở bất kỳ nơi nào khác!", "placeholder": {"desc1": "<PERSON><PERSON><PERSON> bình luận mới", "desc2": "Tham gia với chúng tôi sớm"}, "registerNow": "<PERSON><PERSON><PERSON> ký ngay", "joined": "Đ<PERSON> đăng ký", "joinHint": "Hướng dẫn", "joinMessage": "<div><PERSON><PERSON><PERSON> ký thành công！</div><div><a href=\"{webinarUrl}\" target=\"_blank\">{webinarUrl}</a></div>"}}}