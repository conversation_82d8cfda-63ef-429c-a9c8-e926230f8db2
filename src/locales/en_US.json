{"sidebar": {"profile": "Profile", "topic": "Topic", "dashboard": "Dashboard", "dashboard1": "Dashboard 1", "dashboard2": "Dashboard 2", "videoChat": "Video Chat", "app": "App", "eCommerce": "E-Commerce", "productListing": "Product Listing", "productDetail": "Product Detail", "checkout": "Checkout", "wishlist": "Wishlist", "socialApp": "Social App", "todo": "Todo", "projectManagement": "Project Management", "email": "Email", "inbox": "Inbox", "emailCompose": "Email Compose", "calendar": "Calendar", "user": "User", "userProfile": "User Profile", "userEdit": "User Edit", "userAdd": "User Add", "userList": "User List", "components": "Components", "uiElements": "UI Elements", "color": "Color", "typography": "Typography", "alert": "<PERSON><PERSON>", "badges": "Badges", "breadcrumb": "<PERSON><PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON>", "cards": "Cards", "carousel": "Carousel", "video": "Video", "grid": "Grid", "images": "Images", "listGroup": "List Group", "media": "Media", "modal": "Modal", "notifications": "Notifications", "pagination": "Pagination", "popovers": "Popovers", "progressBars": "Progress Bars", "tabs": "Tabs", "tooltips": "Tooltips", "forms": "Forms", "formElements": "Form Elements", "formValidation": "Form Validation", "formSwitch": "Form Switch", "formRadio": "Form Radio", "formCheckbox": "Form Checkbox", "table": "Table", "basicTable": "Basic Table", "dataTable": "Data Table", "editable": "Editable", "charts": "Charts", "highChart": "High Chart", "amChart": "AM Chart", "apexChart": "Apex Chart", "icons": "Icons", "dripicons": "Dripicons Icon", "fontAwsome5": "Font Awsome 5", "lineAwsomeIcon": "lineAwsomeIcon", "rimixIcon": "Remixicon", "unicon": "Unicon", "treeView": "Tree View", "pages": "Pages", "authentication": "Authentication", "login": "<PERSON><PERSON>", "register": "Register", "recoverPassword": "Recover Password", "confirmMail": "Confirm Mail", "lockScreen": "Lock Screen", "maps": "Maps", "googleMap": "Google Map", "extraPages": "Extra Pages", "timeline": "Timeline", "invoice": "Invoice", "blankPage": "<PERSON><PERSON> <PERSON>", "error404": "Error 404", "error500": "Error 500", "pricing": "Pricing", "pricing1": "pricing 1", "maintenance": "Maintenance", "comingSoon": "Coming Soon", "faq": "FAQ", "plugins": "Plugins", "datepicker": "Datepicker", "select": "Select", "draggable": "Draggable", "MenuDesign": "Menu Design", "horizontalMenu": "<PERSON><PERSON>u", "horizontalTopMenu": "Horizontal Top Menu", "twoSlider": "Two Slider", "verticalBlockMenu": "Vertical Block Menu", "menuLevel": "Menu Level", "menuOne": "Menu 1", "menuTwo": "Menu 2", "menuThree": "Menu 3", "menuFour": "Menu 4", "submenu": "Sub-menu", "submenuOne": "Sub-menu 1", "submenuTwo": "Sub-menu 2", "submenuThree": "Sub-menu 3", "myAccount": "Accounts", "accountInformation": "Account Information", "myDocument": "My Documents", "accountTransactions": "Payments", "card": "MyCard", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "Withdraw", "internalFundTransfer": "Internal Fund Transfer", "transactionHistory": "Transaction History", "vault": "<PERSON><PERSON>", "accountSettings": "Account <PERSON><PERSON>", "resetCoPassword": "Reset CO Password", "openAdditionalAccount": "Open An Additional Account", "changeLeverage": "Change Leverage", "platform": "Platform", "mt4Download": "MT4 Download", "mt5Download": "MT5 Download", "webTrader": "Web Trader", "ibPortal": "IB Portal", "siteNewsComponent": "News", "siteNews": "Site News", "promotion": "Promotions", "campaign": "Campaign", "ex-DividendList": "EX-Dividend List", "contactUs": "Contact Us", "emailUs": "Email Us", "liveChat": "Live Chat", "officialWebSite": "Official Website", "backToOldVersion": "Back To Old Version", "name": "Name", "phone": "Phone", "next": "Next", "next1": "Next", "next2": "Next", "submit": "Submit", "back": "Back", "ok": "OK", "cancel": "Cancel", "return": "Return", "add": "Add", "add_account": "Add Account", "confirm": "Confirm", "confirm1": "Confirm", "confirm2": "Confirm", "address": "address", "link": "here", "success": "We have successfully received your application", "error": "Oops.something went wrong", "empty": "No data", "back_home_page": "Back to home page", "ArchiveTradeHistory": "Archive Trade History", "partnerPortal": "Partner Portal", "headerBtn": "DEPOSIT", "tradingTools": "Trading Tools", "acuityTools": "Acuity Tools", "analysisIQ": "AnalysisIQ", "economicCalendar": "Economic Calendar", "downloadsAndLinks": "Downloads & Social Links"}, "account_information": {"show_hidden_account": "Show Hidden Account", "show_visible_account": "Show Visible Account", "floating": "Floating Pnl", "mt4login": "MT4 Login", "mt5login": "MT5 Login", "account_type": "Account Type", "leverage": "Leverage", "currency": "<PERSON><PERSON><PERSON><PERSON>", "hide_account": "Hide Account", "show_account": "Show Account", "balance_floating_pnl": "Balance & Floating PNL", "reset_mt_password": "Reset MT Password", "withdrawal": "Withdraw", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "empty": "All accounts are currently hidden from this list.", "exit": "Exit from Strategy", "recover": "Recovery Request", "join": "Join Again", "beingProcessed": "Being Processed", "recoveryBeingProcessed": "Recovery Being Processed", "joinBeingProcessed": "Re-registration in progress", "exitMessage": "Withdraw from", "recoverMessage": "Would you like to recover", "dialog": {"title": "Reset {type} trading Account password -", "question1": "Enter a secret question answer.", "question2": "Set the new {type} login password.", "next": "Next", "success": "Change password has been completed", "close": "Close"}, "msgBox": {"sure": "OK", "cancel": "Cancel"}, "recoveryAccountSuccessMessage": "The dormant account is restored and a letter is sent.", "Table": {"column_01": "<PERSON><PERSON>", "column_02": "Account Name", "column_03": "Leverage", "column_04": "<PERSON><PERSON><PERSON><PERSON>", "column_05": "Status", "column_06": "Balance", "column_07": "FloatingPnl", "column_08": "Account type", "column_05_01": "Balance/FloatingPnl", "column_05_02": "View All", "column_05_03": "View Balance / FloatingPnl", "viewAll": "Balance ∙ Floating Profit and Loss View", "accountType": {"Standard": "Standard", "MAM Slave": "MAM Slave", "PAMM Slave": "PAMM Slave", "Affiliate Rebate": "Affiliate Rebate", "Pro": "Pro", "Crypto": "Crypto", "Rebate": "Rebate", "PAMM": "PAMM", "PAMM Master": "PAMM Master", "competition": "competition", "MAM Master": "MAM Master", "st_mt5": "st_mt5", "contest2023jp": "contest2023jp", "mt4_micro_standard": "MT4 Standard Micro Account", "DEMO": "DEMO", "pro_mt5": "pro_mt5", "Copy Trade": "Copy Trade"}}, "Status": {"status_1": "Active", "status_0": "<PERSON><PERSON><PERSON>", "status_-1": "Disabled", "status_-2": "Delete", "status_-3": "Archived", "status_-4": "Archive and delete", "status_-5": "Restoring MT4 Account"}}, "mydocument": {"verifyTip1": "<span style=\"font-weight: bold; color: #000000\">Not Verified:</span> Please upload the following documents.", "verifyTip2": "Identification Verified", "title": "In order to complete your account application, please upload the following documents", "attention": "Attention:", "test1": "1. Allowed file types: PDF, JPG, JPEG, PNG, BMP, GIF ", "test2": "2. Maximum upload file size: 5MB", "individual": {"load1": {"title1": "1. Identification", "title2": "Government-Issued Photo Identification Documents:", "test1": "1. Driver's License", "test2": "2. Pass<PERSON>", "test3": "3. ID card", "test4": "(Note: Student ID cards and health insurance cards are not accepted as valid forms of identification.)"}, "load2": {"title1": "2. <PERSON><PERSON> Of Address", "title2": "Identification Documents Showing Your Name, Current Address, and Date of Issue:", "test1": "1. Utility Bills and Residence Certificates (issued within three months)", "test2": "2. Mobile Phone Bills (issued within 3 months)", "test3": "3. General Mail Correspondence", "test4": "(Note: Health insurance cards are not eligible for verification.)", "test5": "*The copy of proof of address such as bank statemenis or utity bills should be issued within 3 months."}}, "company": {"load1": {"title1": "1. Company Details", "title2": "Please ensure that the uploaded documents include:", "test1": "1. Certificate of Incorporation", "test2": "2. Proof of Operating Office Address* (original utility bill or bank statement displaying the Company's Name and Operating Office Address dated within the last three months)", "test3": "3. Co<PERSON> of Register of Directors (If applicable)", "test4": "4. <PERSON><PERSON> of Register of Shareholders (If appricable)"}, "load2": {"title1": "2. Director <PERSON><PERSON>", "title2": "Please ensure that the uploaded documents include:", "test1": "1. Copy ID of Register of Directors", "test2": "2. Director's Proof Address (Bank statement or utility bill with issued date within 3 months, address and your name)"}, "load3": {"title1": "3. Shareholder Details", "title2": "Please ensure that the uploaded documents include:", "test1": "1. Copy ID of Register of Shareholder (If applicable)", "test2": "2. Shareholder's Proof of Address (If applicable)"}}, "test3": "Drag the file here, or", "click_upload": "click Upload", "file_list": "File List", "update_button": "Upload", "table": {"title": "Uploaded Documents", "comment": "Comment", "create_time": "Upload Time", "file_name": "File Name", "status": "Status", "statusList": {"Approved": "Approved", "Pending": "Pending", "Approval Failed": "Approval Failed", "Removed": "Removed"}, "typeList": {"1": "Identification", "2": "Proof 0f Address", "3": "Company Docs", "4": "Director Docs", "5": "Shareholder Docs"}, "uploadSuccess": "Upload successful"}, "deleteDesc": {"operation": "OPERATION", "buttonText": "Delete", "confirmTip": "This operation will permanently delete the file. Do you want to continue?", "confirmTitle": "Hint", "confirmButtonText": "OK", "cancelButtonText": "Cancel", "deleteSuccess": "Delete successful"}}, "reset_password": {"title": "Reset CO Password", "step1": "Step One", "step2": "Step Two", "step3": "Step Three", "description1": "Step One: Enter Current Password", "description2": "Step Two: Answer Security Questions", "description3": "Step Three: Enter New Password", "label1": "Current Password", "label2": "Security Question", "label3": "Answer", "label4": "New Password", "label5": "New Password Confirm", "label6": "Verification Code", "eight_character": "Eight Character", "one_number": "One Number", "one_lowerCase": "One LowerCase", "one_upperCase": "One UpperCase"}, "vault": {"title1": "<PERSON><PERSON>", "title2": "Vault Information", "test": "Your Vault is based on your MT4 Managed Account Currency.", "button1": "Withdraw", "button2": "Transfer", "table": {"title": "Vault Balance History", "date": "Date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "type": "Type", "comment": "Comment"}, "tips": "Tips", "tips_test1": "Due to AML/CTF laws, MYFX Markets cannot deposit funds to third parties. All money withdrawn from your account must go to an account in the same name as your MYFX Markets trading account.", "tips_test2": "Withdrawal process will take 1-3 days, for requests made on weekends, there may be a delay in processing until the following business day.", "tips_test3": "There may be separately fees charged by processing companies", "tips_test4": "Maximum withdrawal amount through bitwallet is 20,000 USD", "tips_test5": "", "tips_test6": "", "typeList": {"TRANSFER_FROM_MAMPAMM_SLAVE": "TRANSFER_FROM_MAMPAMM_SLAVE", "WITHDRAWAL": "WITHDRAWAL", "TRANSFER": "TRANSFER", "manual_withdrawal_b_07122020": "manual_withdrawal_b_07122020", "ADJUSTMENT": "ADJUSTMENT", "REFUND BACK FROM BANK": "REFUND BACK FROM BANK", "Transfer Back": "Transfer Back", "Bank transfer failed": "Bank transfer failed", "Bank Refund": "Bank Refund", "TRANSFER_BACK": "TRANSFER_BACK", "TRANSFER_BACK_FROM_BANK": "TRANSFER_BACK_FROM_BANK", "FUNDS BACK FROM BANK": "FUNDS BACK FROM BANK"}}, "oata": {"title": "Open Additional Trading Account", "label1": "Server", "label2": "Account Type", "label3": "<PERSON><PERSON><PERSON><PERSON>", "label4": "IB Code", "label5": "Comments", "label6": "Leverage", "instruction": "*The leverage will be the same as your existing accounts.", "redioLabel1": "Standard", "redioLabel2": "Pro", "placeholder": "If applicable", "currency_check": "Currency is not valid", "title2": "Success", "title3": "Confirmation Document Approval Process Incomplete", "openSuccessTitle": "Success", "sub_title": "Please back to the first page", "test1": "By Checking this box, I agree to the terms and conditions of MYFX Markets", "test2": "MYFX Markets Terms and Conditions", "test_2_2": "MYFX Markets Disclosure Statement", "test3": "Please proceed with the registration process by uploading the necessary documents using the link below:", "test4": "Upload your document", "test5": "If you have already uploaded your documents, please wait for a while until the process is completed. Customers who need to resubmit documents will be notified via <NAME_EMAIL>.", "test6": "<EMAIL>", "ibCodeCheck": {"noData": "This is a required field", "isNumber": "Please enter letters or numbers"}}, "platform": {"button_test1": " MT4 Download", "button_test2": " MT5 Download", "button_test3": " Web Trader", "wd_button_test1": " MT4 ", "wd_button_test2": " MT5 ", "title1": "Operating System", "title2": "Mobile", "trader_test": "If the browser does not respond, please click", "trader_test2": "", "desktop_laptop": "Desktop / Laptop", "smartphone_tablet": "Smartphone / Tablet", "for_windows": "For Windows", "for_mac": "For Mac", "for_android": "For Android", "for_IOS": "For iOS", "type": {"MT4 Download": "MT4 Download", "MT5 Download": "MT5 Download"}, "hint_01": "The MT4/MT5 terminal is automatically updated once it is installed.", "hint_02": "No need to reinstall.", "desktop": "Desktop / Laptop", "mobile": "Smartphones / Tablets", "download": "Download", "open": "Open", "goto": "Here"}, "news": {"button_test1": "News", "button_test2": "Promotion", "button_test3": "Ex-Dividend"}, "emailus": {"title": "Please use the following form to send us your questions, concerns, or comments", "label1": "Name", "label2": "Email", "label3": "Phone Number", "label4": "Account Number", "label5": "Comments", "test1": "We value your input and appreciate your feedback.", "test2": "We will respond to your messages in a timely manner.", "tips": "Tips", "tips_test": "Your feedback is important to us, and we highly appreciate your input. Rest assured, we will promptly respond to your messages.", "success": "Thank you for your inquiry. As soon as we confirm the contents, we will reply from the person in charge.", "qrcodeTitle": "You can also contact us via LINE.", "topic": {"select": ["General Support", "Sales Inquires", "Account Services", "Partner Programs", "Media Inquires", "Compliance"]}, "vuePhoneNumberInput": {"countrySelectorLabel": "Country code", "countrySelectorError": "Choose country", "phoneNumberLabel": "Phone Number", "example": "Example"}}, "transaction": {"button_test1": "<PERSON><PERSON><PERSON><PERSON>", "button_test2": "Withdraw", "button_test3": "Transfer", "button_test4": "History", "button_test5": "Archive", "Archive": {"account": "Please select an account", "startDate": "start date", "endDate": "end date", "search": "search", "date": "Date", "handle": "Operation", "download": "download", "hint_account": "Please select an account", "hint_date": "Please select a date", "hint_no_data": "No data at this time!"}, "deposit": {"newYearTips": "<p style=\"margin: 4px;\">&lt;During the Year-End and New Year Holiday&gt;</p><p style=\"margin: 4px;\">\n・For international bank transfers and cryptocurrency deposits made on December 25 (Wednesday), December 26 (Thursday), and January 1 (Wednesday), funds will be reflected in your account on the next business day.</p><p style=\"margin: 4px;\">・Deposits made via other methods may also be processed on the next business day or later. Thank you for your understanding.</p>", "status": {"available": "Available", "maintenance": "Under Maintenance"}, "AllForm": {"account": "Account", "accountPlaceholder": "Please select an account", "accountError_01": "Please select an account", "amount": "Amount", "amountPlaceholder": "Please enter amount", "amountError_01": "Please enter amount", "amountError_02": "Must not be less than the minimum deposit amount", "amountError_03": "Do not exceed the maximum deposit amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "payType": "Payment Type", "payTypePlaceholder": "Please select payment type", "payTypeError_01": "Please select payment type", "cryptoCompanyName": "Cryptocurrency Company", "cryptoCompanyNamePlaceholder": "Please enter the virtual currency company", "cryptoCompanyNameError_01": "Please enter the virtual currency company", "cryptoCompanyAddress": "Wallet Address", "cryptoCompanyAddressPlaceholder": "Please enter your wallet address", "cryptoCompanyAddressError_01": "Please enter your wallet address", "cryptoAmountHint": "If you wish to deposit an amount exceeding the maximum limit, we will provide you with a separate wallet address. Please contact our Customer Support team via email for assistance.", "chain": "Blockchain", "chainPlaceholder": "Please select blockchain", "chainError_01": "Please select blockchain", "fullKatakana": "Sender's Full Name (full-width Katakana)", "fullKatakanaPlaceholder": "Please enter the depositor's name", "fullKatakanaError_01": "Please enter the depositor's name (full-width Kat<PERSON><PERSON>)", "name": "Name", "namePlaceholder": "Please enter your Chinese name", "namePlaceholder1": "Please enter your name", "nameError_01": "Please enter your Chinese name", "phone": "Phone number", "phonePlaceholder": "Please enter phone number", "phoneError_01": "Please enter phone number", "depositCurrency": "De<PERSON><PERSON><PERSON>", "depositCurrencyPlaceholder": "Please select the deposit currency", "depositCurrencyError_01": "Please select the deposit currency", "paymentMethod": "Payment method", "paymentMethodPlaceholder": "Please select payment method", "paymentMethodError_01": "Please select payment method", "1": "Bank Card", "5": "QR Code"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "table": {"processed_date_time": "Processed Date/Time", "transaction_type": "Transaction Type", "tra_type": {"Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Withdraw": "Withdraw"}, "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "account_number": "Account Number", "payment_type": "Payment Type", "status": "Status", "sta_type": {"CANCELLED": "Cancelled", "COMPLETED": "Completed", "PENDING": "Pending", "PROCESSING": "Processing", "MAMPAMM": "MAM/PAMM", "PROCESSED": "Processed", "FAILED": "Failed"}, "account_type_text": "<PERSON><PERSON>"}, "tips": {"title": "Tips", "test1": "To expedite the process, please ensure all information is accurate and complete. Deposits are accepted in AUD, EUR, GBP, JPY and USD.", "test2": "To comply with AML/CTF regulations, MYFX Markets can only accept funds from your bank account or credit card that matches the name on your MYFX Markets trading account.", "test3": "Third-party deposits are not permissible.", "test4": "The withdrawal method is the same as the deposit method.", "test5": "Manual of deposit and withdrawal we have a manual on deposit and withdrawal. Download it here.", "test6": "Download the deposit and withdrawal manual", "test7": "If you have a MAM/PAMM account, please download it from here.", "test8": "Download deposit and withdrawal manual (for MAM/PAMM).", "test9": "Due to the mechanism of the cryptocurrency, it may take up to 24 hours to complete the transaction, so we recommend depositing with a margin of time.", "test10": "Deposit via cryptocurrency and the profit will be withdrawn to the same wallet.", "test11": "The reflection may be delayed due to system failure, It is advisable to allocate adequate time for the advance payment process.", "test12": "", "test_12_01": "If you are using a VISA or MASTER card, you will need to deposit money into your bitwallet account and then deposit it into your account from the client office.", "test_12_02": "If you send us a screenshot of the fee after you make the payment to your account, we will pay the fee.", "test_12_03": "For CARDS other than VISA and MASTER, you can deposit bitwallet from the client office as usual. The fee will be paid automatically.", "test13": "After clicking the \"confirm\" button, do not press the \"back\" button of the browser. You will be automatically redirected to the bitwallet payment process page, and then you will be redirected back to the client office page.", "test14": "The reflection of this service may be delayed due to the timing of the interbank remittance, etc.", "test15": "The bank account details are issued each time you apply for a deposit and cannot be used for the next transfer. Please apply for deposit every time.", "test16": "If the transfer amount is 500,000 yen or more, please enter the request number in front of the name of the person requesting the transfer at the time of the deposit procedure.", "test17": "You will be responsible for any bank transfer fees.", "test18": "If the trading account currency is not JPY, the transfer amount converted to Japanese yen will be displayed on the next page.", "test19": "If the remittance currency is different from the currency in your MT4/MT5 account, funds will be converted when received and then credited to your MT4/MT5 account.", "test20": "Please ensure to include your MT4/MT5 account number in the remarks field.", "test21": "Please be aware that bank transfer fees will be borne by you.", "test22": "Please be aware that international wire transfers may result in additional fees if an intermediary bank is utilized. We recommend contacting your bank to learn about any potential intermediary bank charges. "}, "step1": "step 1", "step2": "step 2", "step3": "step 3", "description1": "Step One: Select A Payment Type", "description2": "Step Two: Enter Your Deposit Form", "description3": "Step Three: Ensure The Deposit Information", "depositWayColumns": {"label1": "Deposit Methods", "label2": "Status", "label3": "Time to Fund", "label4": "Fees", "label5": "Operation"}, "jpbank": {"depositMethods": "JPY Local Bank", "status": "Available", "timeToFund": "30 Mins", "fee": "Bank Fee Apply", "label1": "Select Account", "label2": "Amount", "label3": "Sender Name(Full width KATAKANA)", "label4": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "label5": "Trading Account Number", "title": "Here is a Bank detail for JPY Only.", "title2": "Attention:", "test": "Currency:", "test1": "Please deposit exact amount.", "test2": "Account number might be different each time, so please confirm the account number each time.", "test3": "The amount shown is based on the current exchange rate. Please note that the amount may be re-calculated during the process and may differ from the actual deposit amount.", "test4": "", "test5": "", "test6": "", "test7": "", "test13": "", "test14": "", "test8": "Please take note of timing when making deposit, In order to minimize any delays, please ensure that all details are complete and correct.", "test9": "", "test10": "", "test11": "", "test12": "", "hitTitle": "Important Notice", "hitDesc": "Due to a current system issue with our bank, confirmation of incoming payments and the reflection of deposits may take longer than usual.\nIn particular, deposits made during late-night hours may not be reflected until after 9:00 AM the following day. We apologize for the inconvenience and kindly ask for your understanding that there may be a delay before your deposit is reflected."}, "bitwallet": {"depositMethods": "Bitwallet", "status": "Available", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label1_placeholder": "Please select an account", "label1_error": "Please select an account", "label2": "Amount", "label2_placeholder": "Please enter the amount", "label2_error": "Please enter the amount", "label3": "De<PERSON><PERSON><PERSON>", "label3_placeholder": "Please select the deposit currency", "label3_error": "Please select the deposit currency", "label4": "bitwallet Deposit amount", "label5": "The amount of money deposited in the trading account", "label6": "Trading account number", "test1": "Currency:", "test2": "The following is your confirmation information for this transaction:", "test3": "The amount shown is based on the current exchange rate.", "test4": "Please note that the amount may be re-calculated during the process and may differ from the actual deposit amount.", "title": "Attention:", "test5": "Please take note of timing when making deposit, In order to minimize any delays, please ensure that all details are complete and correct.", "test6": "", "test7": "", "test8": "Do not use browser Back button once you have clicked on Confirm. You will be redirected to mybitwallet payment page to complete your transaction. Once completed, you will be returned to MyFx Client Office.", "bitWalletTooltip": "When there is no trade made afterdeposited by credit card to Bitwallet,withdrawal fee from 9% will be applied. There is no withdrawal fee when there is more than 5 lots trade in thesubjected account after deposited by credit card to Bitwallet. "}, "bank_wire": {"depositMethods": "International Bank Wire", "status": "Available", "timeToFund": "1-3 Days", "fee": "Bank Fee Apply", "title1": "Please choose your account details based on your base currency.", "title_01": "It is for those who live outside Japan and cannot process domestic Japanese yen bank remittance.", "title_02": "Please note that the deposit account number will vary depending on the currency you are sending.", "title_03": "If you are using a financial institution that cannot enter your account login number, please submit proof of remittance.", "title2": "Trading Account", "test1": "if the currencies of the remittance and the MT4 / MT5 account are different, we will be replaced by the MT4 / MT5 account.", "test2": "write the MT4 / MT5 account number in the note field.", "test3": "The transfer fee in your bank will be your burden.", "test4": "if you pass through a relayed bank, you may be charged with a relaying bank charge. Please note that the fees will be charged to you.", "label1": "JPY", "label2": "USD", "label3": "AUD", "label4": "GBP", "label5": "EUR"}, "crpto": {"bigTitle": "Your deposit has been processed", "hint": "If you do not received funds on trading account after 24 hours, please contact at support with trading account number and TXID.", "depositMethods": "Crypto", "status": "Available", "timeToFund": "1-24 Hrs", "fee": "No Fees", "label1": "Select Account", "label2": "Amount", "label3": "Payment Type", "label4": "Wallet Company Name", "label5": "Crypto Address", "label6": "Chain", "label7": "Trading Account", "label8": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "label9": "Crypto Type", "label10": "Your", "label11": "Address", "label12": "Amount", "label13": "Our", "label14": "Address", "label15": "Date", "label16": "Wallet address", "label17": "Amount", "label18": "Our {type} wallet", "label19": "Transaction number (TXID)", "title": "TRANSACTION HAS NOT BEEN COMPLETED!", "test": "Currency:", "test1": "1. Send funds from your wallet to our wallet shown below.", "test2": "2. Enter TXID which is shown after you send the fund and submit.", "test3": "If TXID is not submitted, funding will be delayed.", "test5": "Send TXID which is shown after you send the fund.", "test6": "What is a TXID?", "test7": "A Transaction Identification (TXID) is a 64-character unique number used in cryptocurrency transactions.", "test8": "Due to the transaction structure with Crypto, it may take maximum 24 hours for your transaction to be completed.", "test9": "Please take time and deposit in advance to minimize any delays. You may use the Payment History page to keep your transaction status updated. If you have any questions or concerns regarding deposit with bitpay, please contact us."}, "credit_card": {"depositMethods": "Credit Card", "status": "Conditions Apply", "creditCardToolTip": "Deposits by credit card are available only to customers who have a deposit history of at least 5( five) transactions and a total of 300,000 yen or equivalent, using other payment methods.", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label1_placeholder": "Please select a registered credit card", "label1_error": "Please select a registered credit card", "label2": "Amount", "label2_placeholder": "Please enter the amount", "label2_error": "Please enter the amount", "label3": "Select registered credit card", "label3_placeholder": "Please select a registered credit card", "label3_error": "Please select a registered credit card", "label4": "Trading account", "label5": "Registered Credit Card", "label6": "Credit Card Number", "label7": "Name on the Card", "label8": "Expiry", "label9": "CVV", "test": "Currency:", "test1": "Deposited amount:", "test2": "Here is a Bank detail for JPY Only.", "test3": "Please deposit exact amount before the due date.", "test4": "Account number might be different each time, so please confirm the account number each time.", "test5": "The amount shown is based on the current exchange rate. Please note that the amount may be re-calculated during the process and may differ from the actual deposit amount.", "title": "Your deposit has been processed", "test6": "Status", "test7": "Success", "test8": "Fail", "test9": "Error Code", "test10": "Trans No", "test11": " ", "test12": " ", "test13": " ", "test14": " ", "test15": " ", "test16": " ", "test17": "Please press the payment button only once.", "remakes": "(Available for use from your 6th deposit.)"}, "awe_pay": {"depositMethods": "<PERSON><PERSON><PERSON><PERSON>", "myr": "Awepay-MYR", "thb": "Awepay-THB", "vnd": "Awepay-VND", "status": "Available", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label2": "Amount", "label3": "Deposited Currency", "title": "The following is your confirmation information for this transaction:", "test": "Currency:", "test1": "MT4", "test2": "Type", "test3": "Create Time", "from_label1": "firstname", "from_label2": "lastname", "from_label3": "country", "from_label4": "postcode", "from_label5": "<PERSON><PERSON>l", "from_label6": "<PERSON><PERSON>l"}, "rmb_funding": {"depositMethods": "RMB Funding", "status": "Available", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label2": "Amount", "label3": "Full Name", "label4": "Phone Number +86", "label5": "ID Number", "label6": "Trading account", "label7": "<PERSON><PERSON><PERSON><PERSON>", "label8": "Type", "label9": "Date", "test": "Currency:"}, "rmb_funding2": {"depositMethods1": "MyPay-CNY", "depositMethods2": "MyPay-THB", "depositMethods3": "MyPay-VND", "depositMethods4": "MyPay-INR", "depositMethods5": "MyPay-IDR", "depositMethods6": "MyPay-KRW", "status": "Available", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label2": "Amount", "label3": "Full Name (Chinese character)", "label4": "Requested Amount", "label5": "<PERSON><PERSON><PERSON><PERSON>", "label6": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "test": "Currency:", "tips": "1. Your payment bank account must be owned by you.<br/> 2. After payment is completed, your MT4 will be automatically credited.<br/> 3. You cannot submit a new payment request before completing the current payment. If you need to submit a new payment order, please wait at least 12 minutes.<br/> If you have any issues using the service, please feel free to contact customer service."}, "xCoins": {"depositMethods1": "Credit Card", "depositMethods2": "Apple Pay / Google Pay", "depositMethods3": "Neteller / Skrill", "status": "Available", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label2": "Amount", "label3": "Birthday", "label1_placeholder": "Please select an account", "label2_placeholder": "Please enter the amount", "label3_placeholder": "Please select your birthday", "confirmInfo": "Here is your confirmation information for this transaction：", "check": {"format": "Please enter the correct format", "limit": "Amounts between {number}"}, "confirmTable": {"label1": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "label2": "XCoins Transaction Cost", "label3": "Trading account", "label4": "Time"}, "tips": {"tip1": "Special Instructions for Apple Pay and Google Pay Users:", "tip2": "• Apple Pay: Log in to the Client Office from your Apple device and submit your deposit request.<br>• Google Pay: Log in to the Client Office from your Google device and submit your deposit request."}}, "MybanQ": {"depositMethods": "JPY Local Bank", "status": "Available", "timeToFund": "30 Mins", "fee": "Bank Fee Apply"}, "grandPay": {"depositMethods": "Credit Card", "status": "Conditions Apply", "creditCardToolTip": "Deposits by credit card are\nonly available to customers\nwho have a deposit history of\nat least 5 times and 100,000\nyen (or equivalent) or more in\ntotal using other methods.", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label1_placeholder": "Please select a account", "label1_error": "Please select a account", "label2": "Amount", "label2_placeholder": "Please enter the amount", "label2_error": "Please enter the amount", "label3": "Select registered credit card", "label3_placeholder": "Please select a registered credit card", "label3_error": "Please select a registered credit card", "label4": "Trading account", "label5": "Registered Credit Card", "label6": "Credit Card Number", "label7": "Name on the Card", "label8": "Expiry", "label9": "CVV", "test": "Currency:", "test1": "Deposited amount:", "test2": "Here is a Bank detail for JPY Only.", "test3": "Please deposit exact amount before the due date.", "test4": "Account number might be different each time, so please confirm the account number each time.", "test5": "The amount shown is based on the current exchange rate. Please note that the amount may be re-calculated during the process and may differ from the actual deposit amount.", "title": "Your deposit has been processed", "test6": "Status", "test7": "Success", "test8": "Fail", "test9": "Error Code", "test10": "Trans No", "test11": " ", "test12": " ", "test13": " ", "test14": " ", "test15": " ", "test16": " ", "test17": "Please press the payment button only once.", "remakes": "(Available for use from your 6th deposit.)"}, "monetix": {"depositMethods": "THB QR Payment", "Philippines": "PHP QR Payment", "MYR": "MYR Payment", "status": "Available", "timeToFund": "Instant", "fee": "No Fees", "label1": "Select Account", "label2": "Amount", "label1_placeholder": "Please select an account", "label2_placeholder": "Please enter the amount", "confirmInfo": "Here is your confirmation information for this transaction：", "check": {"format": "Please enter the correct format", "limit": "Amounts between {number}"}, "confirmTable": {"label1": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "label2": "Monetix Transaction Cost", "label3": "Trading account", "label4": "Time"}}, "mbwSuccess": {"title": "PAYMENT CONFIRMATION", "success": "SUCCESS", "failed": "FAILED", "error": "Error", "payStatus": "Payment Status", "order": "Order No", "mt4Login": "Mt4 Login", "fundingAmount": "Funding Amount", "amount": "Amount", "time": "Create Time", "desc": "Mybitwallet will send the result to the system and the amount will deposit to your account as soon as the system receive the result, please be patient.", "back": "Back to home page"}, "xcoinsSuccess": {"title": "PAYMENT CONFIRMATION", "success": "SUCCESS", "failed": "FAILED", "payStatus": "Payment Status", "fundingAmount": "Funding Amount", "account": "Trading Account", "order": "Order No", "time": "Create Time", "desc": "XCoins will send the result to the system and the amount will deposit to your account as soon as the system receive the result, please be patient.", "back": "Back to home page"}, "grandPaySuccess": {"title": "PAYMENT CONFIRMATION", "success": "SUCCESS", "failed": "FAILED", "payStatus": "Payment Status", "fundingAmount": "Funding Amount", "account": "Trading Account", "order": "Order No", "time": "Create Time", "desc": "GrandPay will send the result to the system and the amount will deposit to your account as soon as the system receive the result, please be patient.", "back": "Back to home page"}, "myPaySuccess": {"title": "PAYMENT CONFIRMATION", "success": "SUCCESS", "failed": "FAILED", "payStatus": "Payment Status", "fundingAmount": "Funding Amount", "account": "Trading Account", "order": "Order No", "time": "Create Time", "desc": "MyPay will send the result to the system and the amount will deposit to your account as soon as the system receive the result, please be patient.", "back": "Back to home page"}, "monetixSuccess": {"title": "PAYMENT CONFIRMATION", "success": "SUCCESS", "failed": "FAILED", "payStatus": "Payment Status", "fundingAmount": "Funding Amount", "account": "Trading Account", "order": "Order No", "time": "Create Time", "desc": "Monetix will send the result to the system and the amount will deposit to your account as soon as the system receive the result, please be patient.", "back": "Back to home page"}, "depositCommonResult": {"title": "PAYMENT CONFIRMATION", "success": "SUCCESS", "failed": "FAILED", "payStatus": "Payment Status", "fundingAmount": "Funding Amount", "account": "Trading Account", "order": "Order No", "time": "Create Time", "desc": "Awepay will send the result to the system and the amount will deposit to your account as soon as the system receive the result, please be patient.", "back": "Back to home page"}}, "withdrawal": {"newYearTips": "<p style=\"margin: 4px;\">&lt;During the Year-End and New Year Holiday&gt;</p><p style=\"margin: 4px;\">\n・Withdrawal operations will be suspended on December 25 (Wednesday), December 26 (Thursday), and January 1 (Wednesday).</p><p style=\"margin: 4px;\">・Domestic bank transfers for JPY (Japanese Yen) will be suspended from December 31 (Tuesday) to January 5 (Sunday).</p><p style=\"margin: 4px;\">・Withdrawals requested during these periods will be processed starting from the next business day; however, please note that processing times may be longer than usual.</p>", "hint_01": "Before making a withdrawal, please make sure that you have sufficient margin if there is a trade in progress.", "hint_02": "Please note that if there is not enough margin left, the margin level may fall after the withdrawal is completed and it may be forced to be executed.", "hint_03": "Before you make a withdrawal it may take approximately 10 days from the time we start processing your withdrawal before the amount is deposited in your account.", "hint_04": "If the credit card company has a closing date during this period, the bill will be billed and the withdrawal will be made, but the credit card company will refund the amount next time, so it may take up to 40 days for the refunded amount to be restored to the credit line.", "title": "Withdraw", "Vault": "<PERSON><PERSON>", "table": {"processed_date_time": "Processed Date/Time", "transaction_type": "Transaction Type", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "account_number": "Account Number", "withdrawal_type": "Withdraw Type", "status": "Status", "statusType": {"PROCESSED": "Processed", "DELETED": "Deleted", "FAILED": "Failed", "MAMPAMM": "MAM/PAMM", "CANCELLED": "Cancelled", "PROCESSING": "Processing", "FALLED": "Cancel"}}, "submit-table": {"mt4Login": "Account", "server": "Server", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "type": "Type", "status": "Status", "createTime": "Time", "type-default": "<PERSON><PERSON><PERSON>", "type-watell": "Wallet withdrawal", "walletLogin": "<PERSON><PERSON>"}, "title2": "Trading Account <PERSON><PERSON><PERSON>", "title3": "MAM/PAMM Account <PERSON><PERSON><PERSON>", "wd-title2": "Trading Account", "wd-title3": "MAM/PAMM", "tipsTableColumns": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount"}, "tipsTableColumnsCrypto": {"currency": "Block chain", "amount": "USDT", "tips": "Withdrawal fee can be vary depending on each convert rate and gas fee charged."}, "tips": {"title": "Guidelines", "test1": "In accordance with AML/CTF regulations, MYFX Markets cannot deposit funds to third parties. All withdrawals from your account must be transferred to an account under the same name as your MYFX Markets trading account.", "test2": "The withdrawal process typically takes 1-3 business days. For withdrawal requests made on weekends,there may be a delay in processing until the following business day.", "test3": "Please be aware that processing companies may charge separate fees for transactions.", "test4": "The maximum withdrawal amount through bitwallet is 20,000 USD. Withdrawal requests will not be processed if the current Margin Level falls below 250%.", "test5": "To adhere to Anti-Money Laundering (AML) regulations, we are obligated to send funds only to an account bearing the same name as your trading account. Your cooperation in this matter is greatly appreciated.", "test6": "Please note that intermediary bank fees are not determined by MYFX MARKETS.", "test_06_01": "仲介銀行にて発生する別手数料はMYFX MARKETSが設定した手数料ではございません。", "test_06_02": "オーストラリア国内の銀行口座への出金に手数料はかかりません。", "test7": "A guide to accounts and withdrawals.", "test8": "A manual on contributions and disbursements was prepared.", "test9": "Download it here.", "test10": "Download Collection Manual", "test11": "Those with MAM/PAMM accounts can download it here.", "test12": "Download the Collection Manual (for MAM/PAMM)", "test13": "Because the service here reflects the delay according to the timing of the inter-bank transfer, it is recommended to allow money transfer.", "test13_01": "If the margin maintenance rate of the source account is <span style=\"color: red; font-weight: bold;\">250%</span> or less, you cannot apply. Please confirm the balance of your position and valid margin before applying.", "test13_02": "In accordance with anti-money laundering (AML) laws, funds will only be transferred to the same account as the name of the transaction account.", "test13_03": "Please use JPY account as the remittance will be made in JPY. Please note that if the trading account currency is not JPY, we will exchange it to JPY and send the money.", "test13_04": "For domestic remittances, you will need the financial institution number and branch number of the recipient financial institution. For financial institution Numbers and branch Numbers, please check their websites.", "test13_05": "Withdrawal fees will be deducted from the withdrawal amount.", "test14": "The detailed remittance account is issued at the time of each remittance application and cannot be used for the next remittance. Please apply for payment every time.", "test15": "If the remittance amount is more than 500,000 yen, please enter the request number in the remittance application acceptance mail before the name of the remittance request.", "test16": "The transfer fee charged at the Customer's bank will be borne by the Customer.", "test17": "If the trading account currency is other than JPY, the remittance amount converted to Japanese yen will be displayed on the next page.", "test18": "The margin maintenance rate of the withdrawal account cannot be applied if it is below 250%. Please confirm the balance of the position and valid margin before applying.", "test19": "In accordance with anti-Money laundering (AML) laws, money is only sent to an account in the same name as the trading account.", "test20": "If the currency of the trading account and the currency of the bank account are different, please specify the currency of the bank account in the comments section.", "test21": "If you want the bank to remit more than 1 million yen, please apply several times. The handling fee is charged for each application.", "test22": "The withdrawal fee is deducted from the withdrawal amount to handle the procedure.", "test23": "Other fees incurred at intermediary banks are not fees set by MYFX MARKETS.", "test24": "There is no charge for withdrawals from domestic Australian bank accounts.", "test25": "The withdrawal fee is deducted from the withdrawal amount to handle the procedure.", "test26": "When withdrawing with virtual currency, there will be a transaction fee for converting the MT4/MT5 account currency into virtual currency and a blockchain transaction fee.", "test27": "The withdrawal fee varies according to the exchange fee for each virtual currency and the blockchain fee.", "test28": "Credit card withdrawal fee 2000 yen, because the received transaction occurred, please understand. For example, if you deposit 20,000 yen, 30,000 yen, and 40,000 yen (a total of 90,000 yen), and withdraw 50,000 yen, you need to withdraw 20,000 yen and 30,000 yen respectively, because you need to charge fees respectively, and the total is 4,000 yen.", "test29": "Credit card withdrawals need to be the same as credit card withdrawals. Therefore, in the case of the profit portion and the contribution amount is less than the income amount, the domestic bank refunds the contribution amount.", "test30": "The process until the withdrawal is complete. The withdrawal amount reaches the customer's account, and it takes approximately 10 days after the Company starts the withdrawal processing. If the date of the credit card company is available during this period, once the application is increased, the deduction will be made. However, it will take about 40 days from the next refund from the credit card company to the renewal of the credit limit.", "test31": "If you wish to withdraw the above maximum withdrawal amount, please make multiple applications.", "test32": "If you make five or more payments to bitwallet with a credit card without a transaction, a withdrawal fee of 9% ~(depending on the brand of the card) applies. There is no fee for withdrawals when there are more than 5 transactions in the object account.", "test33": "The application cannot be made if the margin maintenance rate of the source account is less than 250%. Please confirm the balance of your position and valid margin before applying.", "test34": "If you have deposited more than 5 lots with your credit card, a withdrawal fee of 9% (depending on the card brand) will be applied. Withdrawal fees are free if there are more than 5 lots in the eligible account.", "title2": " Minimum Withdrawal Amount:", "title3": "Maximum Withdrawal Amount:", "title4": "Bank Fee Schedule:", "title5": "Withdrawal Fee Schedule:", "title6": "<PERSON><PERSON><PERSON> Fee:"}, "step1": "step 1", "step2": "step 2", "step3": "step 3", "description1": "Step One: Select A Withdrawal Type", "description2": "Step Two: Enter Your Security Question Answer", "description3": "Step Three: Enter Your Withdrawal Information", "description1-1": "Step One: Enter Your Security Question Answer", "description2-2": "Step Two: Enter Your Withdrawal Information", "withdrawal_way_columns": {"payment_methods": "Withdraw Methods", "time_to_withdraw": "Time to Withdraw", "fee": "Fee", "operation": "Operation"}, "withdrawalWay": {"credit_card": "Credit Card", "international_bank_wire": "International Bank Wire", "bitwallet": "Bitwallet", "crypto_currency": "Crypto Currency", "jp_domestic_bank_wire": "JP Domestic Bank Wire", "cny_Withdrawal": "CNY <PERSON>", "awe_pay": "AWE Pay", "cny_direct_transfer": "CNY Direct Transfer", "monetix": "THB Withdrawal", "10days": "10 Days", "1Day": "1 Day", "1_2Days": "1-2 Days", "1_3Days": "1-3 Days", "instant": "Instant", "fee1": "$25", "fee2": "No Fees", "min": "Minimum Withdrawal amount"}, "mam_pam_form": {"label1": "Select Account", "label2": "Type", "label3": "Amount", "label4": "Comments", "label2_placeholder": "Please select the type"}, "credit_card": {"label1": "Select Account", "label2": "Amount", "label3": "Comments"}, "bank_wire": {"add_account": "Registration", "label1": "Select Account", "label2": "Amount", "label3": "Bank Account", "label4": "Bank Name", "label5": "Beneficiary Account Name", "label6": "Branch Name/BSB/Sort Code", "label7": "IBAN/Account Number", "label8": "SWIFT code", "label9": "Comments", "dialog_title": "Add a New Bank Account", "addAccount": "Register a bank account to withdraw money", "hint_01": "It is for those who live outside Japan and cannot process domestic Japanese yen bank remittance.", "hint_02": "If you use a bank in Japan.", "hint_03": "Here.", "hint_04": "Please proceed from domestic bank transfer of Japanese yen.", "hint_05": "Maximum daily withdrawal amount: 1 million yen equivalent (there is no daily maximum amount)", "hint_06": "If you wish to withdraw more than 1 million yen, please make multiple requests.", "dialogClose": "The bank account has been registered."}, "bit_wallet": {"label1": "Select Account", "label2": "Amount", "label3": "MyBitWallet login email", "label4": "Comments", "email_placeholder": "Please enter your email address", "title1": "Maximum amount outstanding for one day.", "title2": "If you wish to pay more than 250000 yen or more, please apply it separately."}, "crpto_currency": {"tip": "If you would like to pay more than 200000 usdt or more than 2000 usdt, please apply for multiple installations.", "tip_01": "Maximum amount of money per day", "tip_02": "If you wish to pay more than 2000 usdt or more, please apply for multiple installations.", "label1": "Select Account", "label2": "Amount", "label3": "Select Currency", "label4": "Select a Block chain", "label5": "Your wallet company name", "label6": "Wallet Address", "label7": "Comments", "checked_test1": "Due to the inherent characteristics of crypto currencies, it is not possible to track or reverse transactions after it's been processed.", "checked_test2": "Please ensure that all the information provided are correct before processing your withdrawal.", "company_name_placeholder": "Please enter a name", "address_placeholder": "Please enter the address"}, "awe_pay": {"label1": "Select Account", "label2": "Amount", "label3": "Select Withdrawal Currency", "label4": "Credit Account", "label5": "Bank Name", "label6": "Branch Name", "label7": "Branch City", "label8": "Branch Code", "label9": "Beneficiary Account Name", "label10": "IBAN/Account Number", "label11": "Comments", "label12": "Branch Number", "label13": "SWIFT code", "dialog_title": "Add a New Bank Account", "placeholder": {"currency": "Please enter the deposit currency", "bankAccount": "Please select a bank account", "bankName": "Please enter the bank name", "branchName": "Please enter the branch name", "branchCity": "Please enter the branch address", "bankCode": "Please enter the branch code", "nameOnCard": "Please enter your account name", "cardNumber": "Please enter your Bank Account Number", "comment": "Please enter a note"}, "dialog_success": "Bank account added successfully."}, "cny_withdrawal": {"label1": "Select Account", "label2": "Amount", "label3": "Convert to RMB", "label4": "Bank Account", "label5": "IBAN/Account Number", "label6": "Beneficiary Account Name", "label7": "Bank Name", "label8": "Branch Name/BSB/Sort Code", "label9": "SWIFT code", "label10": "Bank Addres", "label11": "Comments", "dialog_title": "Add a New Bank Account", "test": "Select CNY withdrawal."}, "jp_dbw": {"label1": "Select Account", "label2": "Amount", "label3": "Add bank account", "label4": "Bank Name", "label5": "Bank Code", "label6": "Branch Name", "label7": "Branch Number/BSB/Sort Code", "label8": "Beneficiary Account Name", "label_08_01": "口座名義人(", "label_08_02": "半角カタカナ", "label_08_03": "でご入力ください)", "label9": "IBAN/Account Number", "label10": "Account Type", "label11": "Comments", "dialog_title": "Add a New Bank Account", "dialog_success": "Bank account added successfully.", "placeholder": "Please select", "confirmTitle": "Do you want to delete this account？", "tip": "tips", "sure": "confirm", "cancel": "cancel", "message": "Delete successful！"}, "cny_direct_transfer": {"label1": "Select Account"}, "rest_security": "Reset the Security Questions", "rest_security2": "", "balance_message": "The balance does not meet the minimum deposit requirement", "agreement": "Please check the agreement", "securityTitle": "Reset Security Questions", "securityTest1": "We will send Security question reset link to registered email address.", "securityTest2": "Click the link below and check your inbox.", "button_rest_security": "Send Reset Security Questions", "successTest1": "Security Question reset email successfully sent!", "CreditCard": {"form": {"label1": "Account", "label1_placeholder": "Please select an account", "label1_error1": "Please select an account", "label1_balance": "Available Balance", "label2": "Amount", "label2_placeholder": "Please enter amount", "label2_error1": "Please enter amount", "label2_error2": "Please enter a valid number", "label2_error3": "Current account balance is insufficient", "label2_error4": "Must not be less than the minimum withdrawal limit", "label2_error5": "The maximum withdrawal limit must not be exceeded", "label3": "Comment", "label3_placeholder": "Please enter a comment"}}, "BankWire": {"form": {"label3": "Bank name", "label3_error1": "Please enter bank name", "label3_placeholder": "Please enter bank name", "label4": "Account name", "label4_error1": "Please enter your account name", "label4_placeholder": "Please enter your account name", "label5": "Branch Number/BSB/Sort Code", "label5_error1": "Please enter branch number/bsb/sort code", "label5_placeholder": "Please enter the branch number/bsb/sort code", "label6": "IBAN/Account Number", "label6_error1": "Please enter your iban/account number", "label6_placeholder": "Please enter iban/account number", "label7": "SWIFT code", "label7_error1": "Please enter SWIFT code", "label7_placeholder": "Please enter SWIFT code", "label8": "Comment", "label8_placeholder": "Please enter a comment"}}, "JPDomesticBankWire": {"accountType": {"label1": "Ordinary", "label2": "Current", "label3": "Savings", "label4": "Other"}, "tooltip": {"tip1": "【How to input half-width katakana】", "tip2": "For Windows", "tip3": "After entering the text (before confirming it), press the space bar to find it among the predictive text, or press F8.", "tip4": "For Mac", "tip5": "After entering (before confirming), press the space bar to find it in the predictive text, or press \"fn+F8\" or \"Control + ;\". If the shortcut doesn't work, you need to add \"Half-width Katakana\" to the input source input mode in the keyboard settings."}, "form": {"label3": "Bank name", "label3_error1": "Please enter bank name", "label3_placeholder": "Please enter bank name", "label4": "Bank Code", "label4_error1": "Please enter bank code", "label4_error2": "Please enter a valid number", "label4_error3": "The bank code is 4 digits.", "label4_placeholder": "Please enter bank code", "label5": "Branch Name", "label5_error1": "Please enter the branch name", "label5_placeholder": "Please enter the branch name", "label6": "Branch Code", "label6_error1": "Please enter branch code", "label6_error2": "Please enter a valid number", "label6_error3": "The branch code is 3 digits.", "label6_placeholder": "Please enter branch code", "label7": "Account Holder", "label7_error1": "Please enter Account Holder", "label7_error2": "Please enter half-width Katakana characters", "label7_placeholder": "Account Holder", "label8": "Bank Account Number", "label8_error1": "Please enter your Bank Account Number", "label8_error2": "Please enter a valid number", "label8_error3": "Bank Account Number is 5 to 7 digits", "label8_placeholder": "Please enter your Bank Account Number", "label9": "SWIFT code", "label9_error1": "Please enter SWIFT code", "label9_placeholder": "Please enter SWIFT code", "label10": "Account type", "label10_error1": "Please select account type", "label10_placeholder": "Please select account type", "label11": "Comment", "label11_placeholder": "Please enter a comment"}}, "CNYWithdrawal": {"form": {"label3": "Bank name", "label3_error1": "Please enter bank name", "label3_placeholder": "Please enter bank name", "label4": "Branch Name", "label4_error1": "Please enter the branch name", "label4_placeholder": "Please enter the branch name", "label5": "Bank Account Number", "label5_error1": "Please enter your Bank Account Number", "label5_placeholder": "Please enter your Bank Account Number", "label6": "Account Holder", "label6_error1": "Please enter Account Holder", "label6_placeholder": "Please enter Account Holder", "label7": "Phone number", "label7_error1": "Please enter phone number", "label7_error2": "Please enter a valid phone number", "label7_placeholder": "Please enter phone number", "label8": "SWIFT code", "label8_error1": "Please enter SWIFT code", "label8_placeholder": "Please enter SWIFT code", "label9": "Comment", "label9_placeholder": "Please enter a comment"}}, "CNYDirectTransfer": {"form": {"label3": "Bank name", "label3_error1": "Please enter bank name", "label3_placeholder": "Please enter bank name", "label4": "Branch Name", "label4_error1": "Please enter the branch name", "label4_placeholder": "Please enter the branch name", "label5": "Bank address", "label5_error1": "Please enter bank address", "label5_placeholder": "Please enter bank address", "label6": "Bank Account Number", "label6_error1": "Please enter your Bank Account Number", "label6_placeholder": "Please enter your Bank Account Number", "label7": "Account Holder", "label7_error1": "Please enter Account Holder", "label7_placeholder": "Please enter Account Holder", "label8": "SWIFT code", "label8_error1": "Please enter SWIFT code", "label8_placeholder": "Please enter SWIFT code", "label9": "Comment", "label9_placeholder": "Please enter a comment"}}}, "InternalFundTransfer": {"apply": "Apply", "select_01": "Please select the account from which the money was transferred.", "select_02": "Please select the account to send the money to.", "sourceAccount": "source account", "targetAccount": "target account", "checkAmount": "Please enter the amount", "checkBalance": "Not to exceed the balance", "wallet": "MYFX Wallet", "error": {"error1": "Origin account must be valid !", "error2": "Target account must be valid !", "error3": "Target account must be different !"}}, "transaction": {"deposits": {"paymentType": {"Crypto": "Crypto", "BitWallet": "BitWallet", "JPYBankWire": "JPY BankWire", "JPDomesticBankWire": "JP Domestic Bank Wire", "InternationalBankWire": "International Bank Wire", "CreditCard": "Credit Card", "BankWire": "Bank Wire", "UNION_PAY": "UNION_PAY", "Crypto365": "Crypto 365", "MyPay": "My Pay", "MAMPAMMWithdraw": "MAM PAMM Withdraw", "MultiLevelRebate": "MultiLevel Rebate", "CNYBankWire": "CNY Bank Wire", "Wallet": "Wallet", "Xcoins": "Xcoins", "Monetix": "THB Bank Wire", "THBBankWire": "THB Bank Wire", "internationalbank": "International Bank Wire"}}, "title": "Transaction", "table": {"processed_date_time": "Processed Date/Time", "source_account": "Source Account", "target_account": "Target Account", "amount": "Amount", "status": "Status"}, "label1": "Source Account", "label2": "Margin Level:", "label3": "Available Balance:", "label4": "Enter Amount", "label5": "Your Comments", "test": "Your request for internal funds transfer has been sent successfully", "title1": "Transfer success", "tips": {"title": "Guidelines", "test1": "Requests cannot be processed if the current Margin Level falls  <span style=\"color: red;font-weight: bold;\">250%</span> application cannot be made in the following cases.", "test2": "", "test3": "If currency conversion is required, our most favourable exchange rate will be applied.", "test4": "In the case of transfers between accounts requiring cash to be converted, our optimal rate will apply."}}, "check": {"amount": {"test1": "Amount cannot be empty", "test2": "Please enter the correct content", "test3": "Not to exceed the balance", "test4": "Not to less than the Minimum withdrawal limit"}, "bank_name": {"test1": "'Bank Name' cannot be empty"}, "psp_currency": {"test1": "Please select the currency for withdrawal"}, "name_on_card": {"test1": "'Beneficiary Account Name' cannot be empty", "jpbanck_test1": "Please enter the account name in half-width katakana"}, "branch_name": {"test1": "'Branch Name/BSB/Sort' cannot be empty"}, "card_number": {"test1": "'IBAN/Account Number' cannot be empty", "test2": "Card Number is least 5 digits and at most 7 digits.", "type": "请选择类型"}, "swift": {"test1": "'SWIFT code' cannot be empty"}, "email": {"test1": "Please enter the correct email format", "test2": "Not the current login email", "test3": "Email cannot be empty"}, "company_name": {"test1": "Please input Crypto wallet company name"}, "address": {"test1": "Please input your crypto address", "test2": " 'Bank address' cannot be empty"}, "branch_city": {"test1": "'Branch City' cannot be empty"}, "branch_code": {"test1": "'Branch Code' cannot be empty", "test2": "Branch code is 3 digits"}, "bank_code": {"test1": "'Bank Code' cannot be empty", "test2": "Bank code is 4 digits"}, "phone": {"test1": "'phone' cannot be empty", "test2": "Please input your 11 digits phone number"}, "crypto": {"test1": "'crypto currency' cannot be empty"}, "chainType": {"test1": "'Block chain' cannot be empty"}}, "available_balance": "Available Balance:", "account": "Account", "registeredCreditCard": "Registered Credit Card"}, "nav": {"allNotifications": "All Notifications", "allMessages": "All Messages", "allCarts": "All Carts", "viewCarts": "View Carts", "hello": "Hello", "user": {"available": "Available", "profileTitle": "My Profile", "profileSub": "View personal profile details.", "profileEditTitle": "Edit Profile", "profileEditSub": "Modify your personal details.", "accountSettingTitle": "Account <PERSON>ting", "accountSettingSub": "Manage your account parameters.", "privacySettingTitle": "Privacy Setting", "privacySettingSub": "Control your privacy parameters.", "signout": "Sign Out", "resetCoPassword": "Reset CO Password"}}, "dashboard2": {"card1": "AVG Impressions", "card2": "AVG Engagements Rate", "card3": "AVG Reach", "card4": "AVG Transport", "featureTableTitle": "Frequent Post List", "summary": "Post Summary", "totalLikeComment": "Total of Likes $ Comments", "bestPost": "Best Performing Posts"}, "dashboard1": {"card1": "Trade History", "card2": "Closed P&L", "card3": "Market News / Announcement", "card4": "Promotions", "profitAssistant": "Profit Assistant", "winningOrders": "Winning Orders", "openInvoice": "Open Invoices", "depositAndWithdrawal": "Deposit And Withdrawal", "balance": "Balance", "paymentHistory": "Payment History", "currencyStrength": "<PERSON><PERSON><PERSON><PERSON>", "marketSentiment": "Market Sentiment", "deposit_withdrawal_detail": "<PERSON><PERSON><PERSON><PERSON>", "account_details": "Account Details", "position": "Position", "history": "History", "account": "Select Account", "accountBalance": "Balance", "placeholder": "Please select", "dropdown_cancel": "Cancel", "dropdown_done": "Done", "type": {"Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal": "Withdraw"}, "winningOrdersEmpty": "No data", "tableColumnField": {"Ticket": "Ticket", "Symbol": "Symbol", "CMD": "Order Type", "OpenDateTime": "Open Date Time", "OpenPrice": "Open Price", "Swaps": "Swaps", "Commission": "Commission", "Profit": "Profit", "Comment": "Comment", "CloseDateTime": "Close Date Time", "ClosePrice": "Close Price", "Time": "Time", "Lots": "Lots", "Price": "Price"}, "Status": {"title": "Status", "status_01": "Active", "status_02": "Inactive", "hint": "Your trading account is archived, for assistance with your account balance, please contact our customer support."}}, "rightSide": {"customerDistribution": "Customer Distribution", "projectStatistic": "Project Statistic", "countries": "Countries"}, "dropdown": {"view": "Details", "delete": "Delete", "edit": "Edit", "print": "Print", "download": "Download", "unfollow": "Unfollow", "follow": "Follow", "block": "Block"}, "customizer": {"colorCustomizer": "Theme Customizer", "themeColor": "Theme Color", "colorMode": "Color Mode", "sidebar": "Sidebar", "sidebarMini": "Sidebar Mini", "routeAnimation": "Route Animation", "rtlMode": "RTL Mode", "rtl": "RTL", "reset": "Reset"}, "login": {"ClientOffice": "Client Office", "PartnerPortal": "Partner Portal", "sigin": "Sign in", "email": "Email address", "password": "Password", "check_code": "Verification Code", "enter_email": "Enter email", "enter_password": "Enter password", "enter_check_code": "Enter check code", "title": "Manage your accounts", "forgot_password": "Forgot password?", "text": "Enter your email address and password to access Client Office.", "reset_password": "Reset Password", "reset_password_confirm": "Send authentication code", "reset_password_text": "Enter your email address and we'll send you an email with instructions to reset your password.", "reset_password_text_code": "Enter the verification code that sent to your email", "reset_password_resend": "Resend", "reset_password_send": "Send", "reset_password_submit": "Submit", "resendCode": "Verification code has been resent", "tips1": "Your password has been successfully changed.", "tips2": "Please log in again with your new password.", "Enter_the_new_password": "Enter the new password", "New_Password": "New Password", "New_Password_Confirm": "New Password Confirm", "Password_inconsistency": "Password inconsistency", "Insufficient_password_complexity": "Insufficient password complexity", "reset_password_active2": {"desc": "Enter the new password", "new_password": "New Password", "re_password": "New Password Confirm", "inconsistency": "Password inconsistency", "validateSelection": {"Eight Character": "Eight Character", "One Number": "One Number", "One LowerCase": "One LowerCase", "One UpperCase": "One UpperCase"}, "submit": "submit", "submitSuccess": "Change password has been completed"}}, "message": {"email": {"error": "Email sending failed"}}, "accountRegister": {"promotionCheckbox": "I am participating in the current <a target=\"__blank\" href=\"https://myfxmarkets.com/promotion/website-********************/\">cashback promotion</a> and have read and agreed to the <a target=\"__blank\" href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/T%26C+-+Website+%26+Client+Office+Relaunch+Promotion!.pdf\">terms and conditions</a>.", "pdfSubmitButton": "SUBMIT CONTRACT", "pdfAttention": "After submitting the LPOA, you will receive an email", "pdfSubmitHint": "You may have to wait 1 to 2 minutes", "title": "MYFX MARKETS LIVE ACCOUNT APPLICATION", "titleSlice01": "MYFX MARKETS", "titleSlice02": "LIVE ACCOUNT APPLICATION", "title1": "Individual Trading Account", "title2": "Corporate Trading Account", "step1": "step1", "step2": "step2", "step3": "step3", "description1": "Step One: Personal Information", "description2": "Step Two: Security Questions", "description3": "Step Three: Account Specification", "label1": "City ", "label2": "PHONE NUMBER ", "label3": "IB CODE ", "label4": "WHERE DID YOU HEAR ABOUT MYFX MARKETS ", "label5": "ADDRESS ", "label6": "DATE OF BIRTH ", "label7": "TRADING EXPERIENCE ", "label8": "CREATE PASSWORD ", "label9": "PASSWORD MUST CONTAIN:", "label10": "身份证号码 ", "label11": "中文（汉字）姓名 ", "label12": "PLEASE SPECIFY WHERE ELSE YOU HAVE HEARD ABOUT MYFX MARKETS", "label13": "COMPANY NAME", "label14": "COMPANY REGISTRATION NUMBER", "test1": "Enter Your City", "test2": "Enter Phone Number", "test3": "Enter IB Code", "test4": "Please select", "test5": "Enter Your Address", "test6": "Enter Your Password", "test7": "请输入您的中文（汉字）姓名", "test8": "请输入身份证号", "dataTime": "Select date time", "checkbox1": "One Uppercase", "checkbox2": "One Lowercase", "checkbox3": "One Number", "checkbox4": "Eight Character", "checkbox_5": "*Valid symbols <span style=\"font-weight: bold;\">!@#._</span>", "checkbox_5_no_html": "*Valid symbols !@#._", "checkbox5": "By ticking this box, you are agreeing that you have read, understood and agree in full with the attached <a href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/MYFX_Markets_Mauritius_Execution_Policy_Ver_3_August_2024.pdf\" target=\"_blank\">MYFX Markets Mauritius Client Agreement.</a>", "checkbox6": "By ticking this box, you are agreeing that you have read, understood and agree in full with the attached <a href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/MYFX_Markets_Mauritius_Complaints_Policy_Ver_3_August_2024.pdf\" target=\"_blank\">MYFX Markets Mauritius Execution Policy.</a>", "checkbox7": "By ticking this box, you are agreeing that you have read, understood and agree in full with the attached <a href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/MYFX_Markets_Mauritius_Client_Agreement_Ver_3_August_2024.pdf\" target=\"_blank\">MYFX Markets Mauritius Complaints Policy.</a>", "select_marketsList": {"select1": {"item": "Search Engine", "value": "search"}, "select2": {"item": "Promotions", "value": "promotion"}, "select3": {"item": "FX Websites", "value": "fxwebsite"}, "select4": {"item": "IB", "value": "ib"}, "select5": {"item": "Others* Please enter below-", "value": "other"}}, "experienceList": {"select1": {"item": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "select2": {"item": "Intermediate", "value": "Intermediate"}, "select3": {"item": "Advanced", "value": "Advanced"}}, "mt4AccountTypeList": {"select1": {"item": "MT4 Standard Account", "value": "Standard"}, "select2": {"item": "MT4 Pro Account", "value": "Pro"}, "select3": {"item": "MT5 Standard Account", "value": "st_mt5"}, "select4": {"item": "MT5 Pro Account", "value": "pro_mt5"}, "select5": {"item": "MAM Account", "value": "MAM"}, "select6": {"item": "PAMM Account", "value": "PAMM"}, "select7": {"item": "MT4 Micro Account", "value": "mt4_micro_standard"}, "select8": {"item": "Copy Trade", "value": "Copy Trade"}}, "tradeNumber": {"select1": {"item": "Less than 10", "value": "Less than 10"}, "select2": {"item": "10 - 100", "value": "10 - 100"}, "select3": {"item": "101 - 1000", "value": "101 - 1000"}, "select4": {"item": "More than 1000", "value": "More than 1000"}}, "tradeVolume": {"select1": {"item": "Less than 10 lots", "value": "Less than 10 lots"}, "select2": {"item": "10 - 50 lots", "value": "10 - 50 lots"}, "select3": {"item": "200 - 1000 lots", "value": "200 - 1000 lots"}, "select4": {"item": "More than 1000 lots", "value": "More than 1000 lots"}}, "tradeProducts": {"select1": {"item": "Forex", "value": "Forex"}, "select2": {"item": "Metals", "value": "Metals"}, "select3": {"item": "Oil", "value": "Oil"}, "select4": {"item": "Indices", "value": "Indices"}, "select5": {"item": "Crypto", "value": "Crypto"}, "select6": {"item": "Other", "value": "Other"}}, "initialDeposit": {"select1": {"item": "Less than 1,000 USD (Or equvalent)", "value": "Less than 1,000 USD (Or equvalent)"}, "select2": {"item": "1,000 - 5,000 USD (Or equvalent)", "value": "1,000 - 5,000 USD (Or equvalent)"}, "select3": {"item": "5,000 - 20,000 USD (Or equvalent)", "value": "5,000 - 20,000 USD (Or equvalent)"}, "select4": {"item": "More than 20,000 USD (Or equvalent)", "value": "More than 20,000 USD (Or equvalent)"}}, "expectedTradeVolume": {"select1": {"item": "Less than 10 lots", "value": "Less than 10 lots"}, "select2": {"item": "10 - 50 lots", "value": "10 - 50 lots"}, "select3": {"item": "50 - 200 lots", "value": "50 - 200 lots"}, "select4": {"item": "More than 1000 lots", "value": "More than 1000 lots"}}, "IB_test": "The IB number is a number used by our company to identify who introduced you to account opening. If you have received any instructions from the referring party (person), please enter the designated number. Please note that it is necessary to enter the IB number when opening a MAM/PAMM account.", "bottom_test1": "Warning: ", "bottom_test2": "FX market involves significant risks, including complete possible loss of funds. Consequently trading is not suitable for all investors and traders. By increasing leverage risk increases as well.", "bottom_test3": "Legal: ", "bottom_test4": "MYFX Markets, the trading name of AXIS INC., is incorporated under registered number L15835/MYFX by the Registrar of International Business Companies, and registered by the Financial Services Authority.", "bottom_test5": "Myfx Markets, the trading name of Milson’s Fintech, provides you with educational resources to help you become familiar with all the trading features and tools in the trading platform. With the demo account you can test any trading strategies you wish in a risk-free environment. Please bear in mind that the results of the transactions of the practice account are virtual, and do not reflect any real profit or loss or a real trading environment, whereas market conditions may affect both the quotation and execution. FX products are leveraged products and trading FX therefore involves a high level of risk that may not be suitable for everyone. Myfx Markets recommends that you ensure that you fully understand the risks involved before making any decision concerning Myfx Markets’ products. Independent advice should be sought if necessary.", "bottom_test6": "Current Disclaimer: The content of this website does not constitute a recommendation and, consequently, you should consider the information in light of your objectives, financial situation and needs before making any decision about whether to acquire any MYFX Markets’ financial products. A Disclosure Document is available here or sending <NAME_EMAIL> or by calling +64 9 889 4022, which should be considered prior to acquiring or continuing to hold CFDs. Information about our services, including our fees and charges is also available at those sources.", "check": {"phone": {"test1": "Invalid phone number: Phone number must be a numeric value.", "test2": "7-14 Digitals", "test3": "This field is required."}, "address": {"test1": "This field is required", "test2": "A minimum of 2 words is required here.", "test3": "Please enter a valid residential address."}, "date": {"test1": "This field is required", "test2": "Persons under the age of 18 are not permitted to register."}, "security": {"test1": "Please choose different security question.", "test2": "This field is required.", "test3": "Please choose security question.", "test4": "Please enter English", "placeholder": "Choose a question"}, "specification": {"test1": "This field is required"}, "companyNamec": {"test1": "This field is required"}, "nameCN": {"test1": "此字段为必填字段。", "test2": "请输入汉字。", "test3": "最少两个汉字。"}, "cid": {"test1": "此字段为必填字段。", "test2": "请输入正确的身份证号格式。"}}, "security_querstion": {"title": "SECURITY QUESTION", "test": "Please select a security question. This question will help us verify your identity should you forget your Client Office password or request withdrawals.", "label1": "QUESTION 1 *", "label2": "QUESTION 2 *", "label3": "QUESTION 3 *", "test1": "Enter Your Answer"}, "account_specification": {"label1": "BASE CURRENCY", "label2": "ACCOUNT TYPE", "label3": "LEVERAGE ", "label4": "COMMENT", "label5": "Client Agreement", "test1": "MYFX Markets Client Agreement", "test2": "Markets Disclosure Statement", "test3": "By ticking this box, You are agreeing that you have read and understood MYFX Markets’s Client Agreement, Disclosure Statement and that you understand the nature and risks of Margined Products."}, "radioText": "Leverage of 500:1 is available for account balances below 50,000 USD. Once the balance exceeds 50,000 USD, the leverage will automatically be adjusted to 400:1.", "radioText1000": "Leverage of 1000:1 is available for account balances below 7,000 USD. Once the balance exceeds 7,000 USD, the leverage will automatically be adjusted to 500:1.", "next": "Next", "submit": "Submit", "successMsg": "You have already setup your security question.", "month": "Month", "year": "Year", "day": "Day", "close": "Close", "complete": "Confirm", "StepOneForm": {"label1": "Chinese name", "label1_error1": "Please enter your Chinese name", "label1_error2": "Please enter Chinese characters", "label1_error3": "At least 2 Chinese characters", "label1_error4": "Please enter your name", "label1_error5": "At least 2 characters", "label1_placeholder": "Please enter your Chinese name", "label2": "ID number", "label2_error1": "Please enter your ID number", "label2_error2": "Please enter a valid ID number", "label2_placeholder": "Please enter your ID number", "label3": "Phone number", "label3_error1": "Please enter phone number", "label3_error2": "Please enter a valid number", "label3_error3": "Please enter 7-14 digits", "label3_placeholder": "Please enter phone number", "label4": "IB Code", "label4_error1": "Please enter letters or numbers", "label4_placeholder": "Please enter IB Code", "label5": "Where did you hear about MYFX MARKETS", "label5_error1": "Please select where you heard about MYFX MARKETS", "label5_placeholder": "Please select where you heard about MYFX MARKETS", "label6": "Please enter where you heard about MYFX MARKETS", "label6_error1": "Please enter<PERSON>lease enter where you heard about MYFX MARKETS", "label6_placeholder": "Please enter<PERSON>lease enter where you heard about MYFX MARKETS", "label7": "Full Home Address", "label7_error1": "Please enter your address", "label7_error2": "Please enter English letters", "label7_placeholder": "Please enter your address", "label8": "Date of Birth", "label8_error1": "Please select your date of birth", "label8_error2": "Registration is not allowed if you are under 18 years old", "label8_placeholder": "Please select your date of birth", "label9": "Company Name", "label9_error1": "Please enter your company name", "label9_error2": "Please enter English characters", "label9_placeholder": "Please enter your company name", "label10": "Company Registration Number", "label10_error1": "Please enter your company registration number", "label10_placeholder": "Please enter your company registration number", "label11": "What is your Trading Experience", "label11_error1": "Please select your trading experience", "label11_placeholder": "Please select your trading experience", "label12": "Password", "label12_error1": "Please enter your password", "label12_error2": "At least one uppercase letter", "label12_error3": "At least one lowercase letter", "label12_error4": "At least one number", "label12_error5": "At least eight characters", "label12_error6": "Must be at least 8 characters long and contain a combination of uppercase and lowercase letters and numbers", "label12_placeholder": "Please enter your password", "label13": "Average number of trades per month", "label14": "Average volume per month", "label15": "What products you trade with", "label16": "Expected initial deposit", "label17": "Expected trade volume per month", "label18": "Notes for things to be aware of", "label18_placeholder": "Please enter the precautions", "label19": "MYFX Markets Mauritius Client Agreement:", "label20": "MYFX Markets Mauritius Execution Policy:", "label21": "MYFX Markets Mauritius Complaints Policy:", "label19_error": "Please check the agreement"}, "StepTwoForm": {"label1": "Question 1", "label1_error1": "Please choose a question", "label1_error2": "Do not select duplicate questions", "label1_error3": "Please enter the answer to the question", "label1_placeholder1": "Please select question 1", "label1_placeholder2": "Please enter your answer to question 1", "label2": "Question 2", "label2_error1": "Please choose a question", "label2_error2": "Do not select duplicate questions", "label2_error3": "Please enter the answer to the question", "label2_placeholder1": "Please select question 2", "label2_placeholder2": "Please enter your answer to question 2", "label3": "Question 3", "label3_error1": "Please choose a question", "label3_error2": "Do not select duplicate questions", "label3_error3": "Please enter the answer to the question", "label3_placeholder1": "Please select question 3", "label3_placeholder2": "Please enter your answer to question 3"}, "StepThreeForm": {"label1": "Account currency type", "label1_error1": "Please select the account currency type", "label1_placeholder": "Please select the account currency type", "label2": "Account type", "label2_error1": "Please select account type", "label2_placeholder": "Please select account type", "label3": "Leverage", "label3_error1": "Please select leverage", "label3_error2": "Agree and check this agreement", "label3_placeholder": "Please select leverage", "label4": "Comment", "label4_error1": "Please enter a comment", "label4_placeholder": "Please enter a comment", "label5": "Customer Agreement", "label5_error1": "Please read and agree to the agreement", "label5_error2": "Please confirm that you agree to the agreement"}}, "successPage": {"content": "You have already setup your security question."}, "errorPage": {"back": "Back To Home Page"}, "questionnaire": {"title": "Join MYFX Markets as a Referral Partner", "label1": "What is the primary source region for your referred clients?", "label2": "What methods do you use to acquire the clients you refer? (Please note that a risk disclaimer is required for your website and social media.)", "label3": "Are/Were you an IB or such for any other broker? If yes, please provide the name of the broker/s", "label4": "On a monthly basis, how many clients have/will you introduce?", "label5": "On average, what size of deposit do you expect your clients to make(in USD currency)?", "label6": "What currency would you prefer to receive Rebate in?", "agreeTitle": "I acknowledge and agree:", "agree1": "I understand and agree to the terms and conditions of ", "agree2": "I agree and understand that as a Partner, I will be paid for FX and Gold trading, USD $2 per closed trade (round trip) on Pro USD accounts and 0.5pips per closed trade (round trip) on Standard accounts. Non USD accounts, rates vary", "agree3": "I confirm that all the information provided in this form is true, accurate, and complete.", "agree4": "I commit to promptly notifying MYFX Markets of any updates or modifications to the information provided above.", "agree5": "By registering as a partner, I acknowledge and confirm that I am in compliance with all applicable laws in my jurisdiction and will not engage in any solicitation activities in prohibited jurisdictions.", "agree6": "I declare that by registering as a Partner, I agree that I am not contravening any laws in my jurisdiction and will not solicit in prohibited jurisdictions", "send": "SEND", "validate": "This field is required.", "successInfo": {"title": "Welcome to MYFX Markets", "desc1": "Your application is being reviewed, we will get in touch with you as soon as your application is processed.", "desc2": "If you have any further questions, please feel free to contact our <a href=\"mailto:<EMAIL>\"><EMAIL></a>."}, "errorInfo": {"title1": "", "title2": ""}}, "InternationalBankWire": {"BankName": "Bank Name", "BankAddress": "Bank Address", "AccountName": "Account Name", "AccountNumber": "IBAN Account Number", "BeneficiaryBankAddress": "Beneficiary Bank Address", "BICSWIFT": "BIC/SWIFT", "BSBNumber": "BSB Number", "Remarks": "Remarks", "MT4_MT5_Login": "MT4・MT5 Enter your login number.", "mauritiusTableData": {"AccountName": "Account Name", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "SwiftCode": "Swift Code", "IBAN": "IBAN", "AccountHolderAddress": "Account Holder Address", "BankOneAddress": "Bank One Address"}}, "JPDeposit": {"hint1": "Your application for domestic Japanese yen bank remittance has been accepted.", "hint1_1": "Please transfer to the following bank account. Details can also be found in the email \"bank transfer application procedure completion\".", "hint2": "※ When applying for a deposit,<span style=\"color: red; font-weight: bold\"> be sure to include your billing number in front of the name of the person requesting the transfer.</span>", "hint3": "※ The bank account details are issued each time you apply for a deposit, so they cannot be used for the next transfer.", "hint4": "Please make a deposit application every time.", "hint5": "If you forget to include the invoice number before the name of the remitter, please contact our customer support.", "hint6": "※ Confirmation of payment usually takes about 30 minutes after the transfer procedure is completed. Depending on the timing of interbank transfers, it may take longer to reflect.", "hint7": "※ Maintenance will be conducted by the bank from 21:00 on Sunday to the following morning (Japan time). Payments made during this period will be reflected after 09:00 on Monday (Japan time).", "BankName": "Bank Name", "BranchName": "Branch Name", "BranchServiceNumber": "Branch Service Number", "AccountName": "Account Name", "AccountType": "Account Type", "AccountNumber": "Account Number", "BillingNumber": "Billing Number", "BillingNumberHint": "Please write it before the name of the remittance request.", "Amount": "Amount", "TradingAccountNumber": "Trading Account Number", "BillingNumberDes": "<span style=\"color: red; font-size: 12px\">Please write the remitter's name before the transfer (e.g., 12345 Tanaka Taro). If you forget to do so, please contact our customer support.</span>"}, "bank": {"label1": "Bank Name", "label2": "Bank Code", "label3": "Branch Name*", "label4": "Branch Number/BSB/Sort Code *", "label5": "Beneficiary Account Name(in Halfwidth KATAKANA)", "label6": "Account Number", "label7": "Account Type", "add": "Add"}, "loginBtn": {"btn1": "Back to old version", "btn2": "Register an account"}, "DepositsSuccess": "Success", "MAM_PAMM": {"type1": "Partial", "type2": "All", "submitSuccess": "The withdrawal application has been completed.", "hint_01": "Withdrawn from the MAM/PAMM account to the MYFX wallet will be approved by the operator.", "hint_02": "Depending on the status of the transaction, it may be postponed.", "hint_03": "If there is a position in the trade, the withdrawal will be made after closing that position. In that case, the balance will change after the settlement, so the withdrawal amount may be different from the amount entered below."}, "lpoa": {"label1": "Signature", "label3": "Signature", "placeholder1": "Same as your identity.", "label2": "Full name", "label4": "Full name", "tips": "You may have to wait 1 to 2 minutes", "button1": "I agree", "error1": "The signatures are inconsistent.Please request a re-signature", "signSuccess": {"title": "Thank you for signing up a new LPOA contract", "desc1": "Your new LPOA contact wille be sent to you by email shortly.", "desc2": "If you have any questions, please contact our Customer Support at. "}}, "systemError": {"err_01": "An error has occurred!", "err_02": "Oops! This page was not found.", "err_03": "The requested page does not exist."}, "securityDialog": {"title": "Important Security Message!", "content": "Due to security concerns, we would like to ask all our clients to setup a set of security questions immediately that will prompted for when using requesting withdrawal and password change. Please head to the setup page for your security questions. If you have any questions, please feel free to contact customer service via email or live chat. Thank you!", "button": "Go to Setup Page Now"}, "login_param": {"resetCOPassWdSuccess": {"01": "Your password has been changed.", "02": "Log in again with your new password."}, "locked": {"01": "Your account has been temporarily locked due to multiple incorrect password attempts. Please try logging in again after some time. If you require immediate assistance, <NAME_EMAIL>."}, "sqLocked": {"01": "{email} Your account has been locked due to multiple incorrect answer of sequrity question attempts. <NAME_EMAIL> to unlock."}}, "DRIVER": {"start": "start", "prev": "prev", "next": "next", "done": "done", "stepShowTitle": "Welcome!", "stepShowContent": "If this is your first time with us, we recommend that you take a short introductory tour, which will help you to navigate more quickly in the hub.", "step1Title": "Dashboard", "step1Content": "You can check your account performance, transaction history, and any recent news or promotion.", "step2Title": "My Account- Account Information", "step2Content": "Manage your live trading account here.You can view your account information, change your trading account password, and more.", "step3Title": "My Account - My Document", "step3Content": "You can upload your identification documents for verification purposes.", "step4Title": "My Account - Reset CO Password", "step4Content": "Change the password for your Client Office.", "step5Title": "My Account - Archive Trade History", "step5Content": "You can download the transaction history of the account that has been transitioned to archive status.", "step6Title": "Account Transaction", "step6Content": "Deposit funds into your account, transfer money between your trading account, or submit withdraw request.", "step7Title": "<PERSON><PERSON>", "step7Content": "For clients utilizing MAM/PAMM accounts, funds will be routed through this channel for withdrawals and internal fund transfers.", "step8Title": "Open an Additional Account", "step8Content": "If you wish to open additional accounts, submit your application through this page.", "step9Title": "Platform", "step9Content": "Download your MT4/MT5 platform here.", "step10Title": "News", "step10Content": "View the news from us, promotion information, and Ex-Divdend information.", "step11Title": "Email Us", "step11Content": "Send your question, concerns or comments here.", "step12Title": "Official Website", "step12Content": "Move to our official Website.", "step13Title": "Back to Old Version", "step13Content": "You can use our old version of Client Office for a while."}, "Campaign": {"title_01": "New Official Website and Client Office Launch!", "more": "MORE"}, "InviteFriends": {"inviteBtn": "Invite Friends", "link": "what is refer a friend program?", "title": "Invite your friends", "desc": {"desc1": "Please enter customer details.", "desc2": "We will send an email to the email address.", "desc3": "Your information will be sent only to the account opening URL."}, "form": {"name": "Your Firend's Name", "email": "Email", "message": "message", "language": {"title": "Language", "english": "English", "japanese": "Japanese"}, "placeholder1": "Please enter", "placeholder2": "Please select"}, "validate": {"name": "Please enter your name", "email": "Please enter your email address", "emailFormat": "Please enter it in the correct format", "emailSame": "What is the process of logging in from scratch"}, "confirm": "confirm", "success": "Success", "submitSuccess": "Submitted successfully", "back": "Back"}, "Version": {"title": "Update Notice", "button": "Update Now", "hint": "Please click \"Update Now\". If this message appears again after clicking, please try restarting your browser or logging in to the client office again."}, "LPOAUpdateDialog": {"title": "Limited Power of Attorney (LPOA)", "alert": "We have updated the Limited Power of Attorney (LPOA). Please review and click 'Agree' to continue using our services.", "hint": "MYFX Markets has added new item to the Limited Power of Attorney (LPOA) that customers agree to when opening MAM/PAMM accounts. While this does not affect the usage of MAM/PAMM services, it aims to make it easier for customers to understand what they can expect when using the services.", "warn": "Please note that there are no changes to the strategy name and performance fee for the MAM/PAMM you are participating in, as you have already agreed to the existing terms.", "subHint_01": "If you do not agree to the Limited Power of Attorney (LPOA),", "subHint_02": "you will no longer be able to use the MAM/PAMM service. In this case, please send an email from your registered email <NAME_EMAIL> stating that you do not agree to the changes. We will then proceed to close your MAM/PAMM account and transfer your funds to your MYFX Wallet.", "subHint_03": "", "button": "Agree"}, "promotion": {"tab": {"all": "All", "inProcess": "In Progress", "expired": "Expired"}, "notHave": "There are currently no events available", "activityList": {"mainTitle": "My Promotions", "title1": "Get Up to $3,000 Cashback! <br> Boost Your Investment Journey with Us!", "content1": "Cashback for everyone who meets the conditions! <br>Get cashback starting from just 3 lots!", "title2": "Collect Lucky Tickets and Receive a Mystery Gift!", "content2": "Join our promo for a chance to win mystery gift", "title3": "Get up to $500 Bonus for Your First Deposit!", "content3": "Cashback for everyone who meets the conditions! <br>Get cashback starting from just 3 lots!", "title4": "Get up to $400 Bonus on Your First Deposit at MYFX Markets!!", "content4": "• <span style=\"font-weight: bold\">Sign Up</span>: Create your account.<br>\n • <span style=\"font-weight: bold\">Deposit</span>: Make your first deposit.<br>\n • <span style=\"font-weight: bold\">Get Bonus</span>: 50% bonus up to $400!", "content4_100": "• <span style=\"font-weight: bold\">Sign Up</span>: Create your account.<br>\n • <span style=\"font-weight: bold\">Deposit</span>: Make your first deposit.<br>\n • <span style=\"font-weight: bold\">Get Bonus</span>: 100% bonus up to $400!", "permission": "NEW CLIENTS ONLY", "viewBtn": "VIEW PROMOTION", "endDay": "Offer expires in <span style=\"color: red; font-weight: 700\">{day} days</span>", "buttonText1": "VIEW PROMOTION", "buttonText2": "DEPOSIT", "buttonText3": "VIEW MORE"}, "verify": {"title": "Account Verification Needed", "desc": "To join the promotion, please verify your account. Provide the necessary identification and proof of address to complete the process.", "button": "Verify Now!"}, "up600": {"hint": {"content": "I agree to the <a target=\"_blank\" href=\"https://myfx-static-assets.s3.us-west-2.amazonaws.com/webasset/Welcome+Bonus+Promotion-Term+of+Use.pdf\">terms and conditions</a>.", "confirm": "Submit", "cancel": "Cancel"}, "isok_no": "Complete the KYC now to unlock the access.", "isok_no_title": "Account Verification Needed", "isok_no_button": "Verify Now!", "button": "Join Now", "button_ok": "Joined", "account": "Your trading account", "title": "How to get Welcome Bonus", "list": ["Click the \"Join Now\" button to join the promotion.", "Select the account number you wish to participate with.", "Tick the box to agree to the terms and conditions.", "Make a deposit.", "Bonuses are credited to your account within 1-2 business days after the \n deposit", "<span style=\"color: red;\",>*</span>Be sure to click the \"Join Now!\" button to participate in the promotion before making any deposit. Once you've successfully joined, the button will change to \"Joined.\""], "footer": "For more details please visit the <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/welcome-bonus\"><u>promotion page</u></a>.", "note": "Note: *The 50% bonus applies only to your <span style=\"color: red\">initial deposit</span>.", "note_100": "Note: *The 100% bonus applies only to your <span style=\"color: red\">initial deposit</span>.", "grid": ["<PERSON><PERSON><PERSON><PERSON>", "Bonus"]}, "halloween": {"viewBtn": "View Promotion", "title": "Halloween season with a special, limited-time promotion!", "tag": "Existing Clients", "desc": " • <span style=\"font-weight: bold\">Follow：</span>Follow MYFX Markets' official X account<br/>\n • <span style=\"font-weight: bold\">Share：</span>Quote post with your costume ideas<br/> • <span style=\"font-weight: bold\">Get Prize：</span>Get a chance to win $50!", "info": {"title": "Promotion Details🎃", "list": ["Follow MYFX Markets's official X (formerly Twitter) account <a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\" style=\"text-decoration: underline\">\"@MYFXMarketsEN\"</a>.", "Quote the promotion post with your costume idea and the hashtag \"#MYFXMarkets\".", "Chance to win $50 USD (or equivalent currency)! Ten lucky winners will be selected!"], "desc": ["📅 Promotion Period", "From October 10, 2024 (Monday) to October 31, 2024 (Tue), 23:59 (MT4/MT5 Time).", "⏳ Prize Distribution Schedule", "November 5, 2024 (Tue): Winners will be notified via DM.", "November 10, 2024 (Sun): Submit your details by 23:59 (MT4/MT5 Time).", "November 12, 2024 (<PERSON><PERSON>): Prizes will be reflected in your MYFX Markets account.", "Please see more information on the <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/halloween-2024/\">Promotion page</a>."], "button": "<a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\">Follow X account Now!</a>"}}, "blackFriday": {"viewBtn": "View Promotion", "title": "Black Friday season with a special,limited-time promotion!", "tag": "Existing Clients", "desc": " • <span style=\"font-weight: bold\">Follow：</span>Follow MYFX Markets' official X account<br/>\n • <span style=\"font-weight: bold\">Share：</span>Quote post with the items you want to buy most<br/> • <span style=\"font-weight: bold\">Get Prize：</span>Get a chance to win $50!", "info": {"title": "Promotion Details<span style=\"font-family: 'Segoe UI Emoji'\">🛍</span>", "list": ["Follow MYFX Markets's official X (formerly Twitter) account <a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\" style=\"text-decoration: underline\">\"@MYFXMarketsEN\"</a>.", "Quote the promotion post with your want to buy most and the hashtag \"#MYFXMarkets\".", "Chance to win $50 USD (or equivalent currency)! Ten lucky winners will be selected!"], "desc": ["📅 Promotion Period", "From November 25, 2024 (Monday) to November 29, 2024 (Friday), 23:59 (MT4/MT5 Time).", "⏳ Prize Distribution Schedule", "December 6, 2024 (Tue): Winners will be notified via DM.", "December 10, 2024 (Sun): Submit your details by 23:59 (MT4/MT5 Time).", "December 12, 2024 (<PERSON><PERSON>): Prizes will be reflected in your MYFX Markets account.", "Please see more information on the <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/black-friday-2024/\">Promotion page</a>."], "button": "<a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\">Follow X account Now!</a>"}}, "cyberMonday": {"viewBtn": "View Promotion", "title": "Cyber Monday season with a special, limited-time promotion!", "tag": "Existing Clients", "desc": " • <span style=\"font-weight: bold\">Follow：</span>Follow MYFX Markets' official X account<br/>\n • <span style=\"font-weight: bold\">Share：</span>Quote post with the items you want to buy most<br/> • <span style=\"font-weight: bold\">Get Prize：</span>Get a chance to win $50!", "info": {"title": "Promotion Details <span style=\"font-family: 'Segoe UI Emoji'\">🛍</span>", "list": ["Follow MYFX Markets's official X (formerly Twitter) account <a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\" style=\"text-decoration: underline\">\"@MYFXMarketsEN\"</a>.", "Quote the promotion post with your want to buy most and the hashtag \"#MYFXMarkets\".", "Chance to win $50 USD (or equivalent currency)! Ten lucky winners will be selected!"], "desc": ["📅 Promotion Period", "From November 30, 2024 (Saturday) to December 3, 2024 (Tuesday), 23:59 (MT4/MT5 Time).", "⏳ Prize Distribution Schedule", "December 6, 2024 (Fri): Winners will be notified via DM.", "December 10, 2024 (<PERSON><PERSON>): Submit your details by 23:59 (MT4/MT5 Time).", "December 12, 2024 (Thu): Prizes will be reflected in your MYFX Markets account.", "Please see more information on the <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/cyber-monday-2024/\">Promotion page</a>."], "button": "<a target=\"_blank\" href=\"https://x.com/MYFXMarketsEN\">Follow X account Now!</a>"}}, "christmas": {"viewBtn": "View Promotion", "title": "A limited time campaign is held in the Christmas season!", "tag": "Existing Clients", "desc": " • <span style=\"font-weight: bold\">Application：</span>enter the required form of the campaign page and submit it.<br/>\n • <span style=\"font-weight: bold\">Payment and transaction：</span>deposit more than 30000 yen in the period and deal more than 5 lots.<br/> • <span style=\"font-weight: bold\">Gift acquisition：</span>A Christmas gift such as domestic travel is a lottery.", "info": {"title": "🎄 Campaign contents 🎁", "list": ["Application form of step 1 of the campaign page", "At least 30000 yen or more in the period, and at least 5 lots or more.", "Gifts for a total of 23 people."], "desc": ["📅 Promotion Period", "From December 9, 2024 to December 27 (Friday) 23:59", "⏳ Lottery & gift delivery schedule", "In the middle of January 2025, we will announce the winner to the winner and announce the winner in the web site", "Gift shipment to winners in late 2025", "Please see more information on the <a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/christmas2024/\">Promotion page</a>."], "button": "<a target=\"_blank\" href=\"https://myfxmarkets.com/promotion/christmas2024/\">Apply now!</a>"}}, "newYear2025": {"viewBtn": "View Promotion", "title": "Welcome the New Year! We are holding a limited-time promotional campaign!", "tag": "Existing Customers", "desc": " • <span style=\"font-weight: bold\">Application:</span> Fill in the required information in the application form on the campaign page and submit it.<br/>\n • <span style=\"font-weight: bold\">Deposit & Trading:</span> Deposit over 5000 yen and trade more than 0.5 lots within the period.<br/> • <span style=\"font-weight: bold\">Get Red Packet!</span> Receive cash back according to the number of lots traded!", "info": {"title": "Campaign Details 🎉", "list": ["Fill in the required information in the application form on the campaign page 'Step 1' to apply.", "Deposit a minimum of 5000 yen or 50 dollars and trade a minimum of 0.5 lots within the period.", "Receive a red packet (cash back) for <span style=\"font-weight: bold\">all eligible participants</span> based on the number of lots traded!"], "desc": ["📅 Campaign Period", "From January 2, 2025 (Thursday) to January 31, 2025 (Friday) 23:59 (*MT4/MT5 time applies).", "⏳ Cash Back Schedule", "Cash back will be credited to participating accounts around mid-February 2025.", "For more details, please check the <a target=\"_blank\" href=\"https://myfxmarkets.com/ja/promotion/otoshidama2025/\">campaign page</a>."], "button": "<a target=\"_blank\" href=\"https://myfxmarkets.com/ja/promotion/otoshidama2025/\">Apply Now!</a>"}}, "line1000": {"viewBtn": "View Promotion", "title": "To commemorate surpassing 1,000 LINE friends, we are holding a special bonus campaign!", "tag": "Existing Customers", "desc": "To celebrate surpassing 1,000 friends on our official LINE account, we are offering a 3,000 yen trading bonus to customers who deposit 10,000 yen or more during the campaign period.", "info": {"title": "Campaign Details/How to Participate", "list": ["To celebrate surpassing 1,000 friends on our official LINE account, we are offering a 3,000 yen trading bonus to customers who deposit 10,000 yen or more during the campaign period.", "【How to Participate】", "Log in to Client Office and deposit 10,000 yen or more.", "Send your login number via LINE", "⚠️Notes:", "Limited to one use per person.", "The bonus expires in 30 days.", "In order to withdraw more than the amount deposited, you will need to have held the funds for at least 10 minutes and traded at least 5 times in total for a total of 2 lots.", "The 3,000 yen trade bonus will be void when transferring or withdrawing funds.", "Corporate accounts, MAM/PAMM accounts, and Micro accounts are not eligible for this campaign.", "If your account balance becomes negative, the bonus will be voided when the zero cut system is applied.", "Before participating in the campaign, please be sure to read our<a target=\"_blank\" href=\"https://myfxmarkets.com/wp-content/uploads/2025/03/%E3%80%90%E5%88%A9%E7%94%A8%E8%A6%8F%E7%B4%84%E3%80%91LINE%E3%81%8A%E5%8F%8B%E9%81%94%E7%99%BB%E9%8C%B21000%E4%BA%BA%E7%AA%81%E7%A0%B4%E8%A8%98%E5%BF%B5%E3%83%9C%E3%83%BC%E3%83%8A%E3%82%B9.pdf\">Terms of Use</a>and confirm the contents."], "desc": ["📅 Campaign Period", "From March 11, 2025 (Tuesday) to March 13, 2025 (Thursday) 23:59 (MT4/MT5 time)", "🔍 Eligibility", "Customers who meet the following conditions during the campaign period are eligible, whether new or existing.", "Must have a trading account with MYFX Markets", "Must be registered as a friend on the official MYFX Markets LINE account", "【MYFX Markets LINE Official Account Information】", "LINE ID: @myfxmarkets", "Friend addition link: <a href=\"https://lin.ee/TMuvgP5X\" target=\"_blank\">https://lin.ee/TMuvgP5X</a>", "※ You can also register as a friend via QR code."], "button": "<a target=\"_blank\" href=\"https://myfxmarkets.com\">Apply Now!</a>"}}, "cashback2025May": {"title": "Summer Cash Frenzy Campaign – Receive up to $3000!", "desc": "We’re excited to announce a Summer Cash Frenzy campaign!<br>During the campaign period, you’ll get rewarded as you trade (excluding USD/JPY pairs and Gold) on our MT5 Standard or Pro accounts.<br>Receive a cashback reward of up to $3,000.", "info": {"desc": ["📅 Campaign Period", "May 12, 2025 at 9:00 AM – July 4, 2025 at 11:59 PM ", "*MT4/MT5 server time applies", "🔍 Eligible Accounts", "MT5 Standard and Pro accounts", "<span style=\"color: red\">*MT4 accounts, Micro accounts, Corporate accounts, and MAM/PAMM accounts are not eligible.</span>", "📈 Eligible Trades", "FX excluding USD/JPY and precious metals excluding Gold.", "*Only trades that maintain open positions for <span style=\"color: red\">a minimum of 10 minutes</span> will be considered eligible.", "💵 Minimum Deposit Amount", "<span style=\"color: red\">A minimum deposit of $300 USD/$300 AUD is required to participate in this campaign.</span>", "*Trades will only be eligible for this campaign once the deposit has been successfully credited.", "👩 Eligible Participants 🧑", "Existing customers who deposit and trade using MT5 Standard or Pro accounts during the campaign period.", "New customers who open an MT5 Standard or Pro account, make a deposit, and place trades during the campaign period.", "✍️ How to Participate", "1) Click the \"Join Now\" button below to join the campaign.", "2) Make a deposit of at least $300 USD/$300 AUD.", "3) Start trading!", "*Please note that this campaign is only applicable to MT5 Standard and MT5 Pro accounts. Even if you already hold an account with us, you will need to open an additional MT5 account if you do not currently have one.", "⚠️Campaign Rules & Terms", "Only accounts that satisfy all of the following conditions during the campaign period shall be considered eligible:", "Submission of the official application form.", "A deposit of at least $300 USD/$300 AUD.", "Execution of new trades totaling at least 1 lot in the eligible currency pairs after the deposit has been credited.", "Internal fund transfers from other accounts will not be regarded as qualifying deposits.", "The total trading volume will be calculated on a per-account basis, not per individual.", "Cashback received under this campaign cannot be transferred between accounts or withdrawn for a period of 30 days following the date of receipt.", "Open positions at the end of the campaign period will not be counted toward the total trading volume.\n", "This campaign cannot be combined with any other ongoing promotions.", "Please read and confirm <a style=\"text-decoration: underline;\" href=\"https://myfxmarkets.com/wp-content/uploads/2025/05/Terms-and-Conditions-MYFX-Markets-Summer-Cash-Frenzy-Campaign_2.pdf\" target=\"_blank\">MYFX Markets' Terms of Use</a> before participating.", "※For more details including the cashback amount list, please visit <a style=\"text-decoration: underline;\" href=\"https://myfxmarkets.com/promotion/summer-cash-frenzy2025/\" target=\"_blank\">promotion page</a> on our official website."], "joinBtn": "Join Now!", "joinHit": "This account has already participated.", "agreeContent": "I agree to the <a target=\"_blank\" href=\"https://myfxmarkets.com/wp-content/uploads/2025/05/Terms-and-Conditions-MYFX-Markets-Summer-Cash-Frenzy-Campaign.pdf\">terms and conditions</a>.", "hint1": "*Please note that this campaign is only applicable to MT5 Standard and MT5 Pro accounts. Even if you already hold an account with us, you will need to open an additional MT5 account if you do not currently have one.", "hint2": "*The total trading volume will be calculated on a per-account basis, not per individual.", "hint3": "*Accounts that currently hold a bonus are not eligible for this campaign.", "account": "Trading Account Number"}, "selectAll": "Select All", "selectLabel": "Eligible Accounts for the campaign", "accountEmptyDesc": "No selectable accounts available"}}, "PromotionInfo": {"title": "My Promotion", "activityTime": "Offer expires in <span style=\"color: red;\">{day} days</span>", "totalLots": "Total Lots", "promotionPeriod": "Promotion Period", "today": "Today", "howToGetCashback": "How to get Cashback", "rule01": "Minimum Deposit $300", "rule02": "Trade more than 3 lots", "rule03": "Trade FX and/or Metals", "ruleMore": "For more details please visit the {0}", "ruleMoreLink": "promotion page", "lots": "Lots", "cashback": "Cashback", "lostList": "More than {count} lots", "cashbackList": "${count}(or equivalent currency)", "tradingAccount": "Your trading account:"}, "promotionInfo2": {"title": "My Promotion", "tips": {"title": "How to get Lucky Tickets", "content1": "Share and like the promotion post in your Instagram stories", "content2": "Share and like the promotion post on Facebook", "content3": "Share onX (Twiter)", "content4": "Repost and like the promotion post onX", "content5": "Trade2 lots", "content6": "Deposit $100", "more": "For more details please visit the ", "link": "promotion page"}, "endDay": "Offer Ends in <span style=\"color: red; font-weight: 700\">{day} days</span>", "tickets": "You have <span style=\"color: red\">{tickets}</span> Tickets Now!", "totalLots": "Total Lots", "period": "Promotion Period", "today": "Today", "shareInstagam": "Share on instagam", "shareFacebook": "Share on Facebook", "shareTwiter": "Share on X(<PERSON><PERSON><PERSON>)", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "becomeIBOnline": {"title": "Join MYFX Markets as a Referral Partner", "desc1": "Our seamless application process will provide you with a unique referral link, and our comprehensive reporting system will assist you in converting more clients and tracking your earnings.", "desc2": "For any inquiries, please feel free to reach out to our <a href=\"https://static.zdassets.com/web_widget/latest/liveChat.html?v=10#key=myfxmarkets.zendesk.com/&lang=en\" target=\"_blank\" style=\"color: #495eeb\">24/7 </a> customer support.", "desc3": "Embark on a rewarding partnership with MYFX Markets Today!", "checkBtn": "Check Email", "placeholder": "Our CO Email", "validate1": "Email cannot be empty", "validate2": "Please enter the correct email format"}, "profile": {"accountType": "Account Type", "director": "Director", "company": "Company", "individual": "Individual", "personalInformation": "Personal Information", "dateOfBirth": "Date of Birth", "countryOfResidence": "Country of Residence", "residentialAddress": "Residential Address", "contactInformation": "Contact Information", "email": "Email", "phoneNumber": "Phone Number", "identification": "Identification", "status": "Status", "verified": "Verified", "pendingVerification": "Pending Verification", "hint": "*Please contact us using your registered email address for inquiries about changing your registration information.<br/><a href=\"mailto:<EMAIL>\"><EMAIL></a>"}, "tradingTools": {"analysisIQ": {"subscribe": "Subscribe"}, "download": {"socialLinks": "Social Links", "userGuide": "Acuity User Guide", "downloadText": "Download PDF to view", "notice": "Push Notifications", "riskWarning": {"title": "Risk Warning：", "list": ["The market signals on this page are provided for informational purposes only and should not be considered advice or recommendations from MYFX Markets.", "Trade ideas are provided by Acuity Research Limited, a firm authorised and regulated by the FCA (Company Number: 07428345).", "Anyone choosing to use Acuity Trading Signals assumes full responsibility for their actions. We strongly recommend conducting thorough due diligence on any information obtained from this page before engaging in trading activities."]}}, "webinars": {"title": "Webinars", "desc1": "Each of our partner commentators has a proven public track record in the market trading industry.", "desc2": "Through meticulous selection, we are proud to showcase a panel of market experts to support your trading journey, deepen your market knowledge and provide value and insights not available anywhere else!", "placeholder": {"desc1": "New commentator", "desc2": "Join us soon"}, "registerNow": "Register Now", "joined": "Joined", "joinHint": "Hint", "joinMessage": "<div>Successful registration！</div><div><a href=\"{webinarUrl}\" target=\"_blank\">{webinarUrl}</a></div>"}}}