import Vue from 'vue'
import VueI18n from 'vue-i18n'
import enLocale from 'element-ui/lib/locale/lang/en'
import jaLocale from 'element-ui/lib/locale/lang/ja'
import zhCNLocale from 'element-ui/lib/locale/lang/zh-CN'
import zhTwLocale from 'element-ui/lib/locale/lang/zh-TW'
import thLocale from 'element-ui/lib/locale/lang/th'
import viLocale from 'element-ui/lib/locale/lang/vi'
import zhCN from 'wot-design/lib/locale/lang/zh-CN'
import enUS from 'wot-design/lib/locale/lang/en-US'
import veeMessages from '@/locales/vee_validateLocale'

Vue.use(VueI18n)

function loadLocaleMessages () {
  const locales = require.context('./locales', true, /[A-Za-z0-9-_,\s]+\.json$/i)
  const messages = {}
  const en = {
    ...enLocale
  }
  const ja = {
    ...jaLocale
  }
  const elZhCN = {
    ...zhCNLocale
  }
  const zhTW = {
    ...zhTwLocale
  }
  const th = {
    ...thLocale
  }
  const vi = {
    ...viLocale
  }
  locales.keys().forEach(key => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i)
    if (matched && matched.length > 1) {
      const locale = matched[1]
      const message = locales(key)
      // messages[locale] = locales(key)
      if (locale === 'en_US') {
        messages[locale] = { ...message, ...en, ...enUS, ...veeMessages.en_US }
      } else if (locale === 'ja_JP') {
        messages[locale] = { ...message, ...ja, ...enUS, ...veeMessages.ja_JP }
      } else if (locale === 'zh_CN') {
        messages[locale] = { ...message, ...elZhCN, ...zhCN, ...veeMessages.zh_CN }
      } else if (locale === 'zh_TW') {
        messages[locale] = { ...message, ...zhTW, ...enUS, ...veeMessages.zh_TW }
      } else if (locale === 'th') {
        messages[locale] = { ...message, ...th, ...enUS, ...veeMessages.th }
      } else if (locale === 'vi') {
        messages[locale] = { ...message, ...vi, ...enUS, ...veeMessages.vi }
      } else {
        messages[locale] = locales(key)
      }
    }
  })
  return messages
}

// 获取当前系统语言
const getSystemLanguage = () => {
  return navigator.language.replace('-', '_')
}

const languageMap = {
  en_us: 'en_US',
  ja_jp: 'ja_JP',
  ja: 'ja_JP',
  jp: 'ja_JP',
  zh_cn: 'zh_CN',
  zh_tw: 'zh_TW',
  th: 'th',
  vi: 'vi'
}

export default new VueI18n({
  locale: languageMap[getSystemLanguage().toLowerCase()] ?? 'en_US',
  fallbackLocale: languageMap[getSystemLanguage().toLowerCase()] ?? 'en_US',
  messages: loadLocaleMessages()
})
