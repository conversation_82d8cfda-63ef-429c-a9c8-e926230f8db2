<template>
  <!-- 添加一个 div 用作容器 -->
  <div id="grecaptcha"></div>
</template>
<script>
export default {
  props: {
    captchaType: {
      type: String,
      default: 'cloudflare' // 默认使用cloudflare
    }
  },
  data () {
    return {}
  },
  computed: {
    sitekey () {
      // 开发环境使用cloudflare
      if (process.env.NODE_ENV === 'development') {
        return location.origin.includes('myfx.group')
          ? process.env.VUE_APP_INDONESIA_CLOUD_FLARE_SITE_KEY
          : (process.env.VUE_APP_CLOUD_FLARE_SITE_KEY || '0x4AAAAAABgkoiuUbu_9cxnv')
      }

      // 生产环境根据type选择
      if (this.captchaType === 'googleCaptcha') {
        return process.env.VUE_APP_GOOGLE_SITE_KEY || '6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_'
      } else {
        // cloudflare
        return location.origin.includes('myfx.group')
          ? process.env.VUE_APP_INDONESIA_CLOUD_FLARE_SITE_KEY
          : (process.env.VUE_APP_CLOUD_FLARE_SITE_KEY || '0x4AAAAAABggGCrS6o4FSYCK')
      }
    }
  },
  methods: {
    submit: function (token) {
      this.$emit('getValidateCode', token)
    },
    loaded () {
      setTimeout(() => {
        window.grecaptcha.render('#grecaptcha', {
          sitekey: this.sitekey,
          callback: this.submit
        })
      }, 200)
    }
  },
  mounted () {
    this.loaded()
  }
}
</script>
