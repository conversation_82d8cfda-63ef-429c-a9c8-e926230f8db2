<template>
  <div class="card-style4">
    <label class="box">
      <input @change="change" @clear="clear" v-model="selectValue" :disabled="disabled" type="radio" :name="element">
      <div
        class="option"
        :style="{ '--w': val ? '0%' : '100%', '--c': val ? '#485eeb' : '#fff' }"
        v-viewpoint.click="{ message: `[Open Additional Trading Account] Click ${element} ${label}` }"
      >
        <div class="radio-content" style="display: flex; justify-content: center; align-content: center;">
          <div v-if="image" style="height: 15px;width: 15px; display: inline-block; margin: 1px -2px 0 5px">
            <!-- 'img/account/' + 'currency-' +label + '.png' -->
            <el-image style="height: 100%; width: 100%;"
                      :src="`img/openAccount/${label === 'JPY' ? 'JAPAN' : label}-${val === value ? 's' : 'd'}.png`"/>
          </div>
          <span class="radio-title">
            {{ label }}
          </span>
        </div>
      </div>
    </label>
  </div>
</template>

<script>
export default {
  name: 'RadioButton',
  props: {
    label: String,
    value: String,
    element: String,
    val: String,
    disabled: {
      type: Boolean,
      default: false
    },
    image: Boolean
  },
  data () {
    return {
      selectValue: ''
    }
  },
  methods: {
    change () {
      this.selectValue = this.value
      this.$emit('change', this.selectValue === null ? '' : this.selectValue)
    },
    clear () {
      console.log('子----》' + this.selectValue)
      this.selectValue = null
      this.$emit('clear', '')
    }
  }
}
</script>

<style lang="scss" scoped>
label {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

.card-style4 {
  float: left;
  /* width: 100%;
 max-width: 1080px;*/
  margin: 0 auto;
  padding: 0 4px;
}

.card-style4 .title {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 20px;
  text-align: center;
}

.card-style4 input[type="radio"] {
  display: none;
}

.card-style4 .content {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.card-style4 .option {
  width: 100%;
  height: 28px;
  /*margin: 14px;
padding: 8px 15px;*/
  //border: 2px solid #00000050;
  border-radius: 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.card-style4 .option img {
  margin-right: 10px;
}

.card-style4 .radio-content .radio-title {
  display: inline-block;
  color: #485eeb;
  /*font-size: 15px;
font-weight: 500;*/
  margin-left: 10px;
  margin-right: 10px;
  line-height: 22px;
}

.card-style4 .radio-content .radio-title span {
  display: inline-block;
  /*font-size: 24px;*/
  text-transform: capitalize;
  vertical-align: middle;
}

.card-style4 input:checked + .option {
  //border: 2px solid #7382e6;
  //animation: bounceIn 1s;
}

.card-style4 input:disabled + .option {
  //background-color: #e2e3e9;
}

/* Let's write a media query to make it responsive */

@media (max-width: 500px) {
  .card-style4 .radio-content .radio-title {
    font-size: 13px;
  }

  .card-style4 .radio-content .radio-title span {
    font-size: 18px;
  }

  .card-style4 .option {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}


/* Let's create an bounceIn animation */

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    transform: scale3d(0.9, 0.9, 0.9);
  }

  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    transform: scale3d(0.97, 0.97, 0.97);
  }

  100% {
    opacity: 1;
    transform: scaleX(1);
  }
}


/* Let's write a media query to make it responsive */

@media (max-width: 500px) {
  .card-style4 .content .title {
    font-size: 28px;
    line-height: 28px;
  }

  .card-style4 .content .desc {
    font-size: 16px;
    margin-bottom: 20px;
  }
}


// -----------------------
.option {
  overflow: hidden;
  position: relative;
  //transform: translate(2px, -2px);
  //box-shadow: -4px 4px 0px rgba(127, 142, 246, 0.3);
  transition: all .3s;
  border: 1px solid #495DEC;

  //&::before {
  //  content: '';
  //  position: absolute;
  //  top: 0;
  //  left: 0;
  //  width: 0;
  //  height: 100%;
  //  transition: width .3s;
  //  //border-radius: 5px;
  //  background: #475EEB;
  //}

  &:hover::before {
    //width: var(--w);
  }

  .radio-title {
    position: relative;
    z-index: 2;
  }

  &:hover .radio-title {
    //color: var(--c) !important;
  }

  &:active {
    //transform: translate(0, 0);
    //box-shadow: unset;
  }
}

.box input:checked + .option {
  transform: translate(0, 0);
  box-shadow: unset;

  &:hover .radio-title {
    color: #fff !important;
  }
}

</style>
