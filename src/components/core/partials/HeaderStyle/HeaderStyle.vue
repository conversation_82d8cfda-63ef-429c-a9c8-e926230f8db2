<template>
  <div class="iq-top-navbar">
    <div class="iq-navbar-custom">
      <nav class="navbar navbar-expand-lg navbar-light p-0">
        <b-navbar-toggle target="nav-collapse" class="">
          <i @click="toggleNavbar" class="ri-menu-3-line"></i>
        </b-navbar-toggle>
        <b-collapse id="nav-collapse" is-nav>
          <ul class="navbar-nav ml-auto navbar-list">
            <!--            <li class="nav-item toggle-btn">-->
            <!--              <a class="wrapper-menu" @click="sidebarMini">-->
            <!--                <div class="main-circle"><i class="fas fa-chevron-left"></i></div>-->
            <!--                <div class="hover-circle"><i class="fas fa-chevron-right"></i></div>-->
            <!--              </a>-->
            <!--            </li>-->
            <li style="display: flex; justify-content: center; align-items: center">
              <!--              <el-button
                              size="small"
                              style="height: 30px; background-color: #00ff96; border-radius: 15px; border: none; cursor: pointer"
                              @click="goToDeposit">
                              {{ $t('sidebar.deposit') }}
                            </el-button>-->
              <div style="height: 60px;display: flex;align-items: center;justify-content: center;" @click="goToDeposit">
                <!--                <el-button class="blob-btn">-->
                <!--                  {{ $t('sidebar.deposit') }}-->
                <!--                  <span class="blob-btn__inner">-->
                <!--                  <span class="blob-btn__blobs">-->
                <!--                    <span class="blob-btn__blob" style="animation-delay: .2s"></span>-->
                <!--                    <span class="blob-btn__blob" style="animation-delay: .4s"></span>-->
                <!--                    <span class="blob-btn__blob" style="animation-delay: .6s"></span>-->
                <!--                  </span>-->
                <!--                  </span>-->
                <!--                </el-button>-->
                <!--                <button class="deposit-btn" :style="{'&#45;&#45;offset': `${btnOffset}%`}">
                                  <span>{{ $t('sidebar.deposit') }}</span>
                                </button>-->
                <button class="deposit-btn" :style="{width: $i18n.locale === 'ja_JP' ? '140px' : ''}">
                  <span>{{ $t('sidebar.headerBtn') }}</span>
                </button>
              </div>

            </li>
            <!--            <li style="text-align: center" class="nav-item"-->
            <!--                @click="driverFunc({must: true})">-->
            <!--              <a style="text-align: center">-->
            <!--                <i class="el-icon-info"/>-->
            <!--              </a>-->
            <!--            </li>-->
            <li style="text-align: center" v-if="user.isIbAccount==='1'" v-b-tooltip="$t('sidebar.partnerPortal')"
                class="nav-item"
                v-nav-toggle>
              <router-link :to="{ name: 'production.login'}">
                <a style="text-align: center">
                  <i class="fa fa-users"/>
                </a>
              </router-link>
            </li>
            <li class="nav-item" v-nav-toggle>
              <a class="search-toggle iq-waves-effect language-title" href="#">
                <i class="fas fa-globe"></i>
                {{ $store.state['Setting/lang'] && $store.state['Setting/lang'].title }}
                <i class="fas fa-chevron-down"></i></a>
              <div class="iq-sub-dropdown">
                <a class="iq-sub-card" href="javascript:void(0)" v-for="(lang, i) in langsOptions"
                   :key="`Lang${i}`" @click="langChange(lang)">
                  {{ lang.title }}
                </a>
              </div>
            </li>
          </ul>
        </b-collapse>
        <ul class="navbar-list">
          <li class="rounded" v-nav-toggle>
            <a href="#" @click="logout" class="search-toggle iq-waves-effect d-flex align-items-center">
              <i class="ri-login-box-line ml-2"></i>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import { APPNAME, core } from '../../../../config/pluginInit'
import langChange from '../../../../Mixins/langchange'
import { getLocal, removeLocalItem } from '@/Utils/authLocalStorage'
import driverFunc from '@/Utils/driver'
import auth from '../../../../services/auth'

export default {
  name: 'HeaderStyle',
  mixins: [langChange],
  components: {
    // HoverMenu,
    // CollapseMenu
  },
  data () {
    return {
      appName: APPNAME,
      globalSearch: '',
      user: {
        username: '',
        isIbAccount: ''
      },
      isNavbarOpen: false,
      btnOffset: 0
    }
  },
  mounted () {
    setInterval(() => {
      this.btnOffset += 1
    }, 50)

    console.log(getLocal('user'))
    const user = JSON.parse(/* localStorage.getItem('user') */getLocal('user'))
    this.user.username = user.firstname + ' ' + user.lastname
    this.user.firstname = user.firstname
    this.user.isIbAccount = user.isIbAccount

    try {
      const i18n = JSON.parse(localStorage.I18N)
      this.langChange(i18n)
    } catch (err) {
      // const lang = this.getSystemLanguage()
      // const target = this.langsOptions.find(item => item.value === lang)
      // this.langChangeState(target)
      // localStorage.I18N = JSON.stringify(target)
    }
  },
  methods: {
    goToDeposit () {
      this.$router.push('/accountTransactions/transaction')
    },
    driverFunc,

    // 获取当前系统语言
    getSystemLanguage () {
      return navigator.language.replace('-', '_')
    },

    sidebarMini () {
      core.triggerSet()
    },
    toggleNavbar () {
      this.isNavbarOpen = !this.isNavbarOpen
    },
    removeClass () {
      this.showSearch = false
      this.showMenu = ''
      this.globalSearch = ''
      if (document.getElementById('searchbox-datalink') !== null && document.getElementById('searchbox-datalink') !== undefined) {
        document.getElementById('searchbox-datalink').classList.remove('show-data')
      }
    },
    logout () {
      this.$vp.add({ message: '[Header] Click logout.' })

      auth.logout().then(res => {
        removeLocalItem('user')
        removeLocalItem('access_token')
        removeLocalItem('refresh_token')
        removeLocalItem('cashback2025May')
        this.$router.push({ name: 'auth1.sign-in1' })
      })
    },
    ...mapActions({
      langChangeState: 'Setting/setLangAction'
    })

  },

  computed: {

    ...mapGetters({
      langsOptions: 'Setting/langOptionState',
      selectedLang: 'Setting/langState',
      stateUsers: 'Setting/usersState'
    })
  }
}
</script>

<style lang="scss">
*, *:before, *:after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

$openSans: 'Open Sans', Helvetica, Arial, sans-serif;
$cyan: #00ff96;
$dark: #222;
$borderW: 6px;

.blob-btn {
  $numOfBlobs: 3;
  z-index: 1;
  position: relative;
  text-align: center;
  border: none;
  transition: color 0.5s;
  cursor: pointer;
  padding: 8px 15px;
  color: #000000;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 5px -8px rgba(0, 0, 0, 0.3);

  &:before {
    content: "";
    z-index: 1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: 1px solid $cyan;
    border-radius: 15px;
  }

  /*&:after {
    content: "";
    z-index: -2;
    position: absolute;
    left: $borderW*1.5;
    top: $borderW*1.5;
    width: 100%;
    height: 100%;
    border: $borderW solid $dark;
    transition: all 0.3s 0.2s;
  }*/

  &:hover {
    color: $dark;
    background-color: $cyan !important;

    &:after {
      transition: all 0.3s;
      left: 0;
      top: 0;
    }
  }

  &__inner {
    z-index: -1;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  &__blobs {
    position: relative;
    display: block;
    height: 100%;
    filter: url('#goo');
  }

  &__blob {
    position: absolute;
    top: $borderW;
    width: 100% / $numOfBlobs;
    height: 100%;
    background: $cyan;
    border-radius: 100%;
    transform: translate3d(0, 150%, 0) scale(1.7);
    transition: transform 0.45s;
    animation: wave 2s ease infinite alternate;

    @supports (filter: url('#goo')) {
      transform: translate3d(0, 150%, 0) scale(1.4);
    }

    @for $i from 1 through $numOfBlobs {
      &:nth-child(#{$i}) {
        left: ($i - 1) * (120% / $numOfBlobs);
        transition-delay: ($i - 1) * 0.08s;
      }
    }
  }
}

@keyframes wave {
  0% {

  }
  100% {
    transform: translateZ(0) scale(1.7);
    @supports (filter: url('#goo')) {
      transform: translateZ(0) scale(1.1);
    }
  }
}

@media (min-width: 1300px) {
  .toggle-btn {
    display: none;
  }
}

.deposit-btn {
  position: relative;
  padding: 5px 24px;
  min-width: 80px;
  text-align: center;
  border: 0;
  color: #fff;
  border-radius: 20px;
  font-size: 12px;
  overflow: hidden;
  background-color: rgb(3, 255, 150);
  border: 1px solid #00DF81;

  &:hover {
    background: #03FF96;

    &::before {
      content: unset;
    }
  }

  span {
    position: relative;
    z-index: 10;
    color: #0d1350;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: blur(6px);
    background: repeating-radial-gradient(
        circle at left top,
        //#B7DBFF calc(var(--offset) + 30.00% * 1),
        //#AFB8F5 calc(var(--offset) + 30.00% * 2),
        //#B7DBFF calc(var(--offset) + 30.00% * 3),
        //#03FF96 calc(var(--offset) + 30.00% * 4),
        //#D7FFEE calc(var(--offset) + 30.00% * 5),

        #D7FFEE calc(var(--offset) + 30.00% * 1),
        #ABFFDC calc(var(--offset) + 30.00% * 2),
        #03FF96 calc(var(--offset) + 30.00% * 3),
        //#00DF81 calc(var(--offset) + 30.00% * 4),
        #ABFFDC calc(var(--offset) + 30.00% * 4),
    );
  }

  //&::after {
  //  content: '';
  //  position: absolute;
  //  top: 0;
  //  left: 50%;
  //  transform: translate(-50%, 0);
  //  height: 7%;
  //  width: 90%;
  //  border-radius: 50px 50px 0 0;
  //  background: linear-gradient(to bottom, #fff, transparent);
  //}
}

</style>
