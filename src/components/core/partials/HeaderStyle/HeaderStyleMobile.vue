<template>
  <div class="iq-top-navbar">
    <div class="iq-navbar-custom">
      <b-navbar ref="navBarMobile" class="navbar-expand-xl navbar-bg" style="border-radius: 4px;" toggleable="xl "
                type="light">
        <div class="container" style="padding: 10px">
          <b-navbar-brand style="left: 10px;" href="#">
            <!--            <img v-if="screenWidth > 720" style="width: 140px" src="@/assets/images/logo-full.png" alt="logo">-->
            <img style="width: 140px" src="@/assets/images/logo-white.png" alt="logo">
          </b-navbar-brand>
          <!--          <el-button
                      size="small"
                      class="deposit-btn"
                      @click="goToDeposit">
                      {{ $t('sidebar.deposit') }}
                    </el-button>-->
          <div style="height: 60px; right: 8px; position: absolute;display: flex;align-items: center;"
               @click="goToDeposit">
            <!--            <el-button class="blob-btn">-->
            <!--              {{ $t('sidebar.deposit') }}-->
            <!--              <span class="blob-btn__inner">-->
            <!--                  <span class="blob-btn__blobs">-->
            <!--                    <span class="blob-btn__blob" style="animation-delay: .2s"></span>-->
            <!--                    <span class="blob-btn__blob" style="animation-delay: .4s"></span>-->
            <!--                    <span class="blob-btn__blob" style="animation-delay: .6s"></span>-->
            <!--                  </span>-->
            <!--                  </span>-->
            <!--            </el-button>-->
            <!--            <button class="deposit-btn" :style="{'&#45;&#45;offset': `${btnOffset}%`}">
                          <span>{{ $t('sidebar.deposit') }}</span>
                        </button>-->
            <button class="deposit-btn">
              <span>{{ $t('sidebar.headerBtn') }}</span>
            </button>
          </div>
          <!--          <i class="el-icon-info" @click="driverFunc({must: true})"/>-->
          <b-nav-toggle target="navCollapse" style="position: absolute;right: 10px;top: 14px;" @click="toggleNav">
            <span style="display: flex; align-items: center; justify-content: center" v-if="!isMobileNavCollapsed">
              <!-- <i class="fas fa-bars" :style="{color: screenWidth < 720 ?  '#fff' : '#495eeb'}"></i> &lt;!&ndash; 初始图标 &ndash;&gt;-->
              <!--              <i class="fas fa-bars"></i>-->
               <img style="pointer-events: none" width="26" src="@/assets/images/menu/hamburger.png">
            </span>
            <span style="display: flex; align-items: center; justify-content: center" v-else>
<!--              <i class="fas fa-times"></i> &lt;!&ndash; 展开后的图标 &ndash;&gt;-->
              <img style="pointer-events: none" width="26" src="@/assets/images/menu/close.png">
            </span>
          </b-nav-toggle>
          <b-collapse
            id="navCollapse"
            is-nav
            ref="navCollapseMobile"
            v-model="isMobileNavCollapsed"
            @shown="onNavShown"
            @hidden="onNavHidden"
          >
            <b-navbar-nav style="overflow-y: scroll;height: 280px" class="ml-auto driver-mobile-nav">
              <CollapseMenuMobile
                idName="top"
                :items="SideBarItems"
                :open="true"
                @closeNav="closeNav"
              />
            </b-navbar-nav>
            <div class="other">
              <router-link :to="{ name: 'production.login'}">
                <span @click="isMobileNavCollapsed=false">{{ $t('sidebar.partnerPortal') }}</span>
              </router-link>
              <!--              <InviteFriends class="invite1"></InviteFriends>-->
            </div>

            <div class="header-footer">
              <b-dropdown dropright style="display: inline-block;padding: 0;margin: 10px;">
                <template #button-content>
                  <a class="search-toggle iq-waves-effect language-title" href="#">
                    <i class="fas fa-globe"></i>
                    <span>{{ selectedLang && selectedLang.title }}</span>
                  </a>
                </template>
                <b-dropdown-item @click.stop="langChange(lang)" v-for="(lang, i) in langsOptions" href="#">
                  <span>{{ lang.title }}</span>
                </b-dropdown-item>
              </b-dropdown>
              <!--            <router-link :to="{ name: 'production.login'}">
                            <b-button @click="isMobileNavCollapsed=false" style="margin: 10px" variant="primary">
                              <i class="fa fa-users"/><span>{{ $t('sidebar.partnerPortal') }}</span>
                            </b-button>
                          </router-link>-->
              <!--              @click="logout"-->
              <div class="logOut" @click="logout">
                <i class="ri-login-box-line"></i>
                <span>{{ $t('nav.user.signout') }}</span>
              </div>

              <!--            <InviteFriends></InviteFriends>-->
            </div>

          </b-collapse>

        </div>
      </b-navbar>
    </div>
  </div>

</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import { APPNAME } from '../../../../config/pluginInit'
import langChange from '../../../../Mixins/langchange'
import { getLocal, removeLocalItem } from '@/Utils/authLocalStorage'
import CollapseMenuMobile from '@/components/core/menus/CollapseMenuMobile.vue'
import List from '@/components/core/menus/CollapseMenuMobile.vue'
import SideBarItems from '../../../../FackApi/json/SideBar'
import InviteFriends from '@/views/InviteFriends/index.vue'
import driverFunc from '@/Utils/driver'
import { checkPromotionAuth } from '@/services/promotion'
import router from '@/router'

export default {
  name: 'HeaderStyleMobile',
  mixins: [langChange],
  components: {
    InviteFriends,
    List,
    CollapseMenuMobile

  },
  props: {
    items: Array,
    className: {
      type: String,
      default: 'iq-menu'
    },
    open: {
      type: Boolean,
      default: false
    },
    idName: {
      type: String,
      default: 'sidebar'
    },
    accordianName: {
      type: String,
      default: 'sidebar'
    },
    sidebarGroupTitle: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      btnOffset: 0,
      screenWidth: 0,
      appName: APPNAME,
      SideBarItems: [],
      isMobileNavCollapsed: false,
      user: {
        username: '',
        isIbAccount: ''
      }
    }
  },
  created () {
    window.addEventListener('resize', this.updateWindowDimensions)
  },
  mounted () {
    setInterval(() => {
      this.btnOffset += 1
    }, 50)
    document.addEventListener('click', this.handleClickOutsideMobile)
    console.log(getLocal('user'))
    const user = JSON.parse(getLocal('user'))
    this.user.username = user.firstname + ' ' + user.lastname
    this.user.firstname = user.firstname
    this.user.isIbAccount = user.isIbAccount

    //   ------
    this.SideBarItems = this.SideBarItemsDataHandle(SideBarItems)

    console.log(this.SideBarItems, 'SideBarItemsDataHandle')
    this.removeEducation()
    // .filter(f => f.title !== 'promotion')
    // this.checkPromotionAuthHandle()
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleClickOutsideMobile)
    window.removeEventListener('resize', this.updateWindowDimensions)
  },
  methods: {

    // SideBarItems 增加 open 字段
    SideBarItemsDataHandle(source) {
      const _source = JSON.parse(JSON.stringify(source))

      function dp(data) {
        for (let item of data) {
          item['__open'] = false
          if (item.children && item.children.length > 0) {
            dp(item.children)
          }
        }
      }

      dp(_source)

      return _source
    },

    /**
     * @description 菜单 Education Hub 日本国籍、国家不可见
     */
    removeEducation () {
      const user = JSON.parse(getLocal('user'))

      // console.log(this.SideBarItems)
      if ((user?.nationality || '').toLowerCase() === 'japan' || (user?.country || '').toLowerCase() === 'japan') {
        this.SideBarItems = this.SideBarItems.filter(f => f.title !== 'Education Hub')
        // const target = this.SideBarItems.find(f => f.title === 'Trading Tools')
        // target.children = target.children.filter(f => f.title !== 'Education Hub') || []
      }
    },

    // 是否显示promotion
    async checkPromotionAuthHandle () {
      try {
        const { code, promotionStatus } = await checkPromotionAuth({
          coguid: JSON.parse(getLocal('user')).myguid,
          promotionguid: 'ba3a9cf22ed011ef9bdf024d253502b9'
        })
        if (code == 200) {
          if (!!promotionStatus) {
            this.SideBarItems.push({
              path: '/promotion',
              'title': 'promotion',
              'name': 'sidebar.promotion',
              'is_active': false,
              'link': {
                'name': 'promotion'
              },
              'class_name': '',
              'is_icon_class': true,
              'icon': 'promotion'
            })
            router.addRoute(
              {
                path: '/promotion',
                name: 'promotion',
                component: () => import('@/layouts/Layout2.vue'),
                meta: {
                  auth: true
                },
                children: [
                  {
                    path: '/promotion',
                    name: 'promotion',
                    meta: {
                      auth: true,
                      name: 'promotions'
                    },
                    component: () => import('@/views/SiteNews/promotion.vue')
                  },
                  {
                    path: 'promotionInfo',
                    name: 'promotionInfo',
                    meta: {
                      auth: true,
                      name: 'promotionInfo'
                    },
                    component: () => import('@/views/SiteNews/PromotionInfo.vue')
                  },
                  {
                    path: 'promotionInfo-up600',
                    name: 'promotionInfo-up600',
                    meta: {
                      auth: true,
                      name: 'promotionInfo-up600'
                    },
                    component: () => import('@/views/SiteNews/PromotionInfo-up600.vue')
                  },
                  {
                    path: 'promotionInfo2',
                    name: 'promotionInfo2',
                    meta: {
                      auth: true,
                      name: 'promotionInfo2'
                    },
                    component: () => import('@/views/SiteNews/PromotionInfo2.vue')
                  },
                  {
                    path: 'promotionInfo-up600-verify',
                    name: 'promotionInfo-up600-verify',
                    meta: {
                      auth: true,
                      name: 'promotionInfo-up600-verify'
                    },
                    component: () => import('@/views/SiteNews/AccountVerify.vue')
                  }
                ]
              }
            )
          }
        } else {
        }
      } catch (err) {
        console.log(err)
      }
    },

    goToDeposit () {
      this.$router.push('/accountTransactions/transaction')
    },
    driverFunc,

    updateWindowDimensions () {
      this.screenWidth = window.innerWidth
    },
    logout () {
      this.$vp.add({ message: '[Header] Click logout.' })

      removeLocalItem('user')
      removeLocalItem('access_token')
      removeLocalItem('refresh_token')
      removeLocalItem('cashback2025May')
      this.isMobileNavCollapsed = false
      this.$router.push({ name: 'auth1.sign-in1' })
    },
    ...mapActions({
      langChangeState: 'Setting/setLangAction'
    }),
    handleClickOutsideMobile (event) {
      // Get the collapse element
      const collapse = this.$refs.navCollapseMobile
      const navBar = this.$refs.navBarMobile
      // Check if the clicked element is outside of the collapse element
      if (!collapse.$el.contains(event.target) && !navBar.$el.contains(event.target)) {
        // If outside, close the collapse
        this.isMobileNavCollapsed = false
      }
    },
    toggleNav () {
      console.log('????????????????')
    },
    onNavShown () {

    },
    onNavHidden () {

    },
    closeNav () {
      console.log('hide!!!!!!!!!')
      this.isMobileNavCollapsed = false
    }
  },

  computed: {
    ...mapGetters({
      langsOptions: 'Setting/langOptionState',
      selectedLang: 'Setting/langState',
      stateUsers: 'Setting/usersState'
    })
  }
}
</script>
<style scoped lang="scss">
.ml-auto {
  margin-left: auto !important;
}

.el-icon-info {
  position: absolute;
  top: 16px;
  right: 70px;
  font-size: 40px;
  color: #fff;
}

@media screen and (max-width: 1200px) {
  .navbar-bg {
    background-color: #495eeb;
  }

  .iq-top-navbar .navbar-toggler {
    /*background-image: url("../../../../assets/images/list-icon-mobile.png");*/
    background-size: 100% 100%;

    i {
      color: #495eeb;
    }
  }

  ::v-deep .navbar-collapse {
    background-color: #fff;
    position: absolute;
    top: 75px;
    left: 0;
    width: 100%;
    background: var(--iq-white);
    box-shadow: 0px 5px 12px 0px rgba(55, 73, 72, 0.15);
  }

  .iq-top-navbar .navbar-toggler {
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    padding: 7px 10px;
    background-color: #495eeb;
    border-radius: 8px;

    i {
      color: #fff !important;
    }
  }

  .navbar-light .navbar-toggler {
    border: none;
    font-size: 24px;
  }
}

.deposit-btn {
  height: 36px;
  background-color: #00ff96;
  border-radius: 20px;
  border: none;
  font-size: 15px;
  position: absolute;
  right: 50px;
}

*, *:before, *:after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

$openSans: 'Open Sans', Helvetica, Arial, sans-serif;
$cyan: #00ff96;
$dark: #222;
$borderW: 6px;

.blob-btn {
  $numOfBlobs: 3;
  z-index: 1;
  position: relative;
  text-align: center;
  border: none;
  transition: color 0.5s;
  cursor: pointer;
  padding: 8px 15px;
  color: #000000;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 5px -8px rgba(0, 0, 0, 0.3);
  right: 60px;

  &:before {
    content: "";
    z-index: 1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: 1px solid $cyan;
    border-radius: 15px;
  }

  /*&:after {
    content: "";
    z-index: -2;
    position: absolute;
    left: $borderW*1.5;
    top: $borderW*1.5;
    width: 100%;
    height: 100%;
    border: $borderW solid $dark;
    transition: all 0.3s 0.2s;
  }*/

  &:hover {
    color: $dark;
    background-color: $cyan !important;

    &:after {
      transition: all 0.3s;
      left: 0;
      top: 0;
    }
  }

  &__inner {
    z-index: -1;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  &__blobs {
    position: relative;
    display: block;
    height: 100%;
    filter: url('#goo');
  }

  &__blob {
    position: absolute;
    top: $borderW;
    width: 100% / $numOfBlobs;
    height: 100%;
    background: $cyan;
    border-radius: 100%;
    transform: translate3d(0, 150%, 0) scale(1.7);
    transition: transform 0.45s;
    animation: wave 2s ease infinite alternate;

    @supports (filter: url('#goo')) {
      transform: translate3d(0, 150%, 0) scale(1.4);
    }

    @for $i from 1 through $numOfBlobs {
      &:nth-child(#{$i}) {
        left: ($i - 1) * (120% / $numOfBlobs);
        transition-delay: ($i - 1) * 0.08s;
      }
    }
  }
}

@keyframes wave {
  0% {

  }
  100% {
    transform: translateZ(0) scale(1.7);
    @supports (filter: url('#goo')) {
      transform: translateZ(0) scale(1.1);
    }
  }
}

.deposit-btn {
  position: relative;
  padding: 8px 24px;
  min-width: 80px;
  text-align: center;
  border: 0;
  color: #fff;
  border-radius: 16px;
  font-size: 12px;
  overflow: hidden;
  background-color: rgb(3, 255, 150);
  border: 1px solid #00DF81;

  span {
    position: relative;
    z-index: 10;
    color: #0d1350;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: blur(6px);
    background: repeating-radial-gradient(
        circle at left top,
        //#B7DBFF calc(var(--offset) + 30.00% * 1),
        //#AFB8F5 calc(var(--offset) + 30.00% * 2),
        //#B7DBFF calc(var(--offset) + 30.00% * 3),
        //#03FF96 calc(var(--offset) + 30.00% * 4),
        //#D7FFEE calc(var(--offset) + 30.00% * 5),

        #D7FFEE calc(var(--offset) + 30.00% * 1),
        #ABFFDC calc(var(--offset) + 30.00% * 2),
        #03FF96 calc(var(--offset) + 30.00% * 3),
        //#00DF81 calc(var(--offset) + 30.00% * 4),
        #ABFFDC calc(var(--offset) + 30.00% * 4),
    );
  }

  //&::after {
  //  content: '';
  //  position: absolute;
  //  top: 0;
  //  left: 50%;
  //  transform: translate(-50%, 0);
  //  height: 7%;
  //  width: 90%;
  //  border-radius: 50px 50px 0 0;
  //  background: linear-gradient(to bottom, #fff, transparent);
  //}
}

.other {
  margin: 10px 15px;
  padding: 8px 0;
  border-top: 1px solid #b8c0f1;
  border-bottom: 1px solid #b8c0f1;
  display: flex;
  flex-direction: column;
  gap: 5px;
  color: #495EEB;
  font-size: 16px;

  .invite1 {
    margin: 0;
    width: fit-content;

    ::v-deep .el-button {
      background-color: transparent;
      color: #495EEB;
      margin: 0;
      padding: 0;
      font-size: 16px;
      font-weight: 400;
    }
  }
}

.header-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ::v-deep .btn-secondary {
    background-color: #FFFFFF;
    border: none;

    i, span {
      color: #495EEB !important;
    }
  }

  ::v-deep .show > .btn-secondary.dropdown-toggle {
    color: #495EEB;
    background-color: #FFFFFF !important;
    border-color: #FFFFFF;
  }

  .logOut {
    margin-right: 15px;
    color: #495EEB;
    display: flex;
    align-items: center;
    gap: 5px;
  }
}
</style>
