<template>
  <div
    class="iq-sidebar"
    :style="{
      width: collapse ? '130px' : '270px'
    }"
    style="background-color: #495eeb;"
  >
    <div
      class="iq-sidebar-logo d-flex justify-content-center align-items-center"
      style="padding: 14px 0;position: relative"
    >
      <!--      gap: 20px;-->
      <router-link :to="{ name: 'dashboard.home-2' }">
        <div class="iq-light-logo">
          <img
            :src="collapse ? require('@/assets/images/logo_white.png') : require('@/assets/images/logo-white.png')"
            :style="{
              width: collapse ? '40px' : '',
              height: collapse ? '40px' : '',
            }"
            class="img-fluid"
            alt="logo"
          >
        </div>
      </router-link>
      <div class="menu-collapse" @click="collapseChange">
        <i :class="[collapse ? 'el-icon-arrow-right' : 'el-icon-arrow-left']"></i>
      </div>
    </div>
    <div id="sidebar-scrollbar" style="margin-top: 0">
      <nav class="iq-sidebar-menu">
        <!--        <CollapseMenu key="1" :items="SideBarItems" :open="true" :sidebarGroupTitle="true"/>-->
        <Menu :collapse="collapse" :menu="SideBarItems"/>
      </nav>
<!--      <InviteFriends v-if="!collapse"></InviteFriends>-->
      <!--      <div class="p-3"></div>-->
    </div>
    <!--    <div class="menu-collapse" @click="collapseChange">-->
    <!--      <i :class="[collapse ? 'el-icon-arrow-right' : 'el-icon-arrow-left']"></i>-->
    <!--    </div>-->
  </div>
</template>
<script>
import { APPNAME, core } from '@/config/pluginInit'
import Menu from '@/components/core/menus/Menu.vue'
// import CollapseMenu from '../../menus/CollapseMenu'
import SideBarItems from '../../../../FackApi/json/SideBar'
import InviteFriends from '@/views/InviteFriends'
import { checkPromotionAuth } from '@/services/promotion'
import { getLocal } from '@/Utils/authLocalStorage'
import router from '@/router'

export default {
  name: 'SidebarStyle',
  props: {
    collapse: {
      type: Boolean,
      default: false
    }
  },
  components: {
    // CollapseMenu,
    InviteFriends,
    Menu
  },
  mounted () {
    core.SmoothScrollbar()
    this.SideBarItems = SideBarItems

    this.removeDocument()
    this.removeEducation()
    // this.checkPromotionAuthHandle()
  },

  data () {
    return {
      appName: APPNAME,
      // collapse: false,
      SideBarItems: []
    }
  },

  methods: {

    /**
     * @description 菜单 Education Hub 日本国籍、国家不可见
     */
    removeEducation () {
      const user = JSON.parse(getLocal('user'))

      if ((user?.nationality || '').toLowerCase() === 'japan' || (user?.country || '').toLowerCase() === 'japan') {
        const target = this.SideBarItems.find(f => f.title === 'Trading Tools')
        target.children = target.children.filter(f => f.title !== 'Education Hub') || []
      }
    },

    /**
     * @description isok=1 my document页面不显示
     */
    removeDocument () {
      const user = JSON.parse(getLocal('user'))
      if (user.isok == 1) {
        const target = this.SideBarItems.find(f => f.name === 'sidebar.myAccount')
        target.children = target.children.filter(f => f.name !== 'sidebar.myDocument')
      }
    },

    collapseChange () {
      this.$emit('collapseChange')
    },

    // 是否显示promotion
    async checkPromotionAuthHandle () {
      try {
        const { code, promotionStatus } = await checkPromotionAuth({
          coguid: JSON.parse(getLocal('user')).myguid,
          promotionguid: 'ba3a9cf22ed011ef9bdf024d253502b9'
        })
        if (code == 200) {
          if (!!promotionStatus) {
            this.SideBarItems.push({
              path: '/promotion',
              title: 'promotion',
              name: 'sidebar.promotion',
              is_active: false,
              link: {
                name: 'promotion'
              },
              class_name: '',
              is_icon_class: true,
              icon: 'promotion'
            })
            router.addRoute(
              {
                path: '/promotion',
                name: 'promotion',
                component: () => import('@/layouts/Layout2.vue'),
                meta: {
                  auth: true
                },
                children: [
                  {
                    path: '/promotion',
                    name: 'promotion',
                    meta: {
                      auth: true,
                      name: 'promotions'
                    },
                    component: () => import('@/views/SiteNews/promotion.vue')
                  },
                  {
                    path: 'promotionInfo',
                    name: 'promotionInfo',
                    meta: {
                      auth: true,
                      name: 'promotionInfo'
                    },
                    component: () => import('@/views/SiteNews/PromotionInfo.vue')
                  },
                  {
                    path: 'promotionInfo-up600',
                    name: 'promotionInfo-up600',
                    meta: {
                      auth: true,
                      name: 'promotionInfo-up600'
                    },
                    component: () => import('@/views/SiteNews/PromotionInfo-up600.vue')
                  },
                  {
                    path: 'promotionInfo2',
                    name: 'promotionInfo2',
                    meta: {
                      auth: true,
                      name: 'promotionInfo2'
                    },
                    component: () => import('@/views/SiteNews/PromotionInfo2.vue')
                  },
                  {
                    path: 'promotionInfo-up600-verify',
                    name: 'promotionInfo-up600-verify',
                    meta: {
                      auth: true,
                      name: 'promotionInfo-up600-verify'
                    },
                    component: () => import('@/views/SiteNews/AccountVerify.vue')
                  }
                ]
              }
            )
            this.$forceUpdate()
          }
        } else {

        }
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style scoped lang="scss">
#sidebar-scrollbar {
  height: calc(100vh - 73px) !important;

  .iq-sidebar-menu {
    overflow: hidden auto;
    max-height: calc(100vh - 60px - 73px);
    scrollbar-color: transparent transparent !important;
    scrollbar-width: none;
  }

  .iq-sidebar-menu::-webkit-scrollbar {
    display: none !important;
  }

}

.menu-collapse {
  position: absolute;
  right: 5px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: #fff;
  //border-radius: 50%;
  //background-color: #6878ED;
}
</style>
