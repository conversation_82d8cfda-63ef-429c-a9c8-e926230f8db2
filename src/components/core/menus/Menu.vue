<script>
import MenuItem from '@/components/core/menus/MenuItem.vue'
import SubMenu from '@/components/core/menus/SubMenu.vue'
import { getLocal } from '@/Utils/authLocalStorage'


export default {
  name: 'Menu',

  components: {
    MenuItem,
    SubMenu
  },

  props: {
    collapse: {
      type: Boolean,
      default: false
    },
    menu: {
      type: Array,
      default: () => []
    }
  },

  data () {
    return {
      imgSrc: {
        payment: require('@/assets/images/menu/wallet.png'),
        vault: require('@/assets/images/menu/vault.png'),
        'dashboard-icon': require('@/assets/images/menu/account.png'),
        bank: require('@/assets/images/menu/bank.png'),
        document: require('@/assets/images/menu/document.png'),
        'open-account': require('@/assets/images/menu/account2.png'),
        'account-info': require('@/assets/images/menu/account-info.png'),
        'email-us': require('@/assets/images/menu/email.png'),
        'reset-pwd': require('@/assets/images/menu/password.png'),
        website: require('@/assets/images/menu/home.png'),
        platform: require('@/assets/images/menu/platform.png'),
        news: require('@/assets/images/menu/news.png'),
        promotion: require('@/assets/images/menu/promotion.png'),
        profile: require('@/assets/images/menu/profile.png'),
        tradingTools: '/img/acuityTradingAccount/trading-tools.png',
        analysisIq: '/img/acuityTradingAccount/analysis-iq.png',
        calendar: '/img/acuityTradingAccount/calendar.png',
        downloadsSocialLinks: '/img/acuityTradingAccount/downloads-social-links.png',
        eductionHub: '/img/acuityTradingAccount/education-hub.png',
        acuityTools: '/img/acuityTradingAccount/acuity-tools.png'
      }
    }
  },

  methods: {
    getLocal,
    menuSelect (ev) {
      // console.log(ev)
    }
  }
}
</script>

<template>
  <el-menu
    :collapse="collapse"
    router
    :default-active="$route.path"
    active-text-color="#fff"
    text-color="#fff"
    background-color="#495DEC"
    @select="menuSelect"
  >
    <template v-for="item in menu">
      <SubMenu v-if="item.children && item.children.length" :menu-item="item" />
      <MenuItem :index="item.path" :key="item.name" v-else>
        <img width="18" height="18" :src="imgSrc[item.icon]" v-if="imgSrc[item.icon]">
        <i v-else :class="[item.icon]"></i>
        <span slot="title">{{ $t(item.name) }}</span>
      </MenuItem>
    </template>
  </el-menu>
</template>

<style lang="scss">
.el-menu--collapse {
  .el-submenu {
    .el-submenu__title {
      text-align: center;

      span {
        display: none;
      }
    }
  }

  .el-menu-item [class^=el-icon] {
    transform: translateX(0px) !important;
  }
}

.el-menu {
  //transition: unset;
  overflow: hidden;
  border: 0 !important;

  .fa-desktop,
  .fa-user-plus,
  .fa-comments-dollar {
    font-size: 18px !important;
  }

  &.el-menu--collapse {
    width: 130px;
  }

  .el-menu-item {
    //transition: unset;

    .el-tooltip {
      text-align: center;
    }

    &:hover {
      background: #6879ee !important;
    }

    i {
      display: inline-block;
      margin: 0;
      width: 18px;
      height: 18px;
      text-align: center;
      line-height: 18px;
      font-size: 18px;
      color: #fff !important;
    }

    span {
      margin-left: 10px;
    }

    &.is-active {
      background: #6879ee !important;
    }
  }

  //.el-menu-item .el-icon-timer {
  //transform: translateX(-2px);
  //font-size: 24px !important;
  //}

  .el-submenu {
    //transition: unset;

    .el-tooltip {
      text-align: center;
    }

    .el-submenu__title {

      &:hover {
        background: #6879ee !important;
      }
    }

    i:nth-child(1) {
      margin: 0;
      width: 18px;
      height: 18px;
      text-align: center;
      line-height: 18px;
      font-size: 18px !important;
      color: #fff !important;
    }

    .el-menu-item .el-icon-timer {
      transform: translateX(-2px);
      width: 22px;
      height: 22px;
      font-size: 22px !important;
    }

    .el-menu-item .el-icon-timer + span {
      display: inline-block;
      transform: translateX(-4px);
    }

    span {
      margin-left: 10px;
    }

    .el-submenu__icon-arrow {
      right: 11px;
      font-size: 14px;
      color: #fff !important;
    }
  }
}
</style>
