<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .captcha-demo {
            border: 2px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            min-height: 100px;
        }
    </style>
    <!-- 加载验证码脚本 -->
    <script src="https://recaptcha.net/recaptcha/api.js?render=explicit" async defer></script>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js?compat=recaptcha" async defer></script>
</head>
<body>
    <h1>🔧 验证码修复测试</h1>
    
    <div class="test-section">
        <h3>✅ 修复内容</h3>
        <div class="success">
            <strong>问题:</strong> type=cloudflare 时显示 Google 验证码界面<br>
            <strong>原因:</strong> 印尼域名的 CloudFlare Site Key 为空，导致回退到错误的配置<br>
            <strong>修复:</strong> 添加了 Site Key 回退逻辑，确保始终有有效的 CloudFlare Site Key
        </div>
    </div>

    <div class="test-section">
        <h3>🔍 修复前后对比</h3>
        <div class="code">修复前:
// 如果印尼域名的 Site Key 为空，会返回 undefined
return location.origin.includes('myfx.group')
  ? process.env.VUE_APP_INDONESIA_CLOUD_FLARE_SITE_KEY  // 可能为空
  : (process.env.VUE_APP_CLOUD_FLARE_SITE_KEY || '0x4AAAAAABgkoiuUbu_9cxnv')

修复后:
// 添加了多层回退，确保始终有有效的 Site Key
return location.origin.includes('myfx.group')
  ? (process.env.VUE_APP_INDONESIA_CLOUD_FLARE_SITE_KEY || 
     process.env.VUE_APP_CLOUD_FLARE_SITE_KEY || 
     '0x4AAAAAABgkoiuUbu_9cxnv')
  : (process.env.VUE_APP_CLOUD_FLARE_SITE_KEY || '0x4AAAAAABgkoiuUbu_9cxnv')</div>
    </div>

    <div class="test-section">
        <h3>🧪 CloudFlare Turnstile 测试</h3>
        <div class="info">
            使用修复后的 Site Key 逻辑测试 CloudFlare Turnstile
        </div>
        
        <button onclick="testCloudflareFixed()">测试修复后的 CloudFlare</button>
        <div id="cloudflare-fixed-demo" class="captcha-demo"></div>
        
        <div id="test-result"></div>
    </div>

    <div class="test-section">
        <h3>🔍 调试信息</h3>
        <button onclick="showDebugInfo()">显示调试信息</button>
        <div id="debug-info"></div>
    </div>

    <div class="test-section">
        <h3>📋 测试步骤</h3>
        <div class="info">
            <strong>现在请按以下步骤测试:</strong><br><br>
            1. 访问修改密码页面<br>
            2. 打开浏览器开发者工具 (F12)<br>
            3. 查看 Console 中的验证码调试信息<br>
            4. 确认显示的是 CloudFlare Turnstile 而不是 Google reCAPTCHA<br>
            5. 检查 Site Key 是否为 0x4AAAAAABgkoiuUbu_9cxnv
        </div>
        
        <button onclick="openResetPassword()">打开修改密码页面</button>
    </div>

    <div class="test-section">
        <h3>🎯 预期结果</h3>
        <div class="success">
            <strong>修复后的预期行为:</strong><br>
            • type=cloudflare 时显示 CloudFlare Turnstile 验证码<br>
            • 验证码界面应该是 CloudFlare 的样式，不是 Google 的<br>
            • Console 中显示正确的 Site Key: 0x4AAAAAABgkoiuUbu_9cxnv<br>
            • 验证码功能正常工作
        </div>
    </div>

    <script>
        // 开发环境的 CloudFlare Site Key
        const CLOUDFLARE_SITE_KEY = '0x4AAAAAABgkoiuUbu_9cxnv';

        function testCloudflareFixed() {
            const container = document.getElementById('cloudflare-fixed-demo');
            const result = document.getElementById('test-result');
            
            container.innerHTML = '<p>正在使用修复后的逻辑渲染 CloudFlare Turnstile...</p>';
            result.innerHTML = '<div class="status info">测试中...</div>';
            
            setTimeout(() => {
                if (window.grecaptcha && window.grecaptcha.render) {
                    try {
                        console.log('🔍 测试验证码渲染:');
                        console.log('  - 使用 Site Key:', CLOUDFLARE_SITE_KEY);
                        console.log('  - 验证码类型: CloudFlare Turnstile');
                        
                        window.grecaptcha.render(container, {
                            sitekey: CLOUDFLARE_SITE_KEY,
                            callback: (token) => {
                                console.log('✅ CloudFlare Turnstile token 获取成功:', token);
                                result.innerHTML = '<div class="status success">✅ CloudFlare Turnstile 渲染成功！</div>';
                            },
                            'error-callback': (error) => {
                                console.error('❌ CloudFlare Turnstile 错误:', error);
                                result.innerHTML = '<div class="status error">❌ CloudFlare Turnstile 渲染失败: ' + error + '</div>';
                            }
                        });
                        
                        // 检查渲染结果
                        setTimeout(() => {
                            const iframe = container.querySelector('iframe');
                            if (iframe) {
                                const src = iframe.src;
                                if (src.includes('challenges.cloudflare.com')) {
                                    result.innerHTML = '<div class="status success">✅ 确认渲染的是 CloudFlare Turnstile！</div>';
                                } else if (src.includes('recaptcha')) {
                                    result.innerHTML = '<div class="status error">❌ 仍然渲染的是 Google reCAPTCHA</div>';
                                } else {
                                    result.innerHTML = '<div class="status warning">⚠️ 无法确定验证码类型</div>';
                                }
                            }
                        }, 2000);
                        
                    } catch (error) {
                        console.error('渲染失败:', error);
                        container.innerHTML = '<p class="error">渲染失败: ' + error.message + '</p>';
                        result.innerHTML = '<div class="status error">❌ 渲染失败: ' + error.message + '</div>';
                    }
                } else {
                    container.innerHTML = '<p class="error">grecaptcha 未加载</p>';
                    result.innerHTML = '<div class="status error">❌ grecaptcha 脚本未加载</div>';
                }
            }, 1000);
        }

        function showDebugInfo() {
            const debugInfo = document.getElementById('debug-info');
            
            let html = '<div class="status info"><h4>🔍 当前环境调试信息:</h4>';
            html += `<p><strong>当前域名:</strong> ${location.origin}</p>`;
            html += `<p><strong>是否印尼域名:</strong> ${location.origin.includes('myfx.group') ? '是' : '否'}</p>`;
            html += `<p><strong>应该使用的 Site Key:</strong> ${CLOUDFLARE_SITE_KEY}</p>`;
            html += `<p><strong>grecaptcha 加载状态:</strong> ${typeof window.grecaptcha !== 'undefined' ? '已加载' : '未加载'}</p>`;
            
            // 检查脚本
            const scripts = document.querySelectorAll('script[src*="recaptcha"], script[src*="turnstile"]');
            html += `<p><strong>验证码脚本数量:</strong> ${scripts.length}</p>`;
            scripts.forEach((script, index) => {
                html += `<p><strong>脚本 ${index + 1}:</strong> ${script.src}</p>`;
            });
            
            html += '</div>';
            debugInfo.innerHTML = html;
        }

        function openResetPassword() {
            // 尝试打开修改密码页面
            const urls = [
                'http://localhost:1026/#/account-settings/reset-co-password',
                'http://localhost:1026/account-settings/reset-co-password',
                'http://localhost:1026'
            ];
            
            urls.forEach((url, index) => {
                setTimeout(() => {
                    window.open(url, '_blank');
                }, index * 1000);
            });
        }

        // 页面加载时自动显示调试信息
        window.onload = function() {
            console.log('🔧 验证码修复测试页面已加载');
            setTimeout(() => {
                showDebugInfo();
            }, 2000);
        };
    </script>
</body>
</html>
