// 验证码功能测试脚本
// 使用 Node.js 和 puppeteer 进行自动化测试

const puppeteer = require('puppeteer');

async function testCaptchaFunctionality() {
    console.log('🚀 开始验证码功能测试...\n');
    
    let browser;
    try {
        // 启动浏览器
        browser = await puppeteer.launch({
            headless: false, // 显示浏览器窗口以便观察
            defaultViewport: { width: 1280, height: 720 }
        });
        
        const page = await browser.newPage();
        
        // 监听控制台消息
        page.on('console', msg => {
            console.log(`🖥️  浏览器控制台: ${msg.text()}`);
        });
        
        // 监听网络请求
        page.on('response', response => {
            if (response.url().includes('/captcha')) {
                console.log(`📡 验证码API请求: ${response.url()}`);
                console.log(`📊 响应状态: ${response.status()}`);
            }
        });
        
        console.log('1️⃣ 测试登录页面验证码...');
        await testLoginPage(page);
        
        console.log('\n2️⃣ 测试忘记密码页面验证码...');
        await testForgotPasswordPage(page);
        
        console.log('\n✅ 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

async function testLoginPage(page) {
    try {
        // 访问登录页面
        console.log('   📍 访问登录页面: http://localhost:1026/auth/sign-in1');
        await page.goto('http://localhost:1026/auth/sign-in1', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        // 检查页面标题
        const title = await page.title();
        console.log(`   📄 页面标题: ${title}`);
        
        // 检查是否有验证码容器
        const captchaContainer = await page.$('#google-captcha');
        if (captchaContainer) {
            console.log('   ✅ 找到验证码容器');
            
            // 检查验证码是否可见
            const isVisible = await page.evaluate(() => {
                const container = document.querySelector('#google-captcha');
                return container && container.offsetParent !== null;
            });
            
            if (isVisible) {
                console.log('   ✅ 验证码容器可见');
            } else {
                console.log('   ⚠️  验证码容器不可见');
            }
        } else {
            console.log('   ❌ 未找到验证码容器');
        }
        
        // 检查环境变量和验证码类型
        const captchaInfo = await page.evaluate(() => {
            return {
                nodeEnv: process.env.NODE_ENV || 'unknown',
                origin: window.location.origin,
                captchaVisible: !!document.querySelector('#google-captcha'),
                grecaptchaLoaded: typeof window.grecaptcha !== 'undefined'
            };
        });
        
        console.log('   📊 验证码信息:');
        console.log(`      - 环境: ${captchaInfo.nodeEnv}`);
        console.log(`      - 域名: ${captchaInfo.origin}`);
        console.log(`      - 验证码可见: ${captchaInfo.captchaVisible}`);
        console.log(`      - grecaptcha已加载: ${captchaInfo.grecaptchaLoaded}`);
        
        // 检查是否有错误
        const errors = await page.evaluate(() => {
            const errorElements = document.querySelectorAll('.error, .alert-danger, .is-invalid');
            return Array.from(errorElements).map(el => el.textContent.trim());
        });
        
        if (errors.length > 0) {
            console.log('   ⚠️  页面错误:', errors);
        } else {
            console.log('   ✅ 页面无明显错误');
        }
        
    } catch (error) {
        console.error('   ❌ 登录页面测试失败:', error.message);
    }
}

async function testForgotPasswordPage(page) {
    try {
        // 访问忘记密码页面
        console.log('   📍 访问忘记密码页面: http://localhost:1026/auth/recover-password1');
        await page.goto('http://localhost:1026/auth/recover-password1', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        // 检查页面标题
        const title = await page.title();
        console.log(`   📄 页面标题: ${title}`);
        
        // 检查是否有验证码容器
        const captchaContainer = await page.$('#google-captcha');
        if (captchaContainer) {
            console.log('   ✅ 找到验证码容器');
        } else {
            console.log('   ❌ 未找到验证码容器');
        }
        
        // 检查验证码类型
        const captchaInfo = await page.evaluate(() => {
            const container = document.querySelector('#google-captcha');
            return {
                containerExists: !!container,
                containerVisible: container && container.offsetParent !== null,
                grecaptchaLoaded: typeof window.grecaptcha !== 'undefined'
            };
        });
        
        console.log('   📊 忘记密码页面验证码信息:');
        console.log(`      - 容器存在: ${captchaInfo.containerExists}`);
        console.log(`      - 容器可见: ${captchaInfo.containerVisible}`);
        console.log(`      - grecaptcha已加载: ${captchaInfo.grecaptchaLoaded}`);
        
    } catch (error) {
        console.error('   ❌ 忘记密码页面测试失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testCaptchaFunctionality();
}

module.exports = { testCaptchaFunctionality };
