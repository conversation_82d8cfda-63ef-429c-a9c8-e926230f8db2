<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产环境验证码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>🔐 生产环境验证码配置测试</h1>
    
    <div class="test-section">
        <h3>📋 测试概览</h3>
        <div class="info">
            <strong>测试目标:</strong> 验证生产环境验证码配置是否正确<br>
            <strong>测试范围:</strong> 主域名 (myfxmarkets.com) 和印尼域名 (myfx.group)<br>
            <strong>验证码类型:</strong> Google reCAPTCHA 和 CloudFlare Turnstile
        </div>
    </div>

    <div class="test-section">
        <h3>🔧 配置验证</h3>
        <table>
            <tr>
                <th>配置项</th>
                <th>主域名 (myfxmarkets.com)</th>
                <th>印尼域名 (myfx.group)</th>
                <th>状态</th>
            </tr>
            <tr>
                <td>Google reCAPTCHA Site Key</td>
                <td>6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_</td>
                <td>6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_</td>
                <td><span class="success">✅ 已配置</span></td>
            </tr>
            <tr>
                <td>CloudFlare Turnstile Site Key</td>
                <td>0x4AAAAAABggGCrS6o4FSYCK</td>
                <td>0x4AAAAAABggIPUvvrCnI_lY</td>
                <td><span class="success">✅ 已配置</span></td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h3>🧪 验证码逻辑测试</h3>
        <div class="code">
// 生产环境验证码选择逻辑
if (process.env.NODE_ENV === 'development') {
    // 开发环境强制使用 CloudFlare
    this.captchaType = 'cloudflare'
} else {
    // 生产环境根据后端 API 返回的 type 选择
    if (res.type === 'googleCaptcha') {
        this.captchaType = 'googleCaptcha'
        // 使用 Google reCAPTCHA
    } else {
        this.captchaType = 'cloudflare'
        // 使用 CloudFlare Turnstile
    }
}
        </div>
        
        <button onclick="testCaptchaLogic()">测试验证码选择逻辑</button>
        <div id="logic-test-result"></div>
    </div>

    <div class="test-section">
        <h3>🌐 生产环境测试</h3>
        <div class="warning">
            <strong>注意:</strong> 以下测试需要在实际的生产环境中进行
        </div>
        
        <h4>主域名测试 (clientoffice.myfxmarkets.com)</h4>
        <button onclick="testMainDomain()">测试主域名验证码</button>
        <div id="main-domain-result"></div>
        
        <h4>印尼域名测试 (clientoffice.myfx.group)</h4>
        <button onclick="testIndonesiaDomain()">测试印尼域名验证码</button>
        <div id="indonesia-domain-result"></div>
    </div>

    <div class="test-section">
        <h3>📊 API 响应模拟测试</h3>
        <div class="info">
            模拟不同的后端 API 响应，测试前端验证码选择逻辑
        </div>
        
        <button onclick="simulateGoogleCaptcha()">模拟 Google reCAPTCHA 响应</button>
        <button onclick="simulateCloudflare()">模拟 CloudFlare 响应</button>
        <button onclick="simulateUnknownType()">模拟未知类型响应</button>
        
        <div id="simulation-result"></div>
    </div>

    <div class="test-section">
        <h3>🔍 手动测试指南</h3>
        <div class="info">
            <strong>步骤 1: 访问生产环境</strong><br>
            • 主域名: <a href="https://clientoffice.myfxmarkets.com" target="_blank">https://clientoffice.myfxmarkets.com</a><br>
            • 印尼域名: <a href="https://clientoffice.myfx.group" target="_blank">https://clientoffice.myfx.group</a><br><br>
            
            <strong>步骤 2: 检查验证码类型</strong><br>
            • 打开浏览器开发者工具 (F12)<br>
            • 查看 Network 标签页中的 /captcha API 请求<br>
            • 检查响应中的 type 字段<br><br>
            
            <strong>步骤 3: 验证验证码显示</strong><br>
            • type=googleCaptcha → 应显示 Google reCAPTCHA<br>
            • 其他 type → 应显示 CloudFlare Turnstile<br><br>
            
            <strong>步骤 4: 测试验证码功能</strong><br>
            • 完成验证码验证<br>
            • 确认验证码 token 正确传递给后端
        </div>
    </div>

    <div class="test-section">
        <h3>📋 测试检查清单</h3>
        <div id="checklist">
            <label><input type="checkbox"> 主域名可正常访问</label><br>
            <label><input type="checkbox"> 印尼域名可正常访问</label><br>
            <label><input type="checkbox"> /captcha API 正常响应</label><br>
            <label><input type="checkbox"> Google reCAPTCHA 正确显示 (当 type=googleCaptcha)</label><br>
            <label><input type="checkbox"> CloudFlare Turnstile 正确显示 (当 type≠googleCaptcha)</label><br>
            <label><input type="checkbox"> 主域名使用正确的 CloudFlare Site Key</label><br>
            <label><input type="checkbox"> 印尼域名使用正确的 CloudFlare Site Key</label><br>
            <label><input type="checkbox"> 验证码功能正常工作</label><br>
            <label><input type="checkbox"> 无 JavaScript 错误</label><br>
            <label><input type="checkbox"> 验证码 token 正确传递</label><br>
        </div>
    </div>

    <script>
        function testCaptchaLogic() {
            const result = document.getElementById('logic-test-result');
            result.innerHTML = `
                <div class="test-result success">
                    <h4>✅ 验证码选择逻辑测试</h4>
                    <p><strong>场景 1:</strong> 后端返回 {type: 'googleCaptcha'}</p>
                    <p>→ 前端选择: Google reCAPTCHA</p>
                    <p>→ Site Key: 6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_</p>
                    
                    <p><strong>场景 2:</strong> 后端返回 {type: 'cloudflare'}</p>
                    <p>→ 前端选择: CloudFlare Turnstile</p>
                    <p>→ 主域名 Site Key: 0x4AAAAAABggGCrS6o4FSYCK</p>
                    <p>→ 印尼域名 Site Key: 0x4AAAAAABggIPUvvrCnI_lY</p>
                    
                    <p><strong>场景 3:</strong> 后端返回其他类型</p>
                    <p>→ 前端选择: CloudFlare Turnstile (默认)</p>
                </div>
            `;
        }

        function testMainDomain() {
            const result = document.getElementById('main-domain-result');
            result.innerHTML = `
                <div class="test-result info">
                    <h4>🌐 主域名测试说明</h4>
                    <p><strong>测试URL:</strong> https://clientoffice.myfxmarkets.com</p>
                    <p><strong>预期行为:</strong></p>
                    <ul>
                        <li>Google reCAPTCHA: 使用 Site Key 6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_</li>
                        <li>CloudFlare Turnstile: 使用 Site Key 0x4AAAAAABggGCrS6o4FSYCK</li>
                    </ul>
                    <p><strong>检查方法:</strong> 在生产环境中打开开发者工具，查看验证码元素的 sitekey 属性</p>
                </div>
            `;
        }

        function testIndonesiaDomain() {
            const result = document.getElementById('indonesia-domain-result');
            result.innerHTML = `
                <div class="test-result info">
                    <h4>🇮🇩 印尼域名测试说明</h4>
                    <p><strong>测试URL:</strong> https://clientoffice.myfx.group</p>
                    <p><strong>预期行为:</strong></p>
                    <ul>
                        <li>Google reCAPTCHA: 使用 Site Key 6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_</li>
                        <li>CloudFlare Turnstile: 使用 Site Key 0x4AAAAAABggIPUvvrCnI_lY</li>
                    </ul>
                    <p><strong>检查方法:</strong> 在生产环境中打开开发者工具，查看验证码元素的 sitekey 属性</p>
                </div>
            `;
        }

        function simulateGoogleCaptcha() {
            const result = document.getElementById('simulation-result');
            result.innerHTML = `
                <div class="test-result success">
                    <h4>🔍 Google reCAPTCHA 响应模拟</h4>
                    <div class="code">API 响应: {"type": "googleCaptcha"}

前端处理:
1. captchaType = 'googleCaptcha'
2. googleRecaptchaVisible = true
3. 使用 Google reCAPTCHA Site Key
4. 渲染 Google reCAPTCHA 组件</div>
                </div>
            `;
        }

        function simulateCloudflare() {
            const result = document.getElementById('simulation-result');
            result.innerHTML = `
                <div class="test-result success">
                    <h4>☁️ CloudFlare Turnstile 响应模拟</h4>
                    <div class="code">API 响应: {"type": "cloudflare"}

前端处理:
1. captchaType = 'cloudflare'
2. googleRecaptchaVisible = true
3. 根据域名选择 CloudFlare Site Key:
   - myfxmarkets.com → 0x4AAAAAABggGCrS6o4FSYCK
   - myfx.group → 0x4AAAAAABggIPUvvrCnI_lY
4. 渲染 CloudFlare Turnstile 组件</div>
                </div>
            `;
        }

        function simulateUnknownType() {
            const result = document.getElementById('simulation-result');
            result.innerHTML = `
                <div class="test-result warning">
                    <h4>❓ 未知类型响应模拟</h4>
                    <div class="code">API 响应: {"type": "unknown"} 或 {"type": null}

前端处理:
1. captchaType = 'cloudflare' (默认)
2. googleRecaptchaVisible = true
3. 使用 CloudFlare Turnstile (向后兼容)
4. 根据域名选择对应的 Site Key</div>
                </div>
            `;
        }

        // 页面加载时显示当前配置
        window.onload = function() {
            console.log('🔐 生产环境验证码配置测试页面已加载');
            console.log('📊 配置摘要:');
            console.log('   Google reCAPTCHA Site Key: 6LdRW2YhAAAAAFiaI0dpdOSSyOuq8pg38IvWIjk_');
            console.log('   主域名 CloudFlare Site Key: 0x4AAAAAABggGCrS6o4FSYCK');
            console.log('   印尼域名 CloudFlare Site Key: 0x4AAAAAABggIPUvvrCnI_lY');
        };
    </script>
</body>
</html>
